<page-container show="{{visible}}" wx:if="{{refundData}}">
  <view class="content">
    <view class="pop-title">
      <text>申请退款</text>
      <image bindtap="closePopClick" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/close_icon.png" mode="" />
    </view>
    <view class="refund-info">
      <!-- 数量 -->
      <view class="between-item">
        <text class="title">退票数量</text>
        <view class="price-list">
          <view 
            class="price-item" 
            wx:for="{{refundData.personList}}"
            wx:key="index">
            <text class="txt blod">{{item.type}}</text>
            <text class="txt blod">￥{{item.price}}</text>
            <text class="txt blod">×{{item.num}}</text>
          </view>
        </view>
      </view>
      <!-- 金额 -->
      <view class="between-item">
        <text class="title">支付金额</text>
        <text class="txt blod">￥{{refundData.totalAmount}}</text>
      </view>
      <!-- 退款政策 -->
      <view class="activity-refund">
        <view class="title-box">
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/refund_icon.png" mode="" />
          <text class="txt">退款政策</text>
          <text class="desc">活动费用将在6月6日0点后支付给活动发起人</text>
        </view>
        <view class="refund-table">
          <!-- 表头 -->
          <view class="table-row head">
            <view class="table-cell">申请退款时间</view>
            <view class="table-cell">退款比例</view>
            <view class="table-cell">退款金额</view>
          </view>

          <!-- 表格内容 - 动态渲染 -->
          <view class="table-row" wx:for="{{refundList}}" wx:key="index">
            <view class="table-cell">{{item.time}}</view>
            <view class="table-cell">{{item.detail}}</view>
            <view class="table-cell">￥{{item.price}}</view>
          </view>
        </view>
        <view class="bottom-desc">该活动在报名截止前人数不满16人，将自动取消，钱款将自动退还到您的支付账户支付账户</view>
      </view>
    </view>

    <view class="bottom-bar-box" bindtap="sumbtm">
      <view>确认退款（￥{{refundData.refundAmount}}）</view>
    </view>
  </view>
</page-container>