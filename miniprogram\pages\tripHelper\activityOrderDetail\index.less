.main{
  background-color: #F7F7F7;
  height: 100vh;
  width: 100vw;
  overflow-y: auto;
}
.order-detail{
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.order-statu{
  padding: 32rpx 24rpx 24rpx 36rpx;
  background: #FFFFFF;
  .title-box{
    display: flex;
    align-items: flex-start;
    .icon{
      width: 64rpx;
      height: 64rpx;
      margin-right: 16rpx;
    }
    .rotating {
      animation: rotate 2s linear infinite;
    }
    
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    .txt-box{
      display: flex;
      flex-direction: column;
    }
    .title{
      font-weight: 500;
      font-size: 48rpx;
      color: #33333E;
    }
    .desc{
      font-size: 24rpx;
      color: #33333E;
    }
  }
  .btm-box{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    .btm{
      height: 72rpx;
      min-width: 178rpx;
      border-radius: 120rpx 120rpx 120rpx 120rpx;
      line-height: 72rpx;
      font-size: 28rpx;
      text-align: center;
    }
    .success{
      background: #0198FF;
      color: #FFFFFF;
    }
  }
}
.order-info{
  margin: 24rpx 24rpx 0 24rpx;
  padding: 32rpx 24rpx 8rpx 24rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  .info-item{
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    text{
      font-size: 28rpx;
      color: #66666E;
    }
    text:last-child{
      max-width: 492rpx;
      word-wrap: break-word;
      word-break: break-all; 
      display: inline-block; 
      overflow-wrap: break-word;
    }
  }
}
.activity-info{
  margin: 24rpx 24rpx 0 24rpx;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  .pic{
    width: 144rpx;
    height: 144rpx;
    border-radius: 24rpx;
    margin-right: 24rpx;
  }
  .cotent{
    flex: 1;
  }
  .title{
    font-size: 36rpx;
    color: #33333E;
    flex: 1;
  }
  .address-box{
    display: flex;
    align-items: center;
    image{
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    text{
      font-size: 24rpx;
      color: #33333E;
    }
  }
  
}
.ewm-info{
  margin: 2rpx 24rpx 0 24rpx;
  padding: 48rpx 0;
  background: #FFFFFF;
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  image{
    width: 320rpx;
    height: 320rpx;
  }
  .txt-box{
    display: flex;
    align-items: center;
    margin-top: 24rpx;
    view{
      width: 22rpx;
      height: 2rpx;
      background: #CCCCCE;
    }
    text{
      font-size: 24rpx;
      color: #99999E;
      margin: 0 16rpx;
    }
  }
}
.comment-list{
  margin: 24rpx 24rpx 0 24rpx;
  padding: 32rpx 24rpx;
  background: #FFFFFF;
  border-radius: 32rpx;

  .title{
    font-weight: 500;
    font-size: 36rpx;
    color: #33333E;
  }
  .comment-content{
    margin-top: 32rpx;
  }
  .desc-txt{
    font-size: 28rpx;
    color: #33333E;
  }
}