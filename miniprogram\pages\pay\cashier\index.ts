import { getPaymentParams, getPaymentInfo } from '../../../api/pay';
import { StorageKeyEnum } from '../../../enum/index';
import { getOpenId } from '../../../utils/userInfo';
import { connectMobx, Store } from '../../../store/index';

const app = getApp<IAppOption>();

type PayOptions = Omit<WechatMiniprogram.RequestPaymentOption, 'success' | 'fail' | 'complete'>;
const initData = {
  payLoading: false,
  loading: false,
  orderNo: '',
  paymentInfo: {
    totalActualPrice: '',
    hotelName: '',
    companyName: '',
  },
};
type Data = typeof initData & Pick<Store, 'geoInfo' | 'cityInfo'>
Page<Data, WechatMiniprogram.Page.CustomOption & {}, {}>({

  /**
* 页面的初始数据
*/
  data: initData as Data,

  behaviors: [
    connectMobx({
      fields: ['geoInfo', 'cityInfo'],
      actions: [],
    }),
  ],

  /**
* 生命周期函数--监听页面加载
*/
  onLoad(options: { orderNo: string; token: string }) {
    const { orderNo, token } = options;
    if (!orderNo || !token) {
      wx.displayToast({
        title: '参数异常',
      });
      return;
    }
    this.setData({
      orderNo,
    });

    this.queryPaymentInfo(orderNo);

    wx.setStorageSync(StorageKeyEnum.Token, token);
    app.init().then(res => {
      wx.setStorageSync(StorageKeyEnum.UserInfo, res);
    });
  },
  queryPaymentInfo(orderNo: string) {
    this.setData({
      loading: true,
    });
    getPaymentInfo(orderNo).then(res => {
      if (res.isSuccess) {
        this.setData({
          paymentInfo: res.data,
        });
      }
    }).finally(() => {
      this.setData({
        loading: false,
      });
    });
  },

  async handlePayment() {
    const { orderNo, paymentInfo: { totalActualPrice, hotelName } } = this.data;
    this.setData({
      payLoading: true,
    });
    let { openid } = await app.init();
    if (!openid) {
      // 防止初始化换取openid，此处做一次兜底
      const { openid: _openid } = await getOpenId();
      openid = _openid;
    }
    const res = await getPaymentParams({
      openid,
      payType: 'WECHAT',
      orderNo,
      orderPayMoney: Number(totalActualPrice),
      goodsName: hotelName,
    });
    if (res.isSuccess && res.data?.recordList?.length) {
      const { payload = '{}' } = res.data.recordList[0];
      try {
        const payloadJson = JSON.parse(payload);
        if (payloadJson?.metadata) {
          const { success } = await this.wxpay(payloadJson.metadata as PayOptions);
          if (success) {
            wx.redirectTo({
              url: `/pages/pay/result?orderNo=${orderNo}`,
            });
          }
          this.setData({
            payLoading: false,
          });

        } else {
          throw new Error('支付参数有误');
        }
      } catch (error) {
        console.error(error);
        this.setData({
          payLoading: false,
        });
      }
    } else {
      this.setData({
        payLoading: false,
      });
    }
  },
  wxpay(params: PayOptions): Promise<{ success: boolean, code: string, message: string }> {
    return new Promise(resolve => {
      wx.requestPayment({
        ...params,
        success() {
          resolve({ success: true, code: '0000', message: '支付成功' });
        },
        fail(err) {
          console.log('微信支付失败', err);
          resolve({ success: false, code: 'ERROR', message: '微信支付失败，请重新再试' });
        },
      });
    });
  },
});