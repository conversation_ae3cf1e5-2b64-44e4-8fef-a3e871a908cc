<!-- pages/tripHelper/chooseRoute/components/Days/Days.wxml -->
<view class="days-container">    
    <!-- picker-view选择器 -->
    <picker-view 
        class="days-picker" 
        value="{{pickerValue}}" 
        bindchange="onPickerChange"
        indicator-style="height: 50px;"
        style="height: 500rpx"
    >
        <picker-view-column>
            <view 
                class="days-item {{index === pickerValue[0] ? 'selected' : ''}}" 
                wx:for="{{daysArray}}" 
                wx:key="index"
            >
                {{item}}天
            </view>
        </picker-view-column>
    </picker-view>
    
    <!-- 底部按钮区域 -->
    <view class="days-bottom">
        <view class="btn-cancel" bind:tap="onCancel">清空</view>
        <view class="btn-confirm" bind:tap="onConfirm">确定</view>
    </view>
</view>