import dayjs from 'dayjs';

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
    },
    value: {
      type: Array,
      value: [],
    },
    mode: {
      type: String,
      value: 'date' as 'date' | 'hour' | 'minute' | 'second'
    },
    format: {
      type: String,
      value: 'YYYY-MM-DD'
    },
    showWeek: {
      type: Boolean,
      value: true
    },
    min: {
      type: String,
      value: ''
    },
    max: {
      type: String,
      value: ''
    }
  },
  data: {
    step: 'start', // 'start' or 'end'
    startDate: '',
    endDate: '',
    minDate: '1970-01-01',
    maxDate: '2099-12-31'
  },
  observers: {
    visible(val: boolean) {
      if (val) {
        const [startDate = '', endDate = ''] = this.data.value || [];
        this.setData({
          startDate: startDate || dayjs().format(this.data.mode === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'),
          endDate,
          step: 'start',
          ...(this.data.min ? {
            minDate: this.data.min
          } : {}),
          ...(this.data.max ? {
            maxDate: this.data.max
          } : {})
        });
      }
    }
  },
  methods: {
    onDatePickChange(e: { detail: { value: string } }) {
      const date = e.detail.value;
      if (this.data.step === 'start') {
        this.setData({
          startDate: date
        });
      } else {
        this.setData({ endDate: date });
      }
    },
    onLeftClick() {
      if (this.data.step === 'start') {
        this.triggerEvent('visible-change', { visible: false });
      } else {
        this.setData({ step: 'start' });
      }
    },
    onRightClick() {
      if (this.data.step === 'start') {
        this.setData({
          step: 'end',
          endDate: this.data.endDate && this.data.endDate >= this.data.startDate ? this.data.endDate : this.data.startDate
        });
      } else {
        // 结束时间不能早于开始时间
        const { startDate, endDate } = this.data;
        if (!endDate) {
          wx.displayToast({ title: '请选择结束时间', icon: 'none' });
          return;
        }
        if (startDate && endDate < startDate) {
          wx.displayToast({
            title: '结束时间不能早于开始时间',
            icon: 'none',
            duration: 1500
          });
          this.setData({ endDate: '' });
          return;
        }
        this.triggerEvent('change', {
          start: startDate,
          end: endDate
        });
        this.triggerEvent('visible-change', { visible: false });
      }
    },
    handleVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.triggerEvent('visible-change', e.detail);
    }
  }
});