<view class="refresh-load-list {{customClass}}">
  <!-- 下拉刷新区域 - 只在下拉或刷新时显示 -->
  <view
    class="refresh-header"
    style="top: {{topOffset}}; transform: translateY({{pullDistance > 0 || refreshing ? pullDistance - 120 : -120}}rpx);"
    wx:if="{{enableRefresh && (pullDistance > 0 || refreshing)}}"
  >
    <view class="refresh-content">
      <!-- 下拉状态 -->
      <view class="refresh-status" wx:if="{{!refreshing}}">
        <image
          class="refresh-icon {{pullDistance >= refreshThreshold ? 'rotate' : ''}}"
          src="data:image/svg+xml;base64,{{pullDistance >= refreshThreshold ? refreshIconBase64 : arrowDownIconBase64}}"
          mode="aspectFit"
          style="width: {{loadingSize}}; height: {{loadingSize}};"
        />
        <text class="refresh-text">{{pullDistance >= refreshThreshold ? '松开刷新' : '下拉刷新'}}</text>
      </view>

      <!-- 刷新中状态 -->
      <view class="refresh-status" wx:else>
        <t-loading theme="circular" size="{{loadingSize}}" color="{{loadingColor}}" />
        <text class="refresh-text">{{refreshText}}</text>
      </view>
    </view>
  </view>

  <!-- 内容区域 - 根据usePageScroll决定是否使用scroll-view -->
  <scroll-view
    wx:if="{{!usePageScroll}}"
    class="content-container"
    style="margin-top: {{topOffset}}; transform: translateY({{pullDistance}}rpx);"
    scroll-y="{{!disabled}}"
    bindscrolltolower="onScrollToLower"
    bindscroll="onScroll"
    lower-threshold="{{loadMoreDistance}}"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    bindtouchcancel="onTouchEnd"
  >
    <!-- scroll-view模式的内容 -->
    <view class="scroll-content">
      <!-- 初始加载状态 -->
      <view wx:if="{{loading}}">
        <!-- 自定义加载状态插槽 -->
        <slot name="loading"></slot>
      </view>

      <!-- 正常内容 -->
      <view wx:else>
        <!-- 主要内容插槽 -->
        <slot></slot>

        <!-- 上拉加载更多状态 -->
        <view wx:if="{{enableLoadMore}}" class="load-more-container">
          <view wx:if="{{loadingMore}}" class="load-more-loading">
            <text class="load-more-text">{{loadMoreText}}</text>
          </view>
          <view wx:elif="{{!hasMore}}" class="load-more-no-more">
            <text class="load-more-text">{{noMoreText}}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{isEmpty}}" class="empty-container">
        <!-- 自定义空状态插槽 -->
        <slot wx:if="{{hasCustomEmpty}}" name="empty"></slot>
        <!-- 默认空状态 -->
        <view wx:else class="default-empty">
          <status-display
            type="{{statusDisplayType}}"
            title="{{statusDisplayTitle}}"
            style="{{statusDisplayStyle}}"
            bind:buttonclick="onStatusDisplayButtonClick"
          />
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 页面级滚动模式的内容 -->
  <view
    wx:else
    class="page-scroll-container"
    style="margin-top: {{topOffset}};"
  >
    <!-- 初始加载状态 -->
    <view wx:if="{{loading}}">
      <!-- 自定义加载状态插槽 -->
      <slot name="loading"></slot>

      <!-- 默认加载状态（当没有使用自定义插槽时显示） -->
      <view class="loading-container" wx:if="{{!hasCustomLoading}}">
        <t-loading theme="circular" size="{{loadingSize}}" color="{{loadingColor}}" />
        <text class="loading-text">{{loadingText}}</text>
      </view>
    </view>

    <!-- 列表内容 -->
    <view class="list-container" wx:else>
      <!-- 主要内容插槽 -->
      <slot></slot>

      <!-- 空状态 -->
      <view wx:if="{{isEmpty}}">
        <!-- 自定义空状态插槽 -->
        <slot name="empty"></slot>

        <!-- 默认空状态（使用 status-display 组件） -->
        <status-display
          wx:if="{{!hasCustomEmpty}}"
          type="{{statusDisplayType}}"
          title="{{statusDisplayTitle}}"
          customStyle="{{statusDisplayStyle}}"
          visible="{{true}}"
          bind:buttonclick="onStatusDisplayButtonClick"
        />
      </view>

      <!-- 上拉加载更多 -->
      <view class="load-more-container" wx:if="{{enableLoadMore && !isEmpty && !disabled}}">
        <!-- 加载中状态 -->
        <view class="load-more-item" wx:if="{{loadingMore}}">
          <t-loading theme="circular" size="{{loadingSize}}" color="{{loadingColor}}" />
          <text class="load-more-text">{{loadMoreText}}</text>
        </view>

        <!-- 无更多数据状态 -->
        <view class="load-more-item" wx:elif="{{!hasMore}}">
          <text class="load-more-text no-more">{{noMoreText}}</text>
        </view>

        <!-- 默认提示状态 -->
        <view class="load-more-item" wx:else>
          <image class="pull-tip-icon" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMi4zMzMzM0wxMi42NjY3IDdINy4zMzMzM0w4IDIuMzMzMzNaIiBmaWxsPSIjZGRkIi8+Cjwvc3ZnPgo=" mode="aspectFit" />
          <text class="load-more-text pull-tip">{{pullTipText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 自定义底部插槽 -->
  <slot name="footer"></slot>
</view>
