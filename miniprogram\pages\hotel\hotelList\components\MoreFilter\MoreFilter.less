/* pages/hotel/hotelList/components/MoreFilter/MoreFilter.wxss */

.more-filter-container {
  display: flex;
  flex-direction: column;
}

/* 筛选布局 - 左右分栏 */
.filter-layout {
  display: flex;
}

/* 左侧分类导航 */
.left-categories {
  width: 162rpx;
  background-color: #f8f8f8;
  overflow-y: auto;
  max-height: 840rpx;
}

.category-nav-item {
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #33333E;
  height: 120rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  &.active {
    background-color: #fff;
    color: #568ded;
    font-weight: 500;
    
    // &::after {
    //   content: '';
    //   position: absolute;
    //   right: 0;
    //   top: 50%;
    //   transform: translateY(-50%);
    //   width: 6rpx;
    //   height: 48rpx;
    //   background-color: #568ded;
    //   border-radius: 3rpx 0 0 3rpx;
    // }
  }
  
  &:active {
    background-color: #f0f0f0;
  }
}

/* 选中状态指示器 */
.selected-indicator {
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 10rpx;
  height: 10rpx;
  background-color: #568ded;
  border-radius: 50%;
  flex-shrink: 0;
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  background-color: #fff;
  max-height: 840rpx;
  overflow-y: auto;
}

/* 筛选分类 */
.filter-category {
  padding: 24rpx;
  
  &:last-child {
    margin-bottom: 24rpx;
  }
}

/* 分类标题 */
.category-title {
  font-weight: 500;
  margin-bottom: 24rpx;
  
  &.level-1 {
    font-size: 28rpx;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &.level-2 {
    font-size: 28rpx;
    color: #66666E;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24rpx 0 16rpx 0;
  }
}

/* 筛选组 */
.filter-group {
  margin-bottom: 32rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

/* 展开按钮 */
.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.expand-text {
  font-size: 24rpx;
  color: #66666e;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #66666e;
  display: flex;
  align-items: center;
  
  &.expanded {
    transform: rotate(180deg);
  }
}

/* 选项网格 */
.options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.option-item {
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border: 1rpx solid #e5e5e5;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
  padding: 0 16rpx;
  
  /* 处理文字溢出 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  &:active {
    background-color: #ebebeb;
  }
  
  &.selected {
    background-color: #568ded;
    border-color: #568ded;
    color: #fff;
    
    &:active {
      background-color: #4a7bd9;
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 底部操作按钮样式 */
.bottom-actions {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24rpx;
  z-index: 1000;
}

.reset-btn {
  flex: 1;
  height: 104rpx;
  background: #F3F3F3;
  color: #11111E;
  font-size: 32rpx;
  border: none;
  border-radius: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::after {
    border: none;
  }
  
  &:active {
    background-color: #ebebeb;
  }
}

.confirm-btn {
  flex: 1;
  height: 104rpx;
  background-color: #568DED;
  color: #fff;
  font-size: 32rpx;
  border-radius: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::after {
    border: none;
  }
  
  &:active {
    background-color: #4a7bd9;
  }
}