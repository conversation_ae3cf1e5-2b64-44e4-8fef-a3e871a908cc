import { request } from "../utils/request";
import { uploadImage } from "../utils/upload";
import { EImageUploadType } from "../enum/my";

/**
 * 获取用户信息接口
 * 用于获取当前登录用户的基本信息，包括头像、昵称等
 * 
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回用户信息
 */
export const getUserInfo = () => request<My.IUserInfoRes>({
  showLoading: false,
  method: 'GET',
  url: '/hotel/user/profile/userInfo'
})


/**
 * 更新用户信息接口
 * 用于更新当前登录用户的基本信息，包括头像、昵称等
 * 
 * @param data 要更新的用户信息
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回更新后的用户信息
 */
export const updateUserInfo = (data: My.IUpdateUserInfoReq) => request<My.IUserInfoRes>({
  method: 'POST',
  showLoading: true,
  url: '/hotel/user/profile/update',
  data
})



/**
 * 获取基本头像
 * 
 */
export const getAvatar = () => request<string[]>({
  method: 'GET',
  url: '/hotel/user/profile/avatar'
})



/**
 * 发送注销账号短信
 * 
 */
export const sendLogoutCode = () => request({
  method: 'POST',
  url: '/hotel/user/auth/logout/code'
})

/**
 * 账号注销提交
 *
 */
export const sendLogout = (data: My.IFetchLogoutReq) => request({
  method: 'POST',
  url: '/hotel/user/auth/account/logout',
  data
})

/**
 * 上传反馈图片（单张）
 */
export const uploadFeedbackImage = (filePath: string) => {
  return uploadImage(filePath, {
    url: '/hotel/user/upload/images',
    name: 'images',
    type: EImageUploadType.FEEDBACK
  });
}

/**
 * 上传用户头像（单张）
 */
export const uploadAvatarImage = (filePath: string) => {
  return uploadImage(filePath, {
    url: '/hotel/user/upload/images',
    name: 'files',
    type: EImageUploadType.AVATAR
  });
}

/**
 * 通用图片上传方法
 * @param filePath 图片文件路径
 * @param type 图片上传类型
 */
export const uploadImageWithType = (filePath: string, type: EImageUploadType) => {
  return uploadImage(filePath, {
    url: '/hotel/user/upload/images',
    name: 'files',
    type
  });
}

/**
 * 批量上传反馈图片（真正的批量上传，一次接口调用上传多张图片）
 * 用于用户反馈页面的多图片上传
 */
export const uploadMultipleFeedbackImages = (filePaths: string[]) => {
  const { uploadMultipleImagesAtOnce } = require('../utils/upload');
  return uploadMultipleImagesAtOnce(filePaths, {
    url: '/hotel/user/upload/images',
    formData: {
      type: EImageUploadType.FEEDBACK
    }
  });
}



/**
 * 提交用户反馈
 *
 */
export const submitFeedback = (data: My.ISubmitFeedbackReq) => request<My.ISubmitFeedbackRes>({
  method: 'POST',
  url: '/hotel/user/feedback/submit',
  data
})


/**
 * 获取行为接口
 *
 */
export const getFavorites = (data: My.IFavoritesReq) => request<My.ISubmitFeedbackRes>({
  method: 'POST',
  url: '/hotel/search/list/user/behavior/v1',
  data
})

/**
 * 取消收藏接口
 *
 */
export const sendFavorites = (data: My.IFavoritesBatchReq) => request<My.ISubmitFeedbackRes>({
  method: 'POST',
  url: '/hotel/user/favorites/batch',
  data
})

/**
 * 活动签到接口
 * 用于用户在活动中进行签到操作
 *
 * @param data 签到请求参数，包含活动ID和订单ID
 * @returns Promise<Request.IResponseResult<My.ISignInData>> 返回签到结果
 */
export const activitySignIn = (data: My.ISignInReq) => request<My.ISignInData>({
  method: 'POST',
  showLoading: true,
  url: '/trip/activity/v1/perform/signIn',
  data
})

/**
 * C端分页查询活动列表
 * 用于获取用户的活动订单列表，支持分页查询
 *
 * @param params 查询参数，包含页码和每页大小
 * @returns Promise<Request.IResponseResult<My.IActivityPageResponse>> 返回活动列表
 */
export const getActivityList = (params: My.IActivityQueryParams) => request<My.IActivityPageResponse>({
  method: 'GET',
  showLoading: false,
  url: '/trip/activity/v1/front/queryPage',
  data: params
})

/**
 * 行程订单列表查询
 * 用于获取用户的行程订单列表，支持分页查询
 *
 * @param params 查询参数，包含页码和每页大小
 * @returns Promise<Request.IResponseResult<My.ITrailOrderListResponse>> 返回订单列表
 */
export const getTrailOrderList = (params: My.ITrailOrderListRequest) => request<My.ITrailOrderListResponse>({
  method: 'POST',
  showLoading: false,
  url: '/hotel/order/active/trailOrderList',
  data: params
})