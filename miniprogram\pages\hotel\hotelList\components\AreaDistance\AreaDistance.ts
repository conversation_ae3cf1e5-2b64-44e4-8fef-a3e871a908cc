// pages/hotel/hotelList/components/AreaDistance/AreaDistance.ts

interface AreaDistanceFilter {
  typeId: number;
  filterId: string;
  name: string;
  nameEn: string | null;
  describe: string | null;
  multi: number;
  subFilters: AreaDistanceFilter[];
  grey: any | null;
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 区域距离数据
    areaDistance: {
      type: Array,
      value: []
    },
    // 当前选中的位置距离项（用于聚焦）
    currentSelectedItem: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imgBaseUrl: getApp().globalData.imgBaseUrl,
    // 当前选中的一级分类索引
    currentCategoryIndex: 0,
    // 当前选中的二级分类索引
    currentSubCategoryIndex: 0,
    // 当前一级分类数据
    currentCategory: null as AreaDistanceFilter | null,
    // 当前二级分类列表
    currentSubCategories: [] as AreaDistanceFilter[],
    // 当前最终选项列表（叶子节点）
    currentFinalItems: [] as AreaDistanceFilter[],
    // 是否为三级结构
    isThreeLevel: false,
    // 选中的项目
    selectedItem: null as AreaDistanceFilter | null
  },

  /**
   * 组件生命周期
   */
  observers: {
    'areaDistance'(newVal: AreaDistanceFilter[]) {
      if (newVal && newVal.length > 0) {
        // 如果有传入选中项，则聚焦到该项，否则默认选中第一个分类
        const currentSelected = this.properties.currentSelectedItem as AreaDistanceFilter;
        if (currentSelected && currentSelected.typeId) {
          this.focusToSelectedItem(currentSelected, newVal);
        } else {
          this.initializeFirstCategory(newVal[0], 0);
        }
      }
    },
    'currentSelectedItem'(newVal: AreaDistanceFilter) {
      // 当选中项改变时，更新组件状态
      if (newVal && newVal.typeId && this.properties.areaDistance.length > 0) {
        this.setData({
          selectedItem: newVal
        });
        this.focusToSelectedItem(newVal, this.properties.areaDistance);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化第一个分类
    initializeFirstCategory(category: AreaDistanceFilter, index: number) {
      const isThreeLevel = this.checkIsThreeLevel(category);
      
      if (isThreeLevel) {
        // 三级结构：显示二级分类列表
        const subCategories = category.subFilters || [];
        this.setData({
          currentCategoryIndex: index,
          currentSubCategoryIndex: 0,
          currentCategory: category,
          currentSubCategories: subCategories,
          currentFinalItems: subCategories.length > 0 ? (subCategories[0].subFilters || []) : [],
          isThreeLevel: true
        });
      } else {
        // 二级结构：直接显示最终选项
        this.setData({
          currentCategoryIndex: index,
          currentSubCategoryIndex: 0,
          currentCategory: category,
          currentSubCategories: [],
          currentFinalItems: category.subFilters || [],
          isThreeLevel: false
        });
      }
    },

    // 检查是否为三级结构
    checkIsThreeLevel(category: AreaDistanceFilter): boolean {
      if (!category || !category.subFilters || category.subFilters.length === 0) {
        return false;
      }
      
      // 检查第一个子项是否还有子项
      const firstSubItem = category.subFilters[0];
      return !!(firstSubItem && firstSubItem.subFilters && firstSubItem.subFilters.length > 0);
    },



    // 点击一级分类
    onCategoryClick(e: any) {
      const { index } = e.currentTarget.dataset;
      const areaDistance = this.properties.areaDistance as AreaDistanceFilter[];
      
      if (index !== this.data.currentCategoryIndex) {
        const category = areaDistance[index];
        
        // 检查当前选中项是否属于新的类目，如果不属于则清空
        if (!this.isSelectedItemInCategory(category)) {
          this.setData({
            selectedItem: null
          });
        }
        
        this.initializeFirstCategory(category, index);
      }
    },

    // 点击二级分类（仅在三级结构时有效）
    onSubCategoryClick(e: any) {
      const { index } = e.currentTarget.dataset;
      const { currentSubCategories, currentCategory } = this.data;
      
      if (index !== this.data.currentSubCategoryIndex && currentSubCategories[index]) {
        const subCategory = currentSubCategories[index];
        
        // 检查当前选中项是否属于新的二级分类，如果不属于则清空
        if (currentCategory && !this.isSelectedItemInCategory(currentCategory, index)) {
          this.setData({
            selectedItem: null
          });
        }
        
        this.setData({
          currentSubCategoryIndex: index,
          currentFinalItems: subCategory.subFilters || []
        });
      }
    },

    // 点击最终选项
    onFinalItemClick(e: any) {
      const { item } = e.currentTarget.dataset;
      
      // 只有叶子节点才能被选中
      if (!item.subFilters || item.subFilters.length === 0) {
        const { selectedItem } = this.data;
        
        // 检查是否点击的是已选中项
        if (selectedItem && selectedItem.typeId === item.typeId && selectedItem.filterId === item.filterId) {
          // 取消选中
          this.setData({
            selectedItem: null
          });
          
          // 触发重置事件
          this.triggerEvent('distanceReset');
        } else {
          // 选中新项
          this.setData({
            selectedItem: item
          });
          
          // 立即触发选择事件并请求数据
          this.triggerEvent('distanceChange', {
            selectedItem: item,
            filters: {
              typeId: item.typeId,
              filterId: item.filterId,
              category: 'distance' // 添加分类标识，便于识别和替换
            }
          });
        }
      }
    },

    // 重置按钮点击
    onResetClick() {
      this.setData({
        selectedItem: null
      });
      
      // 触发重置事件
      this.triggerEvent('distanceReset');
    },

    // 完成按钮点击
    onConfirmClick() {
      const { selectedItem } = this.data;
      
      // 如果没有选中任何项，触发重置事件清空Filter状态
      if (!selectedItem) {
        this.triggerEvent('distanceReset');
      }
      
      // 统一的关闭弹窗事件
      this.triggerEvent('filterPopupClose');
    },

    // 判断是否为选中项
    isSelected(item: AreaDistanceFilter): boolean {
      const { selectedItem } = this.data;
      return !!(selectedItem && 
               selectedItem.typeId === item.typeId && 
               selectedItem.filterId === item.filterId);
    },

    // 检查选中项是否属于指定的类目或子类目
    isSelectedItemInCategory(category: AreaDistanceFilter, subCategoryIndex?: number): boolean {
      const { selectedItem } = this.data;
      if (!selectedItem) return false;

      const isThreeLevel = this.checkIsThreeLevel(category);
      
      if (isThreeLevel && typeof subCategoryIndex === 'number') {
        // 三级结构：检查是否在指定的二级分类中
        const subCategories = category.subFilters || [];
        if (subCategoryIndex < subCategories.length) {
          const subCategory = subCategories[subCategoryIndex];
          const finalItems = subCategory.subFilters || [];
          return finalItems.some(item => 
            item.typeId === selectedItem.typeId && 
            item.filterId === selectedItem.filterId
          );
        }
        return false;
      } else {
        // 二级结构：检查是否在一级分类的直接子项中
        const finalItems = category.subFilters || [];
        return finalItems.some(item => 
          item.typeId === selectedItem.typeId && 
          item.filterId === selectedItem.filterId
        );
      }
    },

    // 聚焦到指定的选中项
    focusToSelectedItem(selectedItem: AreaDistanceFilter, areaDistance: AreaDistanceFilter[]) {
      // 查找包含该项的一级分类
      for (let i = 0; i < areaDistance.length; i++) {
        const category = areaDistance[i];
        
        // 检查是否为三级结构
        const isThreeLevel = this.checkIsThreeLevel(category);
        
        if (isThreeLevel) {
          // 三级结构：在二级分类中查找
          const subCategories = category.subFilters || [];
          for (let j = 0; j < subCategories.length; j++) {
            const subCategory = subCategories[j];
            const finalItems = subCategory.subFilters || [];
            
            // 在最终项中查找匹配项
            if (finalItems.some(item => item.typeId === selectedItem.typeId && item.filterId === selectedItem.filterId)) {
              this.setData({
                currentCategoryIndex: i,
                currentSubCategoryIndex: j,
                currentCategory: category,
                currentSubCategories: subCategories,
                currentFinalItems: finalItems,
                isThreeLevel: true,
                selectedItem: selectedItem
              });
              return;
            }
          }
        } else {
          // 二级结构：直接在一级分类的子项中查找
          const finalItems = category.subFilters || [];
          if (finalItems.some(item => item.typeId === selectedItem.typeId && item.filterId === selectedItem.filterId)) {
            this.setData({
              currentCategoryIndex: i,
              currentSubCategoryIndex: 0,
              currentCategory: category,
              currentSubCategories: [],
              currentFinalItems: finalItems,
              isThreeLevel: false,
              selectedItem: selectedItem
            });
            return;
          }
        }
      }
      
      // 如果没找到，则默认选择第一个分类
      this.initializeFirstCategory(areaDistance[0], 0);
    }
  }
})