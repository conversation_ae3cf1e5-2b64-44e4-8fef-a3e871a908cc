/**
 * 悬浮按钮组件
 * 用于切换编辑模式的悬浮按钮
 */

/**
 * 组件数据接口定义
 */
interface FloatingButtonData {}

/**
 * 组件属性接口定义
 */
interface FloatingButtonProps extends WechatMiniprogram.Component.PropertyOption {
  /** 是否显示按钮 */
  visible: {
    type: BooleanConstructor;
    value: boolean;
  };
  /** 是否为编辑模式 */
  isEditMode: {
    type: BooleanConstructor;
    value: boolean;
  };
}

/**
 * 组件方法接口定义
 */
interface FloatingButtonMethods {
  /** 切换模式事件 */
  onToggleMode(): void;
  /** 字符串索引签名以满足MethodOption约束 */
  [key: string]: Function;
}

Component<
  FloatingButtonData,
  FloatingButtonProps,
  FloatingButtonMethods,
  WechatMiniprogram.Component.BehaviorOption,
  {},
  false
>({
  /**
   * 组件的属性列表
   */
  properties: {
    /** 是否显示按钮 */
    visible: {
      type: Boolean,
      value: false
    },
    /** 是否为编辑模式 */
    isEditMode: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换模式事件
     */
    onToggleMode() {
      this.triggerEvent('togglemode');
    }
  }
});
