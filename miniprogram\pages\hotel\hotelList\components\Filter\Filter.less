/* pages/hotel/hotelList/components/Filter/Filter.wxss */

.filter-container {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 32rpx 16rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.filter-item.active {
  color: #568DED;
  
  .filter-text {
    color: #568DED;
    font-weight: 500;
  }
  
  .filter-arrow {
    color: #568DED;
  }
  
  .filter-count {
    background-color: #568DED;
    color: #fff;
  }
}

.filter-text {
  font-size: 28rpx;
  color: #33333e;
}

.sort-label,.distance-label{
  max-width: 180rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


.filter-content {
  display: flex;
  align-items: center;
}

.filter-count {
  font-size: 20rpx;
  border-radius: 12rpx;
  width: 24rpx;
  height: 24rpx;
  line-height: 24rpx;
  text-align: center;
  margin-left: 4rpx;
}

.filter-arrow {
  color: #999;
  transition: all 0.3s ease;
  
  &.rotated {
    transform: rotate(180deg);
  }
}

