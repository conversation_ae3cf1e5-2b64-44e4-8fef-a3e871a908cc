// pages/tripHelper/my/edit/index.ts
import { getImageUrl } from '../../../../utils/images';
import { GenderEnum, GenderTextEnum } from '../../../../enum/my';
import { updateUserInfo, uploadAvatarImage, getUserInfo } from '../../../../api/my';
import { store } from '../../../../store/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {
      avatar: '',
      nickname: '',
      gender: GenderEnum.Unknown,
      birthday: '',
      email: '',
      mobile: '',
      userId: 0
    } as My.IUserInfoRes,
    // 原始用户信息（用于比较是否有变化）
    originalUserInfo: {} as My.IUserInfoRes,
    // 图标资源
    arrowIcon: getImageUrl('arrow.png'),
    // 性别文本映射
    genderTextMap: {
      [GenderEnum.Unknown]: GenderTextEnum.Secret,
      [GenderEnum.Male]: GenderTextEnum.Male,
      [GenderEnum.Female]: GenderTextEnum.Female
    },
    // 弹框状态
    showNicknameModal: false,
    showGenderPicker: false,
    // 昵称输入值
    nicknameInput: '',
    // 性别选择器数据
    genderOptions: [
      { label: GenderTextEnum.Female, value: GenderEnum.Female },
      { label: GenderTextEnum.Male, value: GenderEnum.Male }
    ],
    // 显示值
    displayNickname: '请填写',
    displayGender: '请选择',

    // 默认头像URL
    defaultAvatarUrl: getImageUrl('user/avatar_default.png')
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '编辑'
    });
    // 获取用户信息
    this.getUserInfo();
  },


  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      const result = await getUserInfo();
      if (result.isSuccess && result.data) {
        const userInfo = result.data;
        // 计算显示的性别文本
        const displayGender = userInfo.gender === GenderEnum.Unknown ? '请选择' : this.data.genderTextMap[userInfo.gender as keyof typeof this.data.genderTextMap];

        this.setData({
          userInfo,
          originalUserInfo: { ...userInfo }, // 保存原始数据
          // 计算显示值
          displayNickname: userInfo.nickname || '请填写',
          displayGender: displayGender
        });
        // 强制更新显示值
        this.updateDisplayValues();
      } else {
        console.error('获取用户信息失败:', result.message);
        // 使用默认值
        this.setDefaultUserInfo();
      }
    } catch (error) {
      console.error('获取用户信息异常:', error);
      // 使用默认值
      this.setDefaultUserInfo();
    }
  },

  /**
   * 设置默认用户信息
   */
  setDefaultUserInfo() {
    const defaultUserInfo: My.IUserInfoRes = {
      avatar: getImageUrl('user/avatar_default.png'),
      nickname: '',
      gender: GenderEnum.Unknown,
      birthday: '',
      email: '',
      mobile: '',
      userId: 0
    };

    this.setData({
      userInfo: defaultUserInfo,
      originalUserInfo: { ...defaultUserInfo },
      displayNickname: '请填写',
      displayGender: '请选择'
    });
  },

  /**
   * 更新显示值
   */
  updateDisplayValues() {
    const { userInfo } = this.data;
    const displayNickname = userInfo.nickname || '请填写';
    const displayGender = userInfo.gender === GenderEnum.Unknown ? '请选择' : this.data.genderTextMap[userInfo.gender as keyof typeof this.data.genderTextMap];

    this.setData({
      displayNickname,
      displayGender
    });
  },

  /**
   * 微信原生选择头像
   */
  async onChooseAvatar(e: any) {
    const { avatarUrl } = e.detail;

    if (!avatarUrl) {
      wx.displayToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }


    try {
      wx.displayLoading({
        title: '上传中...'
      });

      // 上传头像到服务器
      const uploadResult = await uploadAvatarImage(avatarUrl);

      wx.hidePrevLoading();

      if (uploadResult.isSuccess && uploadResult.data) {
        // 实际返回的是数组，但类型定义是对象，需要处理兼容性
        const avatarUrl = Array.isArray(uploadResult.data)
          ? uploadResult.data[0]
          : (uploadResult.data as any).url;

        // 更新头像显示
        this.setData({
          'userInfo.avatar': avatarUrl
        });

        // 立即保存到服务器
        await this.saveUserInfo('avatar');

        wx.displayToast({
          title: '头像更新成功',
          icon: 'success'
        });
      } else {
        wx.displayToast({
          title: uploadResult.message || '头像上传失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hidePrevLoading();
      wx.displayToast({
        title: '头像上传失败',
        icon: 'none'
      });
    }
  },

  /**
   * 点击昵称
   */
  onNicknameClick() {
    // 设置当前昵称到输入框
    this.setData({
      nicknameInput: this.data.userInfo.nickname,
      showNicknameModal: true
    });
  },

  /**
   * 点击性别
   */
  onGenderClick() {
    this.setData({
      showGenderPicker: true
    });
  },

  /**
   * 昵称输入框变化
   */
  onNicknameInput(e: WechatMiniprogram.Input) {
    this.setData({
      nicknameInput: e.detail.value
    });
  },

  /**
   * 昵称弹框状态变化
   */
  onNicknamePopupChange(e: any) {
    if (!e.detail.visible) {
      this.setData({
        showNicknameModal: false,
        nicknameInput: ''
      });
    }
  },

  /**
   * 取消昵称编辑
   */
  onNicknameCancelClick() {
    this.setData({
      showNicknameModal: false,
      nicknameInput: ''
    });
  },

  /**
   * 确认昵称编辑
   */
  async onNicknameConfirmClick() {
    const nickname = this.data.nicknameInput.trim();

    if (!nickname) {
      wx.displayToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    try {
      // 更新昵称
      this.setData({
        'userInfo.nickname': nickname,
        showNicknameModal: false,
        nicknameInput: '',
        displayNickname: nickname
      });

      // 保存到服务器
      await this.saveUserInfo('nickname');

      wx.displayToast({
        title: '昵称更新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('昵称更新失败:', error);
      wx.displayToast({
        title: '昵称更新失败',
        icon: 'none'
      });
    }
  },

  /**
   * 性别选择器变化
   */
  async onGenderPickerChange(e: any) {
    const selectedValue = e.detail.value[0];

    try {
      // 更新性别
      this.setData({
        'userInfo.gender': selectedValue,
        showGenderPicker: false,
        displayGender: this.data.genderTextMap[selectedValue as keyof typeof this.data.genderTextMap]
      });

      // 保存到服务器
      await this.saveUserInfo('gender');

      wx.displayToast({
        title: '性别更新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('性别更新失败:', error);
      wx.displayToast({
        title: '性别更新失败',
        icon: 'none'
      });
    }
  },

  /**
   * 性别选择器取消
   */
  onGenderPickerCancel() {
    this.setData({
      showGenderPicker: false
    });
  },

  /**
   * 保存用户信息到服务器
   * @param field 更新的字段类型，用于日志记录
   */
  async saveUserInfo(field?: string) {

    try {
      const { userInfo } = this.data;

      // 构造更新请求数据
      const updateData: My.IUpdateUserInfoReq = {
        avatar: userInfo.avatar,
        nickname: userInfo.nickname,
        gender: userInfo.gender,
        birthday: userInfo.birthday || ''
      };

      const result = await updateUserInfo(updateData);

      if (result.isSuccess) {

        // 更新原始数据，用于后续比较
        this.setData({
          originalUserInfo: { ...userInfo }
        });

        // 更新全局store，同步到其他页面
        store.setUserDetail({
          avatar: userInfo.avatar,
          nickname: userInfo.nickname,
          gender: userInfo.gender
        });

      } else {
        throw new Error(result.message || '更新失败');
      }
    } catch (error) {
      throw error;
    }
  },



  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时，如果有未保存的更改，可以在这里处理
    // 当前设计是实时保存，所以不需要额外处理
  }
})
