import { ComponentWithComputedStore } from '../../core/componentWithStoreComputed';
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings';
import { behavior as computedBehavior } from 'miniprogram-computed';
import { getImageUrl } from '../../utils/images';
import { hasToken } from '../../utils/userInfo';
import { LoginManager } from '../../utils/loginManager';

ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior, computedBehavior],

  /**
   * 组件的属性列表
   */
  properties: {
    /** 当前选中的tab索引 */
    current: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    tabList: [
      {
        icon: 'home',
        text: '首页',
        path: '/pages/home/<USER>/index',
        iconUrl: getImageUrl('tabbar/home.png'),
        iconActiveUrl: getImageUrl('tabbar/home_act.png')
      },
      {
        icon: 'create',
        text: '创建活动',
        path: '/pages/tripHelper/activityCreate/index',
        iconUrl: getImageUrl('tabbar/plus.png')
      },
      {
        icon: 'user',
        text: '我的',
        path: '/pages/tripHelper/my/index',
        iconUrl: getImageUrl('tabbar/my.png'),
        iconActiveUrl: getImageUrl('tabbar/my_act.png')
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换tab
     */
    switchTab(e: WechatMiniprogram.TouchEvent) {
      const index = e.currentTarget.dataset.index as number;
      const path = e.currentTarget.dataset.path as string;

      if (index === this.data.current) {
        return;
      }
      // 特殊处理：创建活动按钮（index === 1）需要登录判断

      if (path === '/pages/tripHelper/activityCreate/index') {
        // 检查是否已登录
        if (!hasToken()) {
          // 未登录，跳转到登录页面，并传入创建活动页面作为backUrl
          LoginManager.navigateToLogin(path);
          return;
        }
      }

      // 触发父组件事件
      this.triggerEvent('change', { index, path });

      if (path === '/pages/tripHelper/activityCreate/index') {
        wx.navigateTo({
          url: path
        });
        return
      }

      // 页面跳转 - 使用redirectTo避免产生历史栈，模拟原生tabBar行为
      wx.redirectTo({
        url: path
      });
    }
  }
});
