Page({

  /**
   * 页面的初始数据
   */
  data: {
    placeList: [{
      placeName: '',
      startTime: '',
      latitude: '',
      longitude: ''
    }] as Activity.IPlace[],
    activeIndex: null as Nullable<number>,
    showTimePicker: false,
    minDate: '',
    maxDate: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const eventChannel = this.getOpenerEventChannel();

    // 监听来自发起页面的数据
    eventChannel.on?.('getPOIList', (data: {value: Activity.IPlace[], minDate: string, maxDate: string}) => {
      this.setData({
        placeList: data.value.length ? data.value : [{
          placeName: '',
          startTime: '',
          latitude: '',
          longitude: ''
        }],
        minDate: `${data.minDate} 00:00:00`,
        maxDate: `${data.maxDate} 23:59:59`
      })
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  handleCloseTimePicker() {
    this.setData({
      showTimePicker: false
    })
  },
  /** 集合时间变更 */
  handleTimeChange(e: WechatMiniprogram.CustomEvent<{ value: string }>) {
    const list = this.data.placeList;
    if (this.data.activeIndex !== null) {
      list[this.data.activeIndex].startTime = e.detail.value
    }
    this.setData({
      placeList: list
    })
  },
  handleOpenTimePicker(e: WechatMiniprogram.TouchEvent<{}, {}, { index: number }>) {
    this.setData({
      activeIndex: e.currentTarget.dataset.index,
      showTimePicker: true,
    })
  },
  /** 删除集合点 */
  handleRemove(e: WechatMiniprogram.TouchEvent<{}, {}, { index: number }>) {
    const list = this.data.placeList;
    list.splice(e.currentTarget.dataset.index, 1);
    this.setData({
      placeList: list
    })
  },
  /** 新增集合点 */
  handleInsertPlace() {
    this.setData({
      placeList: [
        ...this.data.placeList,
        {
          startTime: '',
          placeName: '',
          longitude: '',
          latitude: ''
        }
      ]
    })
  },
  /** 腾讯地图选择集合点 */
  handleChooseLocation(e: WechatMiniprogram.TouchEvent<{}, {}, { index: number }>) {
    wx.chooseLocation({
      success: (res) => {
        if (res.errMsg === "chooseLocation:ok") {
          // example: {errMsg: "chooseLocation:ok", name: "香溢烟酒店(龙王塘路店)", address: "浙江省杭州市临平区龙王塘路心怡苑北侧约40米", latitude: 30.420338, longitude: 120.296968}
          const { name, latitude, longitude } = res;
          const list = this.data.placeList;
          Object.assign(list[e.currentTarget.dataset.index], {
            placeName: name,
            longitude,
            latitude
          })
          this.setData({
            placeList: list
          })
        }
      }
    })
  },
  handleSubmit() {
    const list = this.data.placeList;
    if (list.length === 0) {
      wx.showToast({
        title: '请至少维护一个集合点',
        icon: 'none',
        duration: 3000
      })
      return;
    }
    if (list.some(item => !item.startTime)) {
      wx.showToast({
        title: '集合时间不能为空',
        icon: 'none',
        duration: 3000
      })
      return;
    }
    if (list.some(item => !item.latitude)) {
      wx.showToast({
        title: '集合地点不能为空',
        icon: 'none',
        duration: 3000
      })
      return;
    }
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.emit?.('updatePOIList', { value: list });
    wx.navigateBack();
  }
})