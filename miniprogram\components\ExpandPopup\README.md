# ExpandPopup 展开弹窗组件

## 组件功能

- 相对定位的向下展开弹窗效果
- 内容高度自适应，支持最大高度限制
- 无背景遮罩的轻量级设计
- 流畅的向下展开/收起动画
- 支持自定义内容（slot）
- 适用于 Filter 组件下方的选项展示

## 使用方法

### 1. 在页面中引入组件

```json
// hotelList.json
{
  "usingComponents": {
    "expand-popup": "./components/ExpandPopup/ExpandPopup"
  }
}
```

### 2. 在页面中使用

```xml
<!-- hotelList.wxml -->
<!-- 注意：ExpandPopup需要放在具有相对定位的父容器中 -->
<view class="filter-container" style="position: relative;">
  <filter bind:sortClick="onSortClick" />

  <expand-popup
    visible="{{showSortPopup}}"
    max-height="60vh"
    bind:close="onSortPopupClose"
  >
    <!-- 自定义内容 -->
    <view class="sort-options">
      <view class="sort-item" wx:for="{{sortOptions}}" wx:key="value" bind:tap="onSortSelect" data-value="{{item.value}}">
        <text class="sort-label">{{item.label}}</text>
        <view class="sort-check" wx:if="{{item.selected}}">✓</view>
      </view>
    </view>
  </expand-popup>
</view>
```

### 3. 页面数据和方法

```typescript
// hotelList.ts
data: {
  showSortPopup: false,
  sortOptions: [
    { label: '智能排序', value: 'smart', selected: true },
    { label: '好评优先', value: 'rating', selected: false },
    { label: '点评数 多→少', value: 'reviews', selected: false },
    { label: '低价优先', value: 'price_low', selected: false },
    { label: '高价优先', value: 'price_high', selected: false }
  ]
},

// 打开排序弹窗
showSortPopup() {
  this.setData({ showSortPopup: true });
},

// 关闭排序弹窗
onSortPopupClose() {
  this.setData({ showSortPopup: false });
},

// 选择排序选项
onSortSelect(e) {
  const value = e.currentTarget.dataset.value;
  // 处理排序选择逻辑
  this.onSortPopupClose();
}
```

## 属性配置

| 属性         | 类型    | 默认值 | 说明                 |
| ------------ | ------- | ------ | -------------------- |
| visible      | Boolean | false  | 控制弹窗显示/隐藏    |
| maxHeight    | String  | '70vh' | 内容区域最大高度     |
| maskClosable | Boolean | true   | 点击遮罩是否关闭弹窗 |

## 事件

| 事件名 | 说明         | 返回值 |
| ------ | ------------ | ------ |
| close  | 弹窗关闭事件 | -      |

## 样式自定义

可以通过 CSS 变量自定义组件样式：

```css
.expand-popup {
  --mask-bg-color: rgba(0, 0, 0, 0.5); /* 遮罩背景色 */
  --content-bg-color: #fff; /* 内容背景色 */
  --content-border-radius: 24rpx; /* 内容区域圆角 */
  --animation-duration: 0.3s; /* 动画持续时间 */
}
```

## 使用场景示例

### 排序选择弹窗

```xml
<expand-popup visible="{{showSortPopup}}" bind:close="closeSortPopup">
  <view class="sort-list">
    <view class="sort-item" wx:for="{{sortOptions}}" wx:key="value">
      {{item.label}}
    </view>
  </view>
</expand-popup>
```

### 筛选选项弹窗

```xml
<expand-popup visible="{{showFilterPopup}}" max-height="80vh" bind:close="closeFilterPopup">
  <view class="filter-content">
    <view class="filter-section">
      <text class="section-title">价格范围</text>
      <!-- 价格选项 -->
    </view>
    <view class="filter-section">
      <text class="section-title">星级</text>
      <!-- 星级选项 -->
    </view>
  </view>
</expand-popup>
```
