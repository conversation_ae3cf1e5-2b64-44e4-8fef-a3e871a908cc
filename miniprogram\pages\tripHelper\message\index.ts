// pages/tripHelper/message/index.ts
import { getImageUrl } from '../../../utils/images';

// 消息项接口定义
interface MessageItem {
  id: number;
  title: string;
  date: string;
  weekday: string;
  status: string;
  time: string;
  description: string;
  image: string;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 消息列表
    messageList: [] as MessageItem[],

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: false,

    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false,

    // 图标资源
    timeIcon: getImageUrl('tripHelper/my/msg_time.png'),
    arrowIcon: getImageUrl('tripHelper/my/msg_arrow.png')
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 使用自定义导航栏，不需要设置原生导航栏标题
    this.loadMessageList();
  },

  /**
   * 加载消息列表
   */
  async loadMessageList(isLoadMore = false) {
    try {
      if (!isLoadMore) {
        this.setData({ loading: true });
      } else {
        this.setData({ loadingMore: true });
      }

      // TODO: 调用真实的消息列表API
      // const response = await getMessageList({
      //   page: this.data.pageNum,
      //   size: this.data.pageSize
      // });

      // 暂时显示空状态，等待真实API接入
      // 当没有数据时，不设置hasMore，让组件自动处理
      this.setData({
        messageList: [],
        pageNum: 1
      });

    } catch (error) {
      console.error('加载消息列表失败:', error);
      wx.displayToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      });
    }
  },



  /**
   * 自定义导航栏返回按钮点击事件
   */
  onNavigationBack() {
    wx.navigateBack();
  },

  /**
   * 点击消息项
   */
  onMessageClick(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    // 跳转到消息详情页
    wx.navigateTo({
      url: `/pages/tripHelper/messageDetail/index?id=${id}`
    });
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({
      refreshing: true,
      pageNum: 1
    });
    this.loadMessageList();
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }
    this.loadMessageList(true);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
