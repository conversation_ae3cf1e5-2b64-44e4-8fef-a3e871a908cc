<template name="touristsInfo">
  <view class="item-view tour-info">
    <!-- 标题+预定数量 -->
    <view class="tour-title-box">
      <text class="left">入住信息</text>
      <view class="right">
        <text class="tips">仅剩2间</text>
        <view class="num-box">
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/sub_disable_icon.png" mode=""/>
          <text>1间</text>
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/add_icon.png" mode=""/>
        </view>
      </view>
    </view>
    <!-- 入住人信息 -->
    <view class="tour-check-men">
      <!-- 姓名 -->
      <!-- 样式1 -->
      <view class="bg-area">
        <view class="left">
          <view class="title-box">
            <text class="must">入住人</text>
          </view>
          <input class="input" placeholder-class="tourists_phcolor" placeholder="请输入住客姓名" type="text" maxlength="30"/>
        </view>
        <image class="right" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/tour_list.png" mode=""/>
      </view>
      <!-- 样式2 -->
      <view class="bg-area">
        <view class="left">
          <view class="title-box">
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/check_men_sub.png" mode=""/>
            <text class="must">房间2</text>
          </view>
          <input class="input" placeholder-class="tourists_phcolor" placeholder="请输入住客姓名" type="text" maxlength="30"/>
        </view>
        <image class="right" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/tour_list.png" mode=""/>
      </view>
      <!-- 提示信息 -->
      <!-- 橙色提示文案 -->
      <!-- <view class="check-men-name-tips">请输入住客姓名，每间只需填1人，姓名不可重复</view> -->
      <!-- 蓝色提示文案 -->
      <view class="check-men-add-tips">
        <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/check_men_add.png" mode=""/>
        <text class="t1">填写房间2住客</text>
        <text class="t2">(可选)</text>
      </view>
      <!-- 手机号 -->
      <view class="bg-area">
        <view class="left">
          <view class="title-box">
            <text class="must">手机号</text>
          </view>
          <text class="area-num">+86</text>
          <input class="input" placeholder-class="tourists_phcolor" placeholder="请输入住客手机号" type="number" maxlength="30"/>
        </view>
        <image class="right" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/phone_icon.png" mode=""/>
      </view>
    </view>
  </view>
</template>