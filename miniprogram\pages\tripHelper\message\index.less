/* pages/tripHelper/message/index.less */
.message-page {
  min-height: 100vh;
  background: #F3F3F3;

  /* 骨架屏样式 */
  .skeleton-container {
    padding: 32rpx;

    .skeleton-item {
      background: #ffffff;
      border-radius: 32rpx;
      margin-bottom: 16rpx;
      padding: 24rpx;

      .skeleton-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .skeleton-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 16rpx;
          background: #f0f0f0;
          margin-right: 24rpx;
          flex-shrink: 0;
        }

        .skeleton-info {
          flex: 1;

          .skeleton-title {
            height: 32rpx;
            background: #f0f0f0;
            border-radius: 16rpx;
            margin-bottom: 12rpx;
            width: 60%;
          }

          .skeleton-date {
            height: 24rpx;
            background: #f0f0f0;
            border-radius: 12rpx;
            width: 40%;
          }
        }

        .skeleton-arrow {
          width: 24rpx;
          height: 24rpx;
          background: #f0f0f0;
          border-radius: 12rpx;
          flex-shrink: 0;
        }
      }

      .skeleton-content {
        .skeleton-status-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12rpx;

          .skeleton-status {
            height: 32rpx;
            background: #f0f0f0;
            border-radius: 16rpx;
            width: 50%;
          }

          .skeleton-time {
            height: 24rpx;
            background: #f0f0f0;
            border-radius: 12rpx;
            width: 25%;
          }
        }

        .skeleton-description {
          height: 48rpx;
          background: #f0f0f0;
          border-radius: 16rpx;
          width: 90%;
        }
      }
    }
  }

  .message-list {
    padding: 32rpx;
    .message-item {
      background: #ffffff;
      border-radius: 32rpx;
      margin-bottom: 16rpx;
      padding: 24rpx;

      .message-header {
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid #F0F0F0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;

        .message-info {
          display: flex;
          align-items: center;
          flex: 1;

          .message-image {
            width: 64rpx;
            height: 64rpx;
            border-radius: 16rpx;
            margin-right: 24rpx;
            flex-shrink: 0;
          }

          .message-title-section {
            flex: 1;

            .message-title {
              color: #66666E;
              font-size: 28rpx;
              font-weight: 500;
              display: block;
              margin-bottom: 4rpx;
            }

            .message-date {
              display: flex;
              align-items: center;

              .date-icon {
                width: 24rpx;
                height: 24rpx;
                margin-right: 8rpx;
              }

              .date-text {
                color: #99999E;
                font-size: 24rpx;
              }
            }
          }
        }

        .arrow-icon {
          width: 16rpx;
          height: 16rpx;
          flex-shrink: 0;
        }
      }

      .message-content {
        .message-status-row {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 8rpx;

          .message-status {
            color: #33333E;
            font-size: 32rpx;
            font-weight: 500;
            max-width: 424rpx;
          }

          .message-time {
            color: #99999E;
            font-size: 24rpx;
            flex-shrink: 0;
            margin-top: 7rpx;
          }
        }

        .message-description {
          color: #33333E;
          font-size: 24rpx;
          line-height: 1.5;
        }
      }
    }
  }
}
