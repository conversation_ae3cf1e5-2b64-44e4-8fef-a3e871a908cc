/* 编辑模式底部操作栏组件样式 */

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  z-index: 1000;

  /* 左侧操作区 */
  .action-left {
    display: flex;
    align-items: center;

    .select-all-btn {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .checkbox-icon {
        width: 36rpx;
        height: 36rpx;
        transition: all 0.3s ease;

        /* 点击效果 */
        &:active {
          transform: scale(0.95);
        }
      }

      .select-all-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  /* 右侧操作区 */
  .action-right {
    .cancel-btn {
      background-color: #568ded;
      color: #fff;
      border: none;
      border-radius: 120rpx;
      padding: 24rpx 48rpx;
      font-size: 28rpx;
      line-height: 1;

      &:active {
        background-color: #4a7bd9;
      }

      &::after {
        border: none;
      }
    }
  }
}
