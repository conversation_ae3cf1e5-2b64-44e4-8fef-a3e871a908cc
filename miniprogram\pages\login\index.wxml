<!--pages/login/index.wxml-->
<view class="login-container">
  <!-- 头像区域 -->
  <view class="avatar-section">
    <image class="avatar-image" src="{{avatarDefaultIcon}}" mode="aspectFill"></image>
  </view>
  
  <!-- 协议区域 -->
  <view class="agreement-section">
    <view class="agreement-checkbox">
      <checkbox
        value="{{agreementChecked}}"
        bind:change="onAgreementChange"
      ></checkbox>
      <view class="agreement-text">
        <text>已阅读并同意</text>
        <text class="agreement-link" bind:tap="onUserAgreementClick">《用户服务协议》</text>
        <text>、</text>
        <text class="agreement-link" bind:tap="onPrivacyAgreementClick">《隐私协议》</text>
      </view>
    </view>
  </view>
  
  <!-- 登录按钮区域 -->
  <view class="login-section">
    <!-- 协议已勾选：手机号授权按钮 -->
    <button
      wx:if="{{agreementChecked}}"
      class="login-button active"
      open-type="getPhoneNumber"
      bind:getphonenumber="onGetPhoneNumber"
    >
      授权手机登录
    </button>

    <!-- 协议未勾选：提示按钮 -->
    <button
      wx:if="{{!agreementChecked}}"
      class="login-button active"
      bind:tap="onLoginClick"
    >
      授权手机登录
    </button>
  </view>
</view>

<!-- 用户信息填写弹框 -->
<user-info-modal
  visible="{{showUserInfoModal}}"
  bind:close="onUserInfoModalClose"
  bind:success="onUserInfoModalSuccess"
></user-info-modal>