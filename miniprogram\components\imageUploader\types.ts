/**
 * 图片上传组件类型定义
 */

/**
 * 上传接口函数类型
 */
export type UploadFunction = (filePaths: string[]) => Promise<{
  isSuccess: boolean;
  data: string[] | null;
  message: string;
}>;

/**
 * 组件属性接口
 */
export interface ImageUploaderProps {
  /** 是否显示标题 */
  showTitle?: boolean;
  /** 标题文本 */
  titleText?: string;
  /** 最大图片数量 */
  maxCount?: number;
  /** 上传按钮文本 */
  uploadText?: string;
  /** 上传中文本 */
  uploadingText?: string;
  /** 上传图标路径 */
  uploadIcon?: string;
  /** 删除图标路径 */
  deleteIcon?: string;
  /** 是否有自定义内容 */
  hasCustomContent?: boolean;
  /** 是否有自定义标题 */
  hasCustomTitle?: boolean;
  /** 自定义上传函数 */
  uploadFunction?: UploadFunction;
  value: string[]
}

/**
 * 组件事件接口
 */
export interface ImageUploaderEvents {
  /** 图片变化事件 */
  change: {
    images: string[];
    imageUrls: string[];
  };
  /** 上传成功事件 */
  uploadSuccess: {
    images: string[];
    imageUrls: string[];
  };
  /** 上传失败事件 */
  uploadError: {
    message: string;
  };
  /** 删除图片事件 */
  delete: {
    index: number;
    image: string;
    imageUrl: string;
  };
}
