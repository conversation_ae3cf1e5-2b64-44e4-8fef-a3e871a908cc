page {
	padding-bottom: 192rpx;
	height: 100%;
	box-sizing: border-box;
	overflow-y: auto;
	background-color: @bg-gray-color;
}

.main-body {
	padding: 24rpx;

	.place-item {
		padding: 32rpx 24rpx;
		border-radius: 32rpx;
		background: #fff;
		margin-top: 24rpx;
		font-size: 28rpx;
		line-height: 40rpx;
		&-title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			&-content {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: @text-title-color;
				font-weight: bold;
				&::before {
					content: "";
					width: 36rpx;
					height: 36rpx;
					background-image: url('@{static-base-url}/tripHelper/activity/place.png');
					background-size: 100%;
					margin-right: 8rpx;
				}
			}
			&-icon {
				width: 24rpx;
				height: 24rpx;
				background-image: url('@{static-base-url}/tripHelper/chooseRoute/deleteIcon.png');
				background-size: 100%;
			}
		}
		&-value {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 48rpx;
			&-label {
				color: @text-color;
			}
			&-content {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				&.is-placeholder {
					color: @text-light-color;
				}
				&::after {
					content: "";
					width: 24rpx;
					height: 24rpx;
					background-image: url('@{static-base-url}/arrow.png');
					background-size: 100%;
					margin-left: 24rpx;
				}
				.place-text {
					text-align: right;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 434rpx;
				}
			}
		}
	}

	.place-increase {
		display: flex;
		align-items: center;
		justify-content: center;
		background: #fff;
		padding: 32rpx 0;
		border-radius: 32rpx;
		margin-top: 24rpx;
		&::before {
			content: "";
			width: 32rpx;
			height: 32rpx;
			background-image: url('@{static-base-url}/tripHelper/activity/plus.png');
			background-size: 100%;
			margin-right: 16rpx;
	
		}
	}
}


.operate-wrap {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100vw;
	padding: 16rpx 32rpx 68rpx;
	background: #fff;
	display: flex;
	box-sizing: border-box;
	button {
		width: 100%;
		height: 108rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 108rpx;
		background: @primary-color;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}
}