// pages/order/list/index.ts
import { getOrderList, deleteOrder } from '../../../api/order';
import { store } from '../../../store/index';

/**
 * 订单状态映射
 */
const ORDER_STATUS_MAP: Record<number, string> = {
  0: '待付款',
  1: '未出行',
  2: '待点评',
  3: '已完成',
  4: '已取消',
  5: '退款中',
  6: '已退款'
};

/**
 * 格式化日期（API返回的日期格式已经是YYYY-MM-DD，直接返回）
 */
function formatDate(dateString: string): string {
  return dateString;
}

/**
 * 转换API返回的订单数据为页面显示数据
 */
function transformOrderItem(apiItem: Order.IOrderItem): Order.OrderItem {
  return {
    orderId: apiItem.id,
    orderNo: apiItem.orderNo,
    status: apiItem.orderStatus.toString(),
    statusText: apiItem.showOrderStatus || ORDER_STATUS_MAP[apiItem.orderStatus] || '未知状态',
    createTime: apiItem.orderCreateTime,
    payTime: apiItem.orderPayTime,
    actualAmount: parseFloat(apiItem.orderAmount),
    hotelName: apiItem.hotelName,
    checkInDate: formatDate(apiItem.arriveDate),
    checkOutDate: formatDate(apiItem.departDate),
    roomType: apiItem.roomTypeName,
    roomCount: apiItem.roomCount,
    nights: apiItem.nightCount
  };
}

/**
 * 转换订单列表数据
 */
function transformOrderList(apiData: Order.IOrderItem[]): Order.OrderList {
  return apiData.map(transformOrderItem);
}

/**
 * 构建订单列表查询参数
 */
function buildOrderListParams(
  pageIndex: number,
  pageSize: number,
  status?: string,
  keyword?: string,
  orderDate?: string
): Order.IOrderListReq {
  return {
    pageIndex,
    pageSize,
    status: status || '',
    keyword: keyword || '',
    orderDate: orderDate || ''
  };
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // Tab相关
    currentTab: 'all' as Order.TabType,
    tabs: [
      { label: '全部', value: 'all' as Order.TabType, status: '' },
      { label: '待付款', value: 'pending_payment' as Order.TabType, status: '0' },
      { label: '未出行', value: 'not_traveled' as Order.TabType, status: '1' },
      { label: '带点评', value: 'to_review' as Order.TabType, status: '2' }
    ] as Order.TabConfig[],

    // 列表数据
    orderLists: {
      all: [] as Order.OrderList,
      pending_payment: [] as Order.OrderList,
      not_traveled: [] as Order.OrderList,
      to_review: [] as Order.OrderList
    },

    // 分页参数
    pageParams: {
      all: { page: 1, hasMore: false },
      pending_payment: { page: 1, hasMore: false },
      not_traveled: { page: 1, hasMore: false },
      to_review: { page: 1, hasMore: false }
    } as Record<Order.TabType, Order.PageParams>,

    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false,

    // 搜索关键词
    searchKeyword: '',

    // 筛选数据
    filterData: {
      status: '',
      startTime: '',
      endTime: '',
      priceRange: '',
      starLevel: '',
      orderDate: '',
      type: ''
    },

    // 弹框显示状态
    showSearchPopup: false,
    showFilterPopup: false,

    // 空状态文案（将通过computed计算）
    emptyText: '暂无订单',
    emptyDesc: '您还没有相关订单哦~',

    // 头部高度
    headerHeight: 108, // 默认高度

  },

  /**
   * 获取当前Tab的订单列表（类似computed）
   */
  getCurrentOrderList(): Order.OrderList {
    return this.data.orderLists[this.data.currentTab] || [];
  },

  /**
   * 获取当前Tab的分页参数（类似computed）
   */
  getCurrentPageParams(): Order.PageParams {
    return this.data.pageParams[this.data.currentTab] || { page: 1, hasMore: true };
  },

  /**
   * 检查当前Tab是否有数据（类似computed）
   */
  hasCurrentTabData(): boolean {
    return this.getCurrentOrderList().length > 0;
  },

  /**
   * 获取动态空状态文案（类似computed）
   */
  getEmptyText(): string {
    const emptyTextMap = {
      all: '暂无订单',
      pending_payment: '暂无待付款订单',
      not_traveled: '暂无未出行订单',
      to_review: '暂无待点评订单'
    };
    return emptyTextMap[this.data.currentTab] || '暂无订单';
  },

  /**
   * 获取动态空状态描述（类似computed）
   */
  getEmptyDesc(): string {
    const emptyDescMap = {
      all: '您还没有任何订单哦~',
      pending_payment: '您还没有待付款的订单~',
      not_traveled: '您还没有未出行的订单~',
      to_review: '您还没有待点评的订单~'
    };
    return emptyDescMap[this.data.currentTab] || '您还没有相关订单哦~';
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: { tab?: string }) {
    console.log('订单列表页面加载，参数:', options);

    // 计算头部高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    const headerHeight = statusBarHeight + 44; // 状态栏高度 + 头部内容高度(88rpx/2)

    this.setData({
      headerHeight: headerHeight * 2 // 转换为rpx
    });

    // 根据URL参数设置初始Tab，修正Tab值映射
    let initialTab: Order.TabType = 'all';

    if (options.tab) {
      // 映射用户中心的订单状态ID到订单列表的Tab值
      const tabMapping: Record<string, Order.TabType> = {
        'all_order': 'all',
        'pending_payment': 'pending_payment',
        'not_traveling_yet': 'not_traveled',
        'refund': 'to_review'
      };

      initialTab = tabMapping[options.tab] || options.tab as Order.TabType || 'all';
    }

    console.log('设置初始Tab:', initialTab);

    // 确保设置正确的Tab
    this.setData({
      currentTab: initialTab
    });

    console.log('Tab设置完成，当前Tab:', this.data.currentTab);



    // 加载对应Tab的数据
    console.log('准备加载数据，当前Tab:', initialTab);
    this.loadData(initialTab);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时可以选择性刷新数据
    // 这里暂时不自动刷新，避免频繁请求
    console.log('订单列表页面显示');
  },

  /**
   * Tab点击事件
   */
  onTabClick(e: WechatMiniprogram.BaseEvent) {
    const { value } = e.currentTarget.dataset;

    // 如果点击的是当前Tab，不做处理
    if (value === this.data.currentTab) return;

    console.log('切换Tab:', value);

    this.setData({
      currentTab: value
    });

    // 如果该Tab没有数据，则加载数据
    if (this.data.orderLists[value].length === 0) {
      this.loadData(value);
    }
  },

  /**
   * 搜索框点击事件
   */
  onSearchClick() {
    console.log('搜索框被点击，准备显示搜索弹框');
    console.log('设置前的状态:', this.data.showSearchPopup);
    this.setData({
      showSearchPopup: true
    });
    console.log('设置后的状态:', this.data.showSearchPopup);

    // 延迟检查状态是否真的改变了
    setTimeout(() => {
      console.log('延迟检查状态:', this.data.showSearchPopup);
    }, 100);
  },

  /**
   * 筛选按钮点击事件
   */
  onFilterClick() {
    this.setData({
      showFilterPopup: true
    });
  },

  /**
   * 搜索事件
   */
  onSearch(e: WechatMiniprogram.CustomEvent<{ keyword: string; status?: string }>) {
    const { keyword, status } = e.detail;
    console.log('搜索参数:', { keyword, status });

    // 更新搜索关键词和筛选状态
    this.setData({
      searchKeyword: keyword || '',
      [`filterData.type`]: status || '' // 将分类状态设置到筛选数据中
    });

    // 重置当前Tab的分页参数并重新加载数据
    this.setData({
      [`pageParams.${this.data.currentTab}.page`]: 1,
      [`pageParams.${this.data.currentTab}.hasMore`]: false,
      [`orderLists.${this.data.currentTab}`]: []
    });

    this.loadData(this.data.currentTab);
  },

  /**
   * 筛选事件
   */
  onFilter(e: WechatMiniprogram.CustomEvent<Order.FilterFormData>) {
    const filterFormData = e.detail;
    console.log('筛选条件:', filterFormData);

    // 将筛选表单数据转换为页面使用的格式
    const filterData = {
      status: filterFormData.orderStatus || '',
      startTime: '',
      endTime: '',
      priceRange: '',
      starLevel: '',
      orderDate: filterFormData.orderDate || '',
      type: filterFormData.type || ''
    };

    this.setData({
      filterData,
      showFilterPopup: false
    }); 

    // 重置当前Tab的分页参数并重新加载数据
    this.setData({
      [`pageParams.${this.data.currentTab}.page`]: 1,
      [`pageParams.${this.data.currentTab}.hasMore`]: true,
      [`orderLists.${this.data.currentTab}`]: []
    });

    this.loadData(this.data.currentTab);
  },

  /**
   * 搜索弹框关闭事件
   */
  onSearchPopupClose() {
    this.setData({
      showSearchPopup: false
    });
  },

  /**
   * 筛选弹框关闭事件
   */
  onFilterPopupClose() {
    this.setData({
      showFilterPopup: false
    });
  },

  /**
   * 返回事件
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    console.log('下拉刷新触发:', this.data.currentTab);
    
    // 重置分页参数
    this.setData({
      [`pageParams.${this.data.currentTab}.page`]: 1,
      [`pageParams.${this.data.currentTab}.hasMore`]: true,
      refreshing: true
    });

    // 重新加载数据
    this.loadData(this.data.currentTab, false, true);
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    const currentTab = this.data.currentTab;
    console.log('上拉加载更多触发:', currentTab);

    // 增加页码
    const newPage = this.data.pageParams[currentTab].page + 1;
    this.setData({
      [`pageParams.${currentTab}.page`]: newPage
    });

    // 加载更多数据
    this.loadData(currentTab, true);
  },

  /**
   * 加载数据
   */
  async loadData(tabValue: Order.TabType, isLoadMore = false, isRefresh = false) {
    try {
      console.log('=== 开始加载数据 ===');
      console.log('加载数据:', { tabValue, isLoadMore, isRefresh });
      console.log('当前页面数据状态:', {
        currentTab: this.data.currentTab,
        tabs: this.data.tabs,
        pageParams: this.data.pageParams
      });

      // 设置加载状态
      if (isLoadMore) {
        this.setData({ loadingMore: true });
      } else if (!isRefresh) {
        this.setData({ loading: true });
      }

      const pageParams = this.data.pageParams[tabValue];
      const currentTab = this.data.tabs.find(tab => tab.value === tabValue);

      console.log('找到的Tab配置:', currentTab);
      console.log('当前分页参数:', pageParams);

      // 构建请求参数
      // 如果有筛选条件中的状态，优先使用筛选条件的状态，否则使用Tab的状态
      const filterStatus = this.data.filterData.status;
      const finalStatus = filterStatus || currentTab?.status || '';

      // 处理时间筛选，优先使用筛选条件中的orderDate
      const orderDate = this.data.filterData.orderDate ||
        (this.data.filterData.startTime ?
          new Date(this.data.filterData.startTime).getFullYear().toString() : '');

      const requestParams = buildOrderListParams(
        pageParams.page,
        10, // pageSize
        finalStatus,
        this.data.searchKeyword,
        orderDate
      );

      console.log('构建的请求参数:', requestParams);
      console.log('准备调用API...');

      // 调用真实API
      const response = await getOrderList(requestParams);

      console.log('API响应:', response);
      console.log('响应状态:', {
        isSuccess: response.isSuccess,
        code: response.code,
        success: response.data?.success,
        dataLength: response.data?.data?.length
      });
      console.log('完整的response.data:', response.data);

      // 修复条件判断：根据实际API响应结构处理
      if (response.isSuccess && response.code === 200) {
        // 检查response.data的结构
        let apiOrders;
        if (Array.isArray(response.data)) {
          // 如果data直接是数组
          apiOrders = response.data;
        } else if (response.data && Array.isArray(response.data.data)) {
          // 如果data是对象，包含data字段
          apiOrders = response.data.data;
        } else {
          console.error('未知的API响应结构:', response);
          throw new Error('API响应结构异常');
        }
        console.log('API返回的原始订单数据:', apiOrders);

        const transformedOrders = transformOrderList(apiOrders);
        console.log('转换后的订单数据:', transformedOrders);

        // 判断是否有更多数据（如果返回的数据少于pageSize，说明没有更多了）
        const hasMore = apiOrders.length >= 10;
        console.log('是否有更多数据:', hasMore, '数据长度:', apiOrders.length);

        if (isLoadMore) {
          // 上拉加载更多，追加数据
          console.log('执行上拉加载更多逻辑');
          this.setData({
            [`orderLists.${tabValue}`]: [...this.data.orderLists[tabValue], ...transformedOrders],
            [`pageParams.${tabValue}.hasMore`]: hasMore,
            loadingMore: false
          });
        } else {
          // 初始加载或下拉刷新，替换数据
          console.log('执行初始加载/下拉刷新逻辑');
          console.log('准备设置的数据:', {
            [`orderLists.${tabValue}`]: transformedOrders,
            [`pageParams.${tabValue}.hasMore`]: hasMore,
            loading: false,
            refreshing: false
          });

          this.setData({
            [`orderLists.${tabValue}`]: transformedOrders,
            [`pageParams.${tabValue}.hasMore`]: hasMore,
            loading: false,
            refreshing: false
          });

          // 验证数据是否正确设置
          console.log('数据设置后验证:', {
            currentTab: this.data.currentTab,
            targetTab: tabValue,
            orderCount: this.data.orderLists[tabValue]?.length,
            orderData: this.data.orderLists[tabValue]
          });
        }

        console.log('=== 数据加载完成 ===');
        console.log('Tab:', tabValue, '数据数量:', transformedOrders.length);
        console.log('更新后的页面数据:', {
          currentTab: this.data.currentTab,
          orderLists: this.data.orderLists,
          pageParams: this.data.pageParams
        });

        // 强制触发页面更新
        this.setData({});

        console.log('页面数据设置完成，当前显示的订单数量:', this.data.orderLists[tabValue]?.length || 0);
      } else {
        console.error('API调用失败:', response);
        throw new Error(response.data?.message || '获取订单列表失败');
      }
    } catch (error) {
      console.error('加载订单列表失败:', error);

      // 重置加载状态
      this.setData({
        loading: false,
        refreshing: false,
        loadingMore: false
      });

      // 显示错误提示
      wx.displayToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 订单卡片点击事件
   */
  onOrderClick(e: WechatMiniprogram.CustomEvent<{ orderData: Order.OrderItem }>) {
    const { orderData } = e.detail;
    console.log('点击订单:', orderData);
    
    // 跳转到订单详情页面
    wx.navigateTo({
      url: `/pages/order/detail/index?orderId=${orderData.orderId}`
    });
  },

  /**
   * 订单操作按钮点击事件
   */
  onActionClick(e: WechatMiniprogram.CustomEvent<{ action: string; orderData: Order.OrderItem }>) {
    const { action, orderData } = e.detail;
    console.log('订单操作:', action, orderData);
    
    switch (action) {
      case 'pay':
        // 去支付
        this.handlePayment(orderData);
        break;
      case 'rebook':
        // 再次预订
        this.handleRebook(orderData);
        break;
      case 'cancel':
        // 取消订单
        this.handleCancel(orderData);
        break;
      case 'delete':
        // 删除订单
        this.handleDelete(orderData);
        break;
      default:
        console.log('未知操作:', action);
    }
  },

  /**
   * 处理支付
   */
  handlePayment(orderData: Order.OrderItem) {
    wx.navigateTo({
      url: `/pages/pay/cashier/index?orderNo=${orderData.orderNo}`
    });
  },

  /**
   * 处理再次预订
   */
  handleRebook(orderData: Order.OrderItem) {
    wx.displayToast({
      title: '再次预订功能开发中',
      icon: 'none'
    });
  },

  /**
   * 处理取消订单
   */
  handleCancel(orderData: Order.OrderItem) {
    wx.displayToast({
      title: '取消订单功能开发中',
      icon: 'none'
    });
  },

  /**
   * 处理删除订单
   */
  handleDelete(orderData: Order.OrderItem) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订单吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteOrderRequest(orderData);
        }
      }
    });
  },

  /**
   * 删除订单请求
   */
  async deleteOrderRequest(orderData: Order.OrderItem) {
    try {
      wx.displayLoading({
        title: '删除中...',
        mask: true
      });

      // 调用真实删除API
      console.log('删除订单:', orderData.orderNo);

      const response = await deleteOrder(orderData.orderNo);

      if (response.isSuccess && response.data.success) {
        // 删除成功，从当前列表中移除该订单
        const currentTab = this.data.currentTab;
        const currentList = this.data.orderLists[currentTab];
        const updatedList = currentList.filter(item => item.orderId !== orderData.orderId);

        this.setData({
          [`orderLists.${currentTab}`]: updatedList
        });

        wx.hidePrevLoading();
        wx.displayToast({
          title: '删除成功',
          icon: 'success',
          duration: 2000
        });
      } else {
        throw new Error(response.data?.message || '删除订单失败');
      }
    } catch (error) {
      console.error('删除订单失败:', error);
      wx.hidePrevLoading();
      wx.displayToast({
        title: '删除失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('页面onReachBottom触发');

    const currentTab = this.data.currentTab;
    const pageParams = this.data.pageParams[currentTab];

    // 检查是否可以加载更多
    if (this.data.loadingMore || !pageParams.hasMore || this.data.loading) {
      console.log('上拉加载被阻止:', {
        loadingMore: this.data.loadingMore,
        hasMore: pageParams.hasMore,
        loading: this.data.loading
      });
      return;
    }

    // 增加页码
    const newPage = pageParams.page + 1;
    this.setData({
      [`pageParams.${currentTab}.page`]: newPage
    });

    // 加载更多数据
    this.loadData(currentTab, true);
  }
});
