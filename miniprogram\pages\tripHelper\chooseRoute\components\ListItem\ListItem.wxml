<!-- pages/tripHelper/chooseRoute/components/ListItem/ListItem.wxml -->
<view class="list-item">
  <!-- 左侧选择按钮 -->
  <view class="left-section" bind:tap="onSelectClick">
    <image class="select-icon" src="{{selected ? selectedIcon : noSelectIcon}}" mode="aspectFit" />
  </view>

  <!-- 右侧内容区域 -->
  <view class="right-section" bind:tap="onContentClick">
    <!-- 路线名称和箭头 -->
    <view class="route-header">
      <text class="route-name">{{routeData.name}}</text>
      <t-icon name="chevron-right" size="32rpx" color="#999" />
    </view>

    <!-- 三张图片 -->
    <view class="route-images">
      <image class="route-img" wx:for="{{routeData.picUrls}}" wx:key="index" src="{{item}}" mode="aspectFill" />
    </view>

    <!-- 道友头像和打卡信息 -->
    <view class="friends-section">
      <view class="friends-avatars" wx:if="{{routeData.userAvatars && routeData.userAvatars.length > 0}}">
        <image class="friend-avatar" wx:for="{{routeData.userAvatars}}" wx:key="index" src="{{item}}" mode="aspectFill" style="z-index: {{routeData.friendAvatars.length + index}}" />
      </view>
      <text class="friends-count" wx:if="{{routeData.totalCheckIn > 0}}">已有{{routeData.totalCheckIn}}位道友打卡</text>
    </view>


  </view>
  <!-- 已打卡标识 -->
  <image class="punched-icon" wx:if="{{routeData.ifCheckIn}}" src="{{punchedIcon}}" mode="aspectFit" />
</view>