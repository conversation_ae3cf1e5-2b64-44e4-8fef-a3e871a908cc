/* pages/hotel/hotelList/components/ExpandPopup/ExpandPopup.wxss */

.expand-popup {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  overflow: hidden;
}

.popup-mask {
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1001;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &.show {
    opacity: 1;
  }
  
  &.hide {
    opacity: 0;
  }
}



.popup-content {
  position: relative;
  background-color: #fff;
  border-radius: 0 0 16rpx 16rpx;
  overflow-y: auto;
  height: 0;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  z-index: 1002;
  
  &.show {
    height: auto;
    opacity: 1;
  }
  
  &.hide {
    height: 0;
    opacity: 0;
  }
  
  // 最小高度和最大高度
  min-height: 0;
  max-height: 70vh;
  
  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6rpx;
  }
  
  &::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3rpx;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3rpx;
    
    &:hover {
      background: #999;
    }
  }
}

/* 动画状态类 */
.expand-popup.visible {
  // .popup-mask.show {
  //   opacity: 1;
  // }
  
  .popup-content {
    height: auto;
    opacity: 1;
  }
}

// .expand-popup.hidden {
//   .popup-mask.hide {
//     opacity: 0;
//   }
  
//   .popup-content.hide {
//     height: 0;
//     opacity: 0;
//     transform: translateY(-20rpx);
//   }
// }

/* 响应式适配 */
// @media (max-height: 667px) {
//   .popup-content {
//     max-height: 60vh;
//   }
// }

// @media (max-height: 568px) {
//   .popup-content {
//     max-height: 50vh;
//   }
// }

/* 内容区域样式优化 */
.popup-content {
  // 确保内容区域有合适的边距
  .expand-content {
    padding: 0;
  }
}

/* 为了确保动画效果，使用固定高度的变体 */
.popup-content.show {
  // 如果内容高度已知，可以使用具体数值
  // height: 400rpx; // 根据实际内容调整
}

/* 优化展开动画的性能 */
.popup-content {
  will-change: height, opacity;
  backface-visibility: hidden;
}