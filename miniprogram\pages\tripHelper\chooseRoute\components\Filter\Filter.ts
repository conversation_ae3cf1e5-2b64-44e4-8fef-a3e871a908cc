// pages/tripHelper/chooseRoute/components/Filter/Filter.ts

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 城市周边选中标签
    cityLabel: {
      type: Object,
      value: { key: '', value: '' }
    },
    // 出行天数选中标签
    daysLabel: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 各筛选项的展开状态
    cityExpanded: false,
    daysExpanded: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 统一的展开状态管理方法
    setExpandState(targetType: string) {
      const currentState = this.data[`${targetType}Expanded` as keyof typeof this.data];
      const newExpandedState = !currentState;
      
      // 设置所有展开状态，确保互斥
      this.setData({
        cityExpanded: targetType === 'city' ? newExpandedState : false,
        daysExpanded: targetType === 'days' ? newExpandedState : false
      });
      
      return newExpandedState;
    },

    // 点击城市周边
    onCityClick() {
      const expanded = this.setExpandState('city');
      this.triggerEvent('cityClick', { expanded, type: 'city' });
    },

    // 点击出行天数
    onDaysClick() {
      const expanded = this.setExpandState('days');
      this.triggerEvent('daysClick', { expanded, type: 'days' });
    },

    // 重置所有展开状态（供页面调用）
    resetExpandedStates() {
      this.setData({
        cityExpanded: false,
        daysExpanded: false
      });
    }
  }
})