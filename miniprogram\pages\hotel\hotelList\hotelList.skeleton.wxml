<!--
此文件为开发者工具生成，生成时间: 2025/6/24下午2:02:33
使用方法：
在 /Users/<USER>/workSpace/ota-wx-mini-web/miniprogram/pages/hotel/hotelList/hotelList.wxml 引入模板

```
<import src="hotelList.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 /Users/<USER>/workSpace/ota-wx-mini-web/miniprogram/pages/hotel/hotelList/hotelList.wxss 中引入样式
```
@import "./hotelList.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view class="hotelListPage">
      <view is="pages/hotel/hotelList/components/NavBar/NavBar">
        <view class="navbar-container NavBar--navbar-container" style="height: 94px;">
          <view class="navbar-placeholder NavBar--navbar-placeholder" style="height: 54px;"></view>
          <view class="navbar-content NavBar--navbar-content" style="height: 40px; padding-right: 94px;">
            <view class="navbar-left NavBar--navbar-left" style="height: 32px; margin-top: 4px;">
              <image class="navbar-back-icon NavBar--navbar-back-icon sk-image" mode="aspectFit|aspectFill|widthFix"></image>
            </view>
            <view class="navbar-center NavBar--navbar-center" style="height: 32px; margin-top: 4px;">
              <view class="navbar-city-wrapper NavBar--navbar-city-wrapper">
                <text class="navbar-city-name NavBar--navbar-city-name sk-transparent sk-text-14-2857-551 sk-text">杭州市</text>
                <view class="navbar-city-arrow NavBar--navbar-city-arrow"></view>
              </view>
            </view>
            <view class="navbar-right NavBar--navbar-right"></view>
          </view>
        </view>
      </view>
      <view is="pages/hotel/hotelList/components/Search/search">
        <view class="search--search-container">
          <view class="search--search-row">
            <view class="search--search-box">
              <view class="search--date-selector">
                <view class="search--date-item">
                  <text class="search--date-label sk-transparent sk-opacity">住</text>
                  <text class="search--date-value sk-transparent sk-text-14-2857-173 sk-text">06-23</text>
                </view>
                <view class="search--date-item">
                  <text class="search--date-label sk-transparent sk-opacity">离</text>
                  <text class="search--date-value sk-transparent sk-text-14-2857-34 sk-text">今天</text>
                </view>
              </view>
              <view class="search--date-separator"></view>
              <view class="search--search-input">
                <view class="search--search-icon sk-pseudo sk-pseudo-circle"></view>
                <text class="search--search-placeholder sk-transparent sk-text-14-2857-304 sk-text">位置/品牌/酒店</text>
              </view>
            </view>
            <view class="search--action-buttons">
              <view class="search--map-btn">
                <image class="search--map-icon sk-image" mode="aspectFit"></image>
                <text class="search--action-text sk-transparent sk-text-14-2857-845 sk-text">地图</text>
              </view>
              <view class="search--favorite-btn">
                <image class="search--favorite-icon sk-image" mode="aspectFit"></image>
                <text class="search--action-text sk-transparent sk-text-14-2857-19 sk-text">收藏</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view is="pages/hotel/hotelList/components/Filter/Filter" id="filter-component">
        <view class="Filter--filter-container">
          <view class="Filter--filter-item Filter--active">
            <text class="Filter--filter-text Filter--sort-label sk-transparent sk-text-14-2857-940 sk-text">智能排序</text>
            <view class="t-icon icon--t-icon Filter--filter-arrow " style="font-size: 18px;">
              <label class="t-icon-chevron-down icon--t-icon-chevron-down t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
            </view>
          </view>
          <view class="Filter--filter-item">
            <text class="Filter--filter-text sk-transparent sk-text-14-2857-926 sk-text">价格/星级</text>
            <view class="Filter--filter-content">
              <view class="t-icon icon--t-icon Filter--filter-arrow " style="font-size: 18px;">
                <label class="t-icon-chevron-down icon--t-icon-chevron-down t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
              </view>
            </view>
          </view>
          <view class="Filter--filter-item">
            <text class="Filter--filter-text Filter--distance-label sk-transparent sk-text-14-2857-707 sk-text">位置距离</text>
            <view class="Filter--filter-content">
              <view class="t-icon icon--t-icon Filter--filter-arrow " style="font-size: 18px;">
                <label class="t-icon-chevron-down icon--t-icon-chevron-down t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
              </view>
            </view>
          </view>
          <view class="Filter--filter-item">
            <text class="Filter--filter-text sk-transparent sk-text-14-2857-408 sk-text">筛选</text>
            <view class="Filter--filter-content">
              <view class="t-icon icon--t-icon Filter--filter-arrow " style="font-size: 18px;">
                <label class="t-icon-chevron-down icon--t-icon-chevron-down t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view is="pages/hotel/hotelList/components/ExpandPopup/ExpandPopup">
        <view style="position: relative;"></view>
      </view>
      <view is="pages/hotel/hotelList/components/QuickFilter/QuickFilter"></view>
      <scroll-view class="hotelList-content" lower-threshold="50" refresher-background="#f5f5f5" refresher-default-style="none" refresher-enabled="true" scroll-y="true">
        <view class="custom-refresher">
          <image class="plane-icon pulling sk-image" style="transform: scale(0.8)"></image>
        </view>
        <view is="pages/hotel/hotelList/components/hotelItem/hotelItem">
          <view class="hotelItem--hotel-item">
            <view class="hotelItem--hotel-image-wrapper">
              <image class="hotelItem--hotel-image hotelItem--loaded sk-image" lazy-load="true" mode="aspectFill" webp="true"></image>
            </view>
            <view class="hotelItem--hotel-info">
              <view class="hotelItem--hotel-name sk-transparent sk-text-14-2857-431 sk-text">
                杭州临平瑞莱克斯大酒店
              </view>
              <view class="hotelItem--rating-row">
                <view class="hotelItem--rating-score sk-transparent sk-text-14-2857-631 sk-text">
                  4.8
                </view>
              </view>
              <view class="hotelItem--location-info">
                <view class="sk-transparent sk-text-14-2857-137 sk-text">临平区</view>
                <view class="hotelItem--business-zone-name sk-transparent sk-text-14-2857-863 sk-text">
                  靠近临平新城
                </view>
              </view>
              <view class="hotelItem--features-row">
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-257 sk-text">
                  行政楼层
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-577 sk-text">
                  会议厅
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-15 sk-text">
                  亲子酒店
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-539 sk-text">
                  干衣机
                </view>
              </view>
              <view class="hotelItem--price-info">
                <text class="hotelItem--currency-symbol sk-transparent sk-opacity">¥</text>
                <text class="hotelItem--price sk-transparent sk-text-14-2857-997 sk-text">351</text>
                <text class="hotelItem--price-unit sk-transparent sk-opacity">起</text>
              </view>
            </view>
          </view>
        </view>
        <view is="pages/hotel/hotelList/components/hotelItem/hotelItem">
          <view class="hotelItem--hotel-item">
            <view class="hotelItem--hotel-image-wrapper">
              <image class="hotelItem--hotel-image hotelItem--loaded sk-image" lazy-load="true" mode="aspectFill" webp="true"></image>
            </view>
            <view class="hotelItem--hotel-info">
              <view class="hotelItem--hotel-name sk-transparent sk-text-14-2857-362 sk-text">
                杭州金沙湖和达希尔顿嘉悦里酒店
              </view>
              <view class="hotelItem--rating-row">
                <view class="hotelItem--rating-score sk-transparent sk-text-14-2857-58 sk-text">
                  4.8
                </view>
              </view>
              <view class="hotelItem--location-info">
                <view class="sk-transparent sk-text-14-2857-77 sk-text">钱塘区</view>
                <view class="hotelItem--business-zone-name sk-transparent sk-text-14-2857-250 sk-text">
                  靠近九堡客运中心区域
                </view>
              </view>
              <view class="hotelItem--features-row">
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-582 sk-text">
                  可携带宠物
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-595 sk-text">
                  机器人服务
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-689 sk-text">
                  园林建筑
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-673 sk-text">
                  精品酒店（设计师酒店）
                </view>
              </view>
              <view class="hotelItem--price-info">
                <text class="hotelItem--currency-symbol sk-transparent sk-opacity">¥</text>
                <text class="hotelItem--price sk-transparent sk-text-14-2857-629 sk-text">698</text>
                <text class="hotelItem--price-unit sk-transparent sk-opacity">起</text>
              </view>
            </view>
          </view>
        </view>
        <view is="pages/hotel/hotelList/components/hotelItem/hotelItem">
          <view class="hotelItem--hotel-item">
            <view class="hotelItem--hotel-image-wrapper">
              <image class="hotelItem--hotel-image hotelItem--loaded sk-image" lazy-load="true" mode="aspectFill" webp="true"></image>
            </view>
            <view class="hotelItem--hotel-info">
              <view class="hotelItem--hotel-name sk-transparent sk-text-14-2857-915 sk-text">
                杭州康莱德酒店
              </view>
              <view class="hotelItem--rating-row">
                <view class="hotelItem--rating-score sk-transparent sk-text-14-2857-506 sk-text">
                  4.7
                </view>
              </view>
              <view class="hotelItem--location-info">
                <view class="sk-transparent sk-text-14-2857-324 sk-text">上城区</view>
                <view class="hotelItem--business-zone-name sk-transparent sk-text-14-2857-755 sk-text">
                  靠近钱江新城
                </view>
              </view>
              <view class="hotelItem--features-row">
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-909 sk-text">
                  园林建筑
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-736 sk-text">
                  精品酒店（设计师酒店）
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-514 sk-text">
                  美食酒店
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-280 sk-text">
                  近地铁
                </view>
              </view>
              <view class="hotelItem--price-info">
                <text class="hotelItem--currency-symbol sk-transparent sk-opacity">¥</text>
                <text class="hotelItem--price sk-transparent sk-text-14-2857-125 sk-text">1417</text>
                <text class="hotelItem--price-unit sk-transparent sk-opacity">起</text>
              </view>
            </view>
          </view>
        </view>
        <view is="pages/hotel/hotelList/components/hotelItem/hotelItem">
          <view class="hotelItem--hotel-item">
            <view class="hotelItem--hotel-image-wrapper">
              <image class="hotelItem--hotel-image hotelItem--loaded sk-image" lazy-load="true" mode="aspectFill" webp="true"></image>
            </view>
            <view class="hotelItem--hotel-info">
              <view class="hotelItem--hotel-name sk-transparent sk-text-14-2857-766 sk-text">
                杭州拱墅城北希尔顿欢朋酒店
              </view>
              <view class="hotelItem--rating-row">
                <view class="hotelItem--rating-score sk-transparent sk-text-14-2857-798 sk-text">
                  4.9
                </view>
              </view>
              <view class="hotelItem--location-info">
                <view class="sk-transparent sk-text-14-2857-746 sk-text">拱墅区</view>
                <view class="hotelItem--business-zone-name sk-transparent sk-text-14-2857-834 sk-text">
                  靠近城北/半山地区
                </view>
              </view>
              <view class="hotelItem--features-row">
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-845 sk-text">
                  会议厅
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-332 sk-text">
                  亲子酒店
                </view>
                <view class="hotelItem--feature-tag sk-transparent sk-text-14-2857-107 sk-text">
                  酒吧
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>