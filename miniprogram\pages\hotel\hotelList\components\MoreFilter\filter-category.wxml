<!-- 筛选类目内容模板 -->
<template name="filter-category">
  <!-- 一级标题 -->
  <view class="category-title level-1">
    <text>{{item.name}}</text>
    <!-- 二级结构时，如果子项超过6个，在一级标题后显示展开按钮 -->
    <view 
      wx:if="{{utils.isTwoLevelStructure(item) && item.subFilters.length > 6}}"
      class="expand-btn"
      data-type-id="{{item.typeId}}"
      data-level="items"
      bindtap="toggleExpand"
    >
      <view class="expand-text">
        {{utils.isExpanded(item.typeId, 'items', expandStates) ? '收起' : '展开'}}
      </view>
      <t-icon 
        name="chevron-down" 
        size="24rpx" 
        class="expand-icon {{utils.isExpanded(item.typeId, 'items', expandStates) ? 'expanded' : ''}}"
      />
    </view>
  </view>
  <!-- 三级结构：二级作为标题，三级为选项 -->
  <block wx:if="{{utils.isThreeLevelStructure(item)}}">
    <view 
      wx:for="{{item.subFilters}}" 
      wx:for-item="subItem"
      wx:key="index"
      class="filter-group"
    >
      <!-- 二级标题 -->
      <view class="category-title level-2">
        <text>{{subItem.name}}</text>
        <view 
          wx:if="{{subItem.subFilters && subItem.subFilters.length > 6}}"
          class="expand-btn"
          data-type-id="{{item.typeId}}"
          data-level="{{subItem.filterId}}"
          bindtap="toggleExpand"
        >
          <view class="expand-text">
            {{utils.isExpanded(item.typeId, subItem.filterId, expandStates) ? '收起' : '展开'}}
          </view>
          <t-icon 
            name="chevron-down" 
            size="28rpx" 
            class="expand-icon {{utils.isExpanded(item.typeId, subItem.filterId, expandStates) ? 'expanded' : ''}}"
          />
        </view>
      </view>
      
      <!-- 三级选项 -->
      <view class="options-grid" wx:if="{{subItem.subFilters && subItem.subFilters.length > 0}}">
        <view 
          wx:for="{{utils.getDisplayItems(subItem.subFilters, item.typeId, subItem.filterId, expandStates)}}" 
          wx:for-item="option"
          wx:key="index"
          class="option-item {{utils.isOptionSelected(option, internalSelectedFilters) ? 'selected' : ''}}"
          data-option="{{option}}"
          data-parent-option="{{subItem}}"
          bindtap="onOptionClick"
        >
          {{option.name}}
        </view>
      </view>
    </view>
  </block>

  <!-- 二级结构：二级直接为选项 -->
  <block wx:elif="{{utils.isTwoLevelStructure(item)}}">
    <view class="options-grid">
      <view 
        wx:for="{{utils.getDisplayItems(item.subFilters, item.typeId, 'items', expandStates)}}" 
        wx:for-item="option"
        wx:key="index"
        class="option-item {{utils.isOptionSelected(option, internalSelectedFilters) ? 'selected' : ''}}"
        data-option="{{option}}"
        data-parent-option="{{item}}"
        bindtap="onOptionClick"
      >
        {{option.name}}
      </view>
    </view>
  </block>
</template> 

<wxs module="utils">
  // 检查是否展开
  function isExpanded(typeId, level, expandStates) {
    if (!expandStates) return false;
    var expandKey = typeId + '_' + level;
    return !!expandStates[expandKey];
  }
  
  // 检查选项是否选中
  function isOptionSelected(option, selectedFilters) {
    if (!selectedFilters || !option) return false;
    
    for (var i = 0; i < selectedFilters.length; i++) {
      var selected = selectedFilters[i];
      if (selected.typeId === option.typeId && selected.filterId === option.filterId) {
        return true;
      }
    }
    return false;
  }
  
  // 获取显示的选项列表（处理展开收起）
  function getDisplayItems(items, typeId, level, expandStates) {
    if (!items || items.length <= 6) {
      return items || [];
    }
    
    var expandKey = typeId + '_' + level;
    var isExpanded = expandStates && expandStates[expandKey];
    
    if (isExpanded) {
      return items;
    } else {
      return items.slice(0, 6);
    }
  }
  
  // 检查是否为三级结构
  function isThreeLevelStructure(item) {
    return item.subFilters && item.subFilters.length > 0 && 
           item.subFilters[0].subFilters && item.subFilters[0].subFilters.length > 0;
  }
  
  // 检查是否为二级结构
  function isTwoLevelStructure(item) {
    return item.subFilters && item.subFilters.length > 0 && 
           (!item.subFilters[0].subFilters || item.subFilters[0].subFilters.length === 0);
  }
  
  module.exports = {
    isExpanded: isExpanded,
    isOptionSelected: isOptionSelected,
    getDisplayItems: getDisplayItems,
    isThreeLevelStructure: isThreeLevelStructure,
    isTwoLevelStructure: isTwoLevelStructure
  };
</wxs>