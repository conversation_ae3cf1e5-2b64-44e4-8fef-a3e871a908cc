Component({
  properties: {
    // 控制弹窗开关
    visible: {
      type: Boolean,
      value: false
    }
  },
  data:{
    refundList: [
      { time: "5月24日 00:00前", detail: "100%" },
      { time: "5月24日00:00~5月25日00:00前", detail: "30%" },
      { time: "5月25日 00:00后", detail: "0%，自行协商" }
    ]
  },
  methods: {
    stopPropagation(){},
    closePopClick(){
      this.triggerEvent('close', { visible:false, type: 'refundPolicy'})
    }
  }
})