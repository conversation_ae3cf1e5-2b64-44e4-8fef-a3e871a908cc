Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    extClass: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    // === 主题预设 ===
    theme: {
      type: String,
      value: 'default', // default | light | dark | transparent
      observer: '_themeChange'
    },
    // === 背景自定义 ===
    background: {
      type: String,
      value: '',
      observer: '_updateStyles'
    },
    // === 文字颜色自定义 ===
    color: {
      type: String,
      value: '',
      observer: '_updateStyles'
    },
    // === 返回按钮配置 ===
    back: {
      type: Boolean,
      value: true
    },
    backIcon: {
      type: String,
      value: '', // 自定义返回图标路径
    },
    backIconColor: {
      type: String,
      value: '', // 返回图标颜色：white | black | 自定义颜色值
      observer: '_updateStyles'
    },
    // === 标题配置 ===
    titleAlign: {
      type: String,
      value: 'center' // center | left
    },
    titleSize: {
      type: String,
      value: '17px'
    },
    titleWeight: {
      type: String,
      value: 'bold' // normal | bold | 500 | 600 等
    },
    // === 其他功能 ===
    loading: {
      type: Boolean,
      value: false
    },
    homeButton: {
      type: Boolean,
      value: false,
    },
    animated: {
      // 显示隐藏的时候opacity动画效果
      type: Boolean,
      value: true
    },
    show: {
      // 显示隐藏导航，隐藏的时候navigationBar的高度占位还在
      type: Boolean,
      value: true,
      observer: '_showChange'
    },
    // back为true的时候，返回的页面深度
    delta: {
      type: Number,
      value: 1
    },
    // === 边框配置 ===
    borderBottom: {
      type: Boolean,
      value: false
    },
    borderColor: {
      type: String,
      value: '#e5e5e5'
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    displayStyle: '',
    // 计算后的样式
    computedBackground: '',
    computedColor: '',
    computedBackIconColor: '',
    // 主题预设
    themes: {
      default: {
        background: '#ffffff',
        color: '#000000',
        backIconColor: '#000000'
      },
      light: {
        background: '#ffffff',
        color: '#000000',
        backIconColor: '#000000'
      },
      dark: {
        background: '#000000',
        color: '#ffffff',
        backIconColor: '#ffffff'
      },
      transparent: {
        background: 'transparent',
        color: '#ffffff',
        backIconColor: '#ffffff'
      }
    }
  },
  lifetimes: {
    attached() {
      const rect = wx.getMenuButtonBoundingClientRect()
      wx.getSystemInfo({
        success: (res) => {
          const isAndroid = res.platform === 'android'
          const isDevtools = res.platform === 'devtools'
          this.setData({
            ios: !isAndroid,
            innerPaddingRight: `padding-right: ${res.windowWidth - rect.left}px`,
            leftWidth: `width: ${res.windowWidth - rect.left }px`,
            safeAreaTop: isDevtools || isAndroid ? `height: calc(var(--height) + ${res.safeArea.top}px); padding-top: ${res.safeArea.top}px` : ``
          })
          // 初始化主题
          this._updateStyles()
        }
      })
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 主题变化处理
     */
    _themeChange() {
      this._updateStyles()
    },

    /**
     * 更新样式
     */
    _updateStyles() {
      const { themes } = this.data
      const { theme, background, color, backIconColor } = this.properties
      const themeConfig = themes[theme as keyof typeof themes] || themes.default

      // 计算背景色
      let computedBackground = background || themeConfig.background

      // 计算文字颜色
      let computedColor = color || themeConfig.color

      // 计算返回图标颜色
      let computedBackIconColor = backIconColor || themeConfig.backIconColor

      // 处理预设颜色值
      if (computedBackIconColor === 'white') {
        computedBackIconColor = '#ffffff'
      } else if (computedBackIconColor === 'black') {
        computedBackIconColor = '#000000'
      }

      this.setData({
        computedBackground,
        computedColor,
        computedBackIconColor
      })
    },

    /**
     * 显示隐藏变化处理
     */
    _showChange(show: boolean) {
      const animated = this.data.animated
      let displayStyle = ''
      if (animated) {
        displayStyle = `opacity: ${
          show ? '1' : '0'
        };transition:opacity 0.5s;`
      } else {
        displayStyle = `display: ${show ? '' : 'none'}`
      }
      this.setData({
        displayStyle
      })
    },

    /**
     * 返回按钮点击
     */
    back() {
      const data = this.data
      if (data.delta) {
        wx.navigateBack({
          delta: data.delta
        })
      }
      this.triggerEvent('back', { delta: data.delta }, {})
    },

    /**
     * 首页按钮点击
     */
    home() {
      this.triggerEvent('home', {}, {})
    }
  },
})
