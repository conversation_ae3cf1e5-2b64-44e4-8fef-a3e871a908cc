<view class="map-container">
	<map
		id="map"
		style="height: calc(100% + 23px);width: 100%;"
		longitude="{{longitude}}"
		latitude="{{latitude}}"
		scale="{{scale}}"
		min-scale="{{minScale}}"
		max-scale="{{maxScale}}"
		show-scale="{{showScale}}"
		show-location="{{showLocation}}"
		enable-zoom="{{enableZoom}}"
		enable-scroll="{{enableScroll}}"
		enable-rotate="{{enableRotate}}"
		enable-poi="{{enablePoi}}"
		markers="{{markers}}"
		bindcallouttap="handleCalloutTap"
	>
		<cover-view slot="callout">
			<cover-view
				wx:for="{{markers}}"
				wx:key="id"
				marker-id="{{item.id}}"
				class="custom-callout-item {{item.active ? 'is-active': null}} {{item.hasBorder ? 'has-border' : null}} {{item.borderRadius ? 'is-radius' : null}}"
			>
				<cover-view class="prefix-text">{{item.customCallout.prefixText}}</cover-view>
				<cover-view>{{item.customCallout.content}}</cover-view>
				<cover-view class="suffix-text">{{item.customCallout.suffixText}}</cover-view>
			</cover-view>
		</cover-view>

	</map>
</view>