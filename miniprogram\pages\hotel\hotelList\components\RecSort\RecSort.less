/* pages/hotel/hotelList/components/RecSort/RecSort.wxss */

.rec-sort {
  background-color: #fff;
  padding: 0;
}

.sort-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f5f5f5;
  }
  
  &.selected {
    .sort-label {
      color: #568DED;
      font-weight: 500;
    }
  }
}

.sort-label {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 优化触摸体验 */
.sort-item {
  user-select: none;
}