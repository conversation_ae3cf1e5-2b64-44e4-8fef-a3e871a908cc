// components/suggestPopup/index.ts
import { getImageUrl } from '../../../../utils/images';
import { submitFeedback } from '../../../../api/home';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 关闭按钮图标
    closeIcon: getImageUrl('login/close.png'),
    // placeholder文本
    placeholderText: `最近出门遇到啥烦恼了吗？
有什么功能你觉得"要是有就太好了"？
随便说说，我们认真听！`,
    // 详细意见
    suggestion: '',
    // 已上传图片URL列表（服务器返回的URL）
    uploadedImageUrls: [] as string[],
    // 最大字数限制
    maxTextLength: 1000,
    // 最大图片数量
    maxImageCount: 9
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭弹框
     */
    onClose() {
      this.triggerEvent('close');
      // 重置表单
      this.resetForm();
    },

    /**
     * 输入建议内容
     */
    onInput(e: WechatMiniprogram.Input) {
      const value = e.detail.value;
      // 限制最大字数
      if (value.length <= this.data.maxTextLength) {
        this.setData({
          suggestion: value
        });
      }
    },

    /**
     * 图片上传组件变化事件
     */
    onImageChange(e: WechatMiniprogram.CustomEvent<{value: string[]}>) {
      const { value } = e.detail;
      this.setData({
        uploadedImageUrls: value
      });
    },

    /**
     * 图片上传成功事件
     */
    onUploadSuccess(e: any) {
      const { imageUrls } = e.detail;
      console.log('图片上传成功:', imageUrls);
    },

    /**
     * 图片上传失败事件
     */
    onUploadError(e: any) {
      const { message } = e.detail;
      console.error('图片上传失败:', message);
      wx.displayToast({
        title: message,
        icon: 'none'
      });
    },









    /**
     * 提交建议
     */
    async onSubmit() {
      const { suggestion, uploadedImageUrls } = this.data;

      // 验证输入
      if (!suggestion.trim()) {
        wx.displayToast({
          title: '请输入您的建议',
          icon: 'none'
        });
        return;
      }

      try {
        // 显示提交loading
        wx.displayLoading({
          title: '提交中...'
        });

        // 调用接口提交建议和图片
        const { isSuccess, message } = await submitFeedback({
          detailedOpinion: suggestion.trim(),
          imageUrls: uploadedImageUrls
        });

        wx.hidePrevLoading();

        if (isSuccess) {
          wx.displayToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000
          });

          // 关闭弹框并重置表单
          setTimeout(() => {
            this.triggerEvent('success', {
              suggestion,
              images: uploadedImageUrls
            });
            this.resetForm();
          }, 2000);
        } else {
          wx.displayToast({
            title: message || '提交失败',
            icon: 'none'
          });
        }
      } catch (error) {
        wx.hidePrevLoading();
        console.error('提交建议失败:', error);
        wx.displayToast({
          title: '提交失败',
          icon: 'none'
        });
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.setData({
        suggestion: '',
        uploadedImageUrls: []
      });
    }
  }
});
