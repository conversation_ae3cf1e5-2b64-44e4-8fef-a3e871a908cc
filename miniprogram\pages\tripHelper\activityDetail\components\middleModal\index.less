.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  .popup-content{
    padding: 40rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title{
      font-weight: 500;
      font-size: 32rpx;
      color: #11111E;
      margin-bottom: 32rpx;
    }
    .des{
      color: #11111E;
      font-size: 28rpx;
      margin-bottom: 32rpx;
      max-width: 518rpx;
    }
    .bottom-bar-box{
      display: flex;
      justify-content: center;
      align-items: center;

      view{
        width: 256rpx;
        height: 96rpx;
        background: #F3F3F3;
        border-radius: 120rpx 120rpx 120rpx 120rpx;
        font-weight: 400;
        font-size: 32rpx;
        color: #33333E;
        line-height: 96rpx;
        text-align: center;
      }
      view:last-child {
        background: #0198FF;
        font-weight: 500;
        color: #FFFFFF;
        margin-left: 16rpx;
      }
    }
  }
}