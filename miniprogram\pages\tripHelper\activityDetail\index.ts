import { getDetailApi } from '../../../api/tripHelper/activityDetail'
import { formatDate,getDayOfWeek } from '../../../utils/tool'

const mockActivityDetail = {
  id: 12345,
  userId: 67890,
  participantIds: ["1001", "1002", "1003", "1004"],
  assemblyPlaceDTOS: [
    {
      startTime: "08:00",
      placeName: "人民广场正门",
      longitude: 121.4737,
      latitude: 31.2304,
      cityName:'上海'
    },
    {
      startTime: "08:30",
      placeName: "中山公园地铁站3号口",
      longitude: 121.4209,
      latitude: 31.2185,
      cityName:'上海'
    }
  ],
  title: "周末徒步佘山国家森林公园",
  description: "本次活动将带领大家徒步游览佘山国家森林公园，欣赏自然风光，呼吸新鲜空气。路线全程约8公里，适合各年龄段参加。我们将提供专业领队和基础徒步装备。",
  coverImages: [
    "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    "https://picsum.photos/id/1015/600/400",
    "https://images.unsplash.com/photo-1519501025264-65ba15a82390"
  ],
  activityType: "户外徒步",
  signUpDeadLineTime: "2023-12-10 18:00:00",
  routeInfo: {
    routeId: 54321,
    name: "佘山环线徒步路线",
    channel: [1, 3],
    project: [2],
    description: "从东佘山南门出发，经西佘山天文台，环线返回起点",
    cities: ["021"],
    cityNames: ["上海"],
    channelName: ["官方渠道", "合作平台"],
    projectName: ["周末休闲"],
    picUrls: [
      "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
      "https://picsum.photos/id/1015/600/400"
    ],
    journeyInfos: [
      {
        dayNo: 1,
        time: "上午",
        locations: [
          {
            locationId: 101,
            locationNameInput: "东佘山南门",
            location: {
              locationId: 101,
              name: "东佘山南门",
              address: "上海市松江区外青松公路9258号",
              type: 1,
              typeName: "公园入口",
              category: 3,
              categoryName: "自然景点",
              playTag: [5, 8],
              playTagName: ["徒步", "摄影"],
              playContent: [2, 3],
              playContentName: ["自然风光", "休闲徒步"],
              otherTag: [1],
              otherTagName: ["集合点"],
              contact: "",
              description: "东佘山主要入口，设有停车场和游客中心",
              pics: [
                {
                  url: "https://example.com/images/location1.jpg",
                  isMain: true
                }
              ],
              picUrls: ["https://example.com/images/location1.jpg"],
              videos: []
            },
            description: "集合地点，领取装备和讲解注意事项"
          }
        ]
      }
    ]
  },
  startTime: "2023-12-12 08:00:00",
  endTime: "2023-12-12 16:00:00",
  dateDesc: "12月12日 周日",
  maxTicketCount: 30,
  minTicketCount: 10,
  leftTicketCount: 8,
  perCost: [
    {
      perCost: 128.00,
      type: 1
    },
    {
      perCost: 98.00,
      type: 2
    }
  ],
  policyDTO: {
    id: 1,
    name: "标准退款政策",
    desc: "活动前48小时可全额退款，之后按比例退款",
    policyDetails: [
      {
        id: 1,
        refundRule: 1,
        timeType: 1,
        time: 2,
        canRefund: 1,
        refundProportion: 100
      },
      {
        id: 2,
        refundRule: 2,
        timeType: 1,
        time: 1,
        canRefund: 1,
        refundProportion: 50
      }
    ]
  },
  allowWaiting: 0,
  status: 1,
  wxImage: "https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotel/orderDetail/ewm.png",
  cityName: "上海",
  showAlternateButton: false,
  showDetailBtn: false,
  showSignInBtn: false,
  showCheckinBtn: false,
  showSignUpBtn: false,
  showActivityManageBtn:true
}; 
interface PageOptions {
  id?: string | number;
}
Page({
  /** 页面的初始数据 */
  data: {
    isLoading:true,
    pageOptions:{} as PageOptions,
    isShowOpacityNavbar:true,         // 导航是否透明
    detailData:null as null | ActivityDetail.IDetailResponse,  
    shareData:null as null | ActivityDetail.ShareDataParams,
    shareCardVisible:false,           // 分享活动卡片
    isShowRefundModal:false,          // 退款弹窗
    isShowInsuranceModal:false,       // 保险弹窗
    isShowManagementModal:false,      // 活动管理弹窗
    setActivityList:[] as Array<{ desc: string; type: string }>, // 活动管理列表
    isShowMiddleModal:false,          // 提示弹窗
    middleModalContent:{},            // 提示弹窗内容


    // 测试
    memberList: [
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg',sex:0 },
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg',sex:0  },
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg' ,sex:0 },
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg',sex:1 },
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg',sex:1 },
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg' ,sex:0},
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg' ,sex:1},
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg' ,sex:0 },
      { name: '大发', imageUrl: 'https://pavo.elongstatic.com/i/Hotel120_120/1waUJftazeM.jpg',sex:0  }
    ],
  },

  /** 生命周期函数--监听页面加载 */
  onLoad(options : PageOptions) {
    const { id } = options

    if (!id) {
      wx.showToast({ title: '缺少活动', icon: 'none' });
      return;
    }

    this.setData({
      pageOptions:options
    },() => {
      this.initDetail()
    })
  },
  /** 用户点击右上角分享 */
  onShareAppMessage(): WechatMiniprogram.Page.ICustomShareContent {
    const { detailData } = this.data
    return {
      title: detailData?.title,
      path: '/pages/tripHelper/activityDetail/index',
      imageUrl: detailData?.coverImages[0]
    };
  },

  // 初始化
  async initDetail(){
    try{
      const { pageOptions } = this.data
      const { code , isSuccess, data ,message } = await getDetailApi({id:pageOptions.id!})
      if(code === 200 && isSuccess && data){
        const { coverImages,title,perCost,startTime,wxImage,cityName,showActivityManageBtn,showSignUpBtn,allowWaiting , showAlternateButton,showCheckinBtn,status } = data
        let detailData = data

        // 价格，取最低价外显
        const lowPrice = Math.min(...perCost.map(item => item.perCost));
        // 取整数和小数仅用于渲染页面
        const priceArr = lowPrice.toString().split('.')
        const integerPrice = priceArr[0]
        const decimalPrice = priceArr.length > 1 ? ('.' + priceArr[1]) : '.00'

        // 活动时间（MM.DD(weekend))
        const activityTime = formatDate(startTime,'MM.DD') + '（' + getDayOfWeek(startTime) + '）'

        // 页面底部按钮
        // status -1未知、1已创建、2待审核、3审核成功、4审核失败、5已创建但未成团、6拼团成功、7已取消、8进行中、9已完结
        // allowWaiting -1未知、1允许候补、2不允许候补
        // 已取消 > 精彩打卡 > 活动管理 > 报名上车 > 已满员 > 候补等位
        let btmObj={
          desc:'',          // 按钮文案
          type:'',          // 按钮类型
          isDisabled:false, // 是否高亮度
        }
        if(status === 7){
          btmObj.desc = `已取消`
          btmObj.type ='activityAlreadyCancel'
          btmObj.isDisabled = true
        }else if(showCheckinBtn){
          btmObj.desc = `精彩打卡`
          btmObj.type ='activityCheckin'
        }else if(showActivityManageBtn){ 
          btmObj.desc = '活动管理'
          btmObj.type = 'activityManagement'
        }else if(showSignUpBtn){
          btmObj.desc = `报名上车（¥${integerPrice}）`
          btmObj.type ='activityRegistration'
        }else if(showAlternateButton){
          // 是否允许补位
          if(allowWaiting === 1){
            btmObj.desc = `候补等位（¥${integerPrice}）`
            btmObj.type ='activityWaitlist'
          }else{
            btmObj.desc = `已满员`
            btmObj.type ='activityAlreadyFull'
            btmObj.isDisabled = true
          }
        }

        // 分享卡片数据
        const shareData = {
          headImg:coverImages[0],
          price:lowPrice,
          title,
          startDate:formatDate(startTime,'YYYY.MM.DD'),
          cityName,
          wxImage,
          integerPrice,
          decimalPrice
        }

        this.setData({
          detailData:{
            ...detailData,
            lowPrice,
            integerPrice,
            decimalPrice,
            activityTime,
            btmObj
          },
          shareData,
          isLoading : false
        })
      }else{
        wx.showToast({ title: message , icon: 'none' });
      }
    }catch(error){
      wx.showToast({ title: '页面加载失败，请稍后重试', icon: 'none' });
    }
  },
  // 获取报名人信息
  // 获取退改政策
  // 分享活动弹窗
  shareActivity(){
    this.setData({shareCardVisible:true})
  },
  // 签到
  signInActivity(){

  },
  // 唤起腾讯地图
  openMap(event: WechatMiniprogram.BaseEvent) {
    const { lon,lat,cityName,address } = event.currentTarget.dataset
    
    wx.openLocation({
      latitude: lat,        // 纬度，范围为-90~90，负数表示南纬
      longitude: lon,       // 经度，范围为-180~180，负数表示西经
      name: cityName,       // 位置名
      address,              // 地址的详细说明
      scale: 18,            // 地图缩放级别，整形值，范围从1~28
      success: (res) => {
        console.log('打开成功', res);
      },
      fail: (err) => {
        console.error('打开失败', err);
      }
    });
  },
  // 报名人数查看全部
  jumpToRegistrationList(event: WechatMiniprogram.BaseEvent){
    const { type } = event.currentTarget.dataset;
    this.jumpToPage(type)
  },
  // 滚动监听
  handleScroll(event: WechatMiniprogram.ScrollViewScroll) {
    const systemInfo = wx.getSystemInfoSync();
    const scrollTopPx = event.detail.scrollTop; // 获取 px 单位的滚动距离
    const scrollTopRpx = (scrollTopPx * 750) / systemInfo.windowWidth; // 转换为 rpx
    const { isShowOpacityNavbar } = this.data
    const shouldShow = scrollTopRpx <= 500;

    if (isShowOpacityNavbar !== shouldShow) {
      this.setData({ isShowOpacityNavbar: shouldShow });
    }
  },
  // 路由跳转集合
  jumpToPage(type:string) {
    let url = '';
    switch (type) {
      // 报名列表页
      case 'registrationList':
        url = `/pages/tripHelper/registrationList/index?id=${this.data.pageOptions.id}`;
        break;
      // 打卡页
      case 'checkin':
        console.log('跳转打卡页面')
        break;
      // 编辑活动页面
      // case ''
      // 报名页
      case 'order':
        // if(登陆){

        // }else{

        // }
      default:
        break;
    }
    wx.navigateTo({ url })
  },

  // 按钮点击事件集合
  btmClickEvent(event: WechatMiniprogram.BaseEvent | WechatMiniprogram.CustomEvent){
    let finalType: string | undefined;

    if (typeof event === 'object'){
      let dataset = event.currentTarget.dataset;
      if(Object.keys(dataset).length === 0 && typeof (event as any).detail === 'object'){
        dataset = (event as WechatMiniprogram.CustomEvent).detail
      }
      finalType = dataset.type
    } 
    switch(finalType){
      // 活动管理
      case 'activityManagement':
        this.handPop('',true,'management')
        break;
      // 活动报名
      case 'activityRegistration':
        // if(拼团模式开启){
        //   // 弹弹窗
        // }else{
        //   // 直接进下单页
        // }
        break;
      // 候补等位
      case 'activityWaitlist':
        break;
      // 精彩打卡
      case 'activityCheckin':
        this.jumpToPage('checkin')
        break;
      // 编辑活动
      case 'activityEdit':
        break;
      // 取消活动
      case 'activityCancel':
        this.setData({
          middleModalContent:{
            title:'',
            content:'确定取消当前活动吗？',
            cancelBtm:'放弃取消',
            confirmBtm:'确认取消'
          }
        },() => {
          this.handPop('',true,'modalTips')
        })
        break;
      // 已取消
      case 'activityAlreadyCancel':
        wx.showToast({ title: '活动已取消', icon: 'none' });
        break;
      // 已满员
      case 'activityAlreadyFull':
        wx.showToast({ title: '活动已满员', icon: 'none' });
        break;
      default:
        break;
    }
  },
  // 弹窗触发集合
  handPop(event: WechatMiniprogram.BaseEvent | WechatMiniprogram.CustomEvent | string,visible?: boolean, type?: string){

    let finalType: string | undefined;
    let finalVisible: boolean | undefined;

    if(typeof type === 'string' && typeof visible === 'boolean'){
      finalType = type
      finalVisible = visible
    }else if (typeof event === 'object'){
      let dataset = event.currentTarget.dataset;
      if(Object.keys(dataset).length === 0 && typeof (event as any).detail === 'object'){
        dataset = (event as WechatMiniprogram.CustomEvent).detail
      }
      const {type,visible} = dataset
      finalType = type
      finalVisible = visible
    } 

    if (!finalType || typeof finalVisible !== 'boolean') {
      return;
    }
    
    switch(finalType){
      // 退改政策弹窗
      case 'refundPolicy':
        this.setData({isShowRefundModal:finalVisible})
        break;
      // 保险弹窗
      case 'insurancePlan':
        this.setData({isShowInsuranceModal:finalVisible})
        break;
      // 活动管理弹窗
      case 'management':
        let setActivityList = []  // 活动管理按钮
        const {detailData} = this.data
        // 打开的时候处理
        if(detailData?.showActivityManageBtn && finalVisible){
          if(detailData?.showSignUpBtn){
            setActivityList.push({
              desc:'活动报名',
              type:'activityRegistration'
            })
          }else if(detailData?.allowWaiting === 1 && detailData?.showAlternateButton){
            setActivityList.push({
              desc:'候补等位',
              type:'activityWaitlist'
            })
          }
  
          // 编辑取消默认都在
          setActivityList.push({
            desc:'编辑活动',
            type:'activityEdit'
          })
          setActivityList.push({
            desc:'取消活动',
            type:'activityCancel'
          })
        }
        this.setData({
          isShowManagementModal:finalVisible,
          setActivityList
        })
        break;
      // 强提示弹窗（取消活动）
      case 'modalTips':
        this.setData({isShowMiddleModal:finalVisible})
        break;
    }
  },
})