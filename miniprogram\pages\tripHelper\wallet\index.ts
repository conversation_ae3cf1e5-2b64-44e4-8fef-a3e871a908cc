// pages/tripHelper/wallet/index.ts
import { getImageUrl } from '../../../utils/images'
import { getWalletBalance, getActivityOrderList } from '../../../api/wallet';





Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 头部背景图，与"我的"页面使用相同的背景图
    headBgIcon: getImageUrl('tripHelper/my/bg.png'),

    // 箭头图标
    arrowIcon: getImageUrl('arrow.png'),

    // 工具函数
    getImageUrl: getImageUrl,

    // 钱包余额数据（直接存储接口返回的数据）
    walletBalance: {
      entryMoney: 0,        // 可提现金额
      waitCommission: 0,    // 待结算佣金（历史总收入）
      sumMonthMoney: 0      // 本月收入
    } as Wallet.IBalanceData,

    // 导航栏状态
    navbarBgColor: 'transparent',
    navbarTheme: 'transparent' as 'transparent' | 'white',

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: true,

    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false,

    // 交易记录列表（直接存储接口返回的数据）
    transactionList: [] as Wallet.IActivityOrderSummary[],


  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 页面加载时获取钱包余额和交易记录
    this.loadWalletBalance();
    this.loadTransactionList();
  },

  /**
   * 页面滚动事件
   */
  onPageScroll(e: WechatMiniprogram.Page.IPageScrollOption) {
    const { scrollTop } = e;
    const threshold = 100; // 滚动阈值

    if (scrollTop > threshold) {
      // 滚动超过阈值，显示白色背景
      if (this.data.navbarBgColor === 'transparent') {
        this.setData({
          navbarBgColor: 'rgba(255, 255, 255, 0.95)',
          navbarTheme: 'white'
        });
      }
    } else {
      // 滚动未超过阈值，显示透明背景
      if (this.data.navbarBgColor !== 'transparent') {
        this.setData({
          navbarBgColor: 'transparent',
          navbarTheme: 'transparent'
        });
      }
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({
      refreshing: true,
      pageNum: 1
    });
    this.loadTransactionList();
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }
    this.loadTransactionList(true);
  },

  /**
   * 加载钱包余额
   */
  async loadWalletBalance() {
    try {
      const response = await getWalletBalance();

      if (response.code === 200 && response.data) {
        this.setData({
          walletBalance: response.data
        });
      } else {
        console.error('获取钱包余额失败:', response.message);
        wx.showToast({
          title: response.message || '获取钱包余额失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取钱包余额异常:', error);
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });
    }
  },

  /**
   * 加载交易记录列表
   */
  async loadTransactionList(isLoadMore = false) {
    try {
      if (!isLoadMore) {
        this.setData({ loading: true });
      } else {
        this.setData({ loadingMore: true });
      }

      // 调用真实API接口获取活动订单列表，支持分页
      const response = await getActivityOrderList({
        page: this.data.pageNum,
        size: this.data.pageSize
      });

      if (response.code === 200 && response.data !== undefined) {
        // 检查返回的数据结构
        if (Array.isArray(response.data)) {
          // 如果返回的是数组（空数据情况）
          const records = response.data;

          if (isLoadMore) {
            // 上拉加载更多
            this.setData({
              transactionList: [...this.data.transactionList, ...records],
              hasMore: false, // 空数组表示没有更多数据
              pageNum: this.data.pageNum + 1
            });
          } else {
            // 首次加载
            this.setData({
              transactionList: records,
              hasMore: false, // 空数组表示没有更多数据
              pageNum: 2
            });
          }
        } else {
          // 如果返回的是包含分页信息的对象
          const { records = [], current = 1, total = 0, size = this.data.pageSize } = response.data;

          if (isLoadMore) {
            // 上拉加载更多
            this.setData({
              transactionList: [...this.data.transactionList, ...records],
              hasMore: (current * size) < total, // 根据分页信息判断是否还有更多数据
              pageNum: this.data.pageNum + 1
            });
          } else {
            // 首次加载
            this.setData({
              transactionList: records,
              hasMore: (current * size) < total, // 根据分页信息判断是否还有更多数据
              pageNum: current + 1 // 下次加载下一页
            });
          }
        }
      } else {
        console.error('获取活动订单列表失败:', response.message);
        wx.showToast({
          title: response.message || '获取交易记录失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载交易记录异常:', error);
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      });
    }
  },





  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadWalletData();
    wx.stopPullDownRefresh();
  },



  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 自定义导航栏返回按钮点击事件
   */
  onNavigationBack() {
    wx.navigateBack();
  },

  /**
   * 加载钱包数据
   */
  loadWalletData() {
    // TODO: 调用API获取钱包数据
    console.log('加载钱包数据');
  },



  /**
   * 点击提现记录按钮
   */
  onWithdrawRecord() {
    wx.navigateTo({
      url: '/pages/tripHelper/wallet/withdrawRecord/index'
    });
  },

  /**
   * 点击一键提现按钮
   */
  onWithdraw() {
    // 检查可提现金额
    if (this.data.walletBalance.entryMoney <= 0) {
      wx.showToast({
        title: '暂无可提现金额',
        icon: 'none'
      });
      return;
    }

    // 跳转到提现页面，传递默认金额
    wx.navigateTo({
      url: `/pages/tripHelper/wallet/withdraw/index?amount=${this.data.walletBalance.entryMoney}`
    });
  },

  /**
   * 点击禁用状态的提现按钮
   */
  onWithdrawDisabled() {
    wx.showToast({
      title: '暂无可提现金额',
      icon: 'none'
    });
      wx.navigateTo({
      url: `/pages/tripHelper/wallet/withdraw/index?amount=${this.data.walletBalance.entryMoney}`
    });
  },

  /**
   * 点击交易记录项，跳转到活动参与人订单信息页面
   */
  onTransactionDetailClick(event: WechatMiniprogram.TouchEvent) {
    const { id } = event.currentTarget.dataset;
    console.log('点击交易详情，活动ID:', id);

    // 跳转到活动参与人订单信息页面
    wx.navigateTo({
      url: `/pages/tripHelper/wallet/detail/index?transactionId=${id}`
    });
  }
});
