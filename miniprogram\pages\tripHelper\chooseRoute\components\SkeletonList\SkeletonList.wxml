<!-- 骨架屏列表组件 -->
<view class="skeleton-list">
  <view class="skeleton-item" wx:for="{{[1,2,3]}}" wx:key="index">
    <view class="skeleton-card">
      <view class="skeleton-content">
        <!-- 左侧图标占位 -->
        <view class="skeleton-icon"></view>
        <!-- 右侧内容区域 -->
        <view class="skeleton-main">
          <!-- 标题和收藏图标行 -->
          <view class="skeleton-header">
            <view class="skeleton-title"></view>
            <view class="skeleton-favorite"></view>
          </view>
          <!-- 图片区域 -->
          <view class="skeleton-images">
            <view class="skeleton-image"></view>
            <view class="skeleton-image"></view>
            <view class="skeleton-image"></view>
          </view>
          <!-- 底部信息行 -->
          <view class="skeleton-footer"></view>
        </view>
      </view>
    </view>
  </view>
  <!-- 最后一个较小的占位 -->
  <view class="skeleton-item-small"></view>
</view> 