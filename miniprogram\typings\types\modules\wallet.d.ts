/**
 * 钱包模块类型定义
 */
declare namespace Wallet {
  // ===== 钱包余额相关 =====
  
  /**
   * 获取钱包余额请求参数
   */
  interface IBalanceReq {
    // 该接口无需额外参数，只需要token
  }

  /**
   * 钱包余额响应数据
   */
  interface IBalanceData {
    /** 可提现金额 */
    entryMoney: number;
    /** 待结算佣金 */
    waitCommission: number;
    /** 本月收入 */
    sumMonthMoney: number;
  }

  /**
   * 获取钱包余额响应
   */
  interface IBalanceRes {
    code: number;
    message: string;
    data: IBalanceData;
  }

  // ===== 提现记录相关 =====

  /**
   * 获取提现记录请求参数
   */
  interface IRecordsReq {
    /** 页码，从1开始 */
    page: number;
    /** 每页大小，默认10，最大100 */
    size: number;
  }

  /**
   * 提现记录项（直接使用接口返回字段）
   */
  interface IWithdrawRecord {
    /** 创建时间 */
    createTime: string | null;
    /** 提现金额 */
    withdrawalMoney: number;
    /** 批次状态 */
    batchStatus: number;
    /** 批次消息 */
    batchMessage: string;
  }

  /**
   * 提现记录列表数据（直接返回数组）
   */
  interface IRecordsData extends Array<IWithdrawRecord> {}

  /**
   * 提现限制查询请求参数（无需参数，只需要token）
   */
  interface IWithdrawLimitationRequest {}

  /**
   * 提现限制查询响应数据
   */
  interface IWithdrawLimitationResponse {
    /** 最大提现金额数值 */
    maxLimitation: number;
    /** 用户姓名，如果有值则显示已绑定，如果为空则显示输入框 */
    userName: string;
  }

  /**
   * 用户提现请求参数
   */
  interface IWithdrawTransferRequest {
    /** 提现金额（字符串类型） */
    transferMoney: string;
    /** 用户真实姓名 */
    userName: string;
  }

  /**
   * 用户提现响应数据
   */
  interface IWithdrawTransferResponse {
    // 根据接口文档，返回的data是空对象
    // 可以根据实际需要扩展
  }

  /**
   * 获取提现记录响应
   */
  interface IRecordsRes {
    code: number;
    message: string;
    data: IRecordsData;
  }

  // ===== 活动订单列表相关 =====

  /**
   * 获取用户活动订单列表请求参数
   */
  interface IActivityOrderListReq {
    /** 页码，从1开始 */
    page: number;
    /** 每页大小 */
    size: number;
  }

  /**
   * 活动订单汇总信息
   */
  interface IActivityOrderSummary {
    /** 活动ID */
    activityId: number;
    /** 活动名称 */
    activityName: string;
    /** 活动开始时间 */
    startTime: string;
    /** 活动结束时间 */
    endTime: string;
    /** 总金额 */
    totalSumAmount: number;
    /** 实际总金额 */
    actualTotalSumAmount: number;
    /** 保险金额 */
    insuranceAmount: number;
    /** 平台服务费 */
    platformServiceAmount: number;
  }

  /**
   * 活动订单列表分页数据
   */
  interface IActivityOrderListData {
    /** 当前页码 */
    current: number;
    /** 每页大小 */
    size: number;
    /** 总记录数 */
    total: number;
    /** 活动订单列表 */
    records: IActivityOrderSummary[];
  }

  /**
   * 获取用户活动订单列表响应
   */
  interface IActivityOrderListRes {
    code: number;
    message: string;
    data: IActivityOrderListData;
  }

  // ===== 页面使用的数据结构 =====

  /**
   * 钱包页面显示的交易记录项
   */
  interface ITransactionItem {
    /** 记录ID */
    id: string;
    /** 标题 */
    title: string;
    /** 日期 */
    date: string;
    /** 图标 */
    icon: string;
    /** 总金额 */
    amount: number;
    /** 详情列表 */
    details: ITransactionDetail[];
  }

  /**
   * 交易详情项
   */
  interface ITransactionDetail {
    /** 标签 */
    label: string;
    /** 金额 */
    amount: number;
    /** 类型：income-收入，expense-支出 */
    type: 'income' | 'expense';
    /** 明细说明 */
    breakdown?: string;
  }

  /**
   * 钱包页面数据状态
   */
  interface IWalletPageData {
    /** 当前可提现金额 */
    currentBalance: number;
    /** 历史总收入 */
    totalIncome: number;
    /** 本月收入 */
    monthIncome: number;
    /** 交易记录列表 */
    transactionList: ITransactionItem[];
    /** 分页参数 */
    pageNum: number;
    pageSize: number;
    hasMore: boolean;
    /** 加载状态 */
    loading: boolean;
    loadingMore: boolean;
    refreshing: boolean;
  }

  // ===== 活动参与人订单信息相关 =====

  /**
   * 获取活动参与人订单信息请求参数
   */
  interface IActivityParticipantOrderReq {
    /** 活动ID */
    activityId: number;
  }

  /**
   * 订单信息
   */
  interface IParticipantOrder {
    /** 订单ID */
    orderId: number;
    /** 订单号 */
    orderNo: string;
    /** 活动ID */
    activityId: number;
    /** 活动名称 */
    activityName: string;
    /** 订单状态 */
    orderStatus: string;
    /** 订单状态描述 */
    orderStatusDesc: string;
    /** 订单金额 */
    orderAmount: number;
    /** 已支付金额 */
    paidAmount: number;
    /** 成人数量 */
    adultCount: number;
    /** 儿童数量 */
    childCount: number;
    /** 下单时间 */
    orderTime: string;
    /** 支付时间 */
    payTime: string;
    /** 退款金额 */
    refundAmount: number;
    /** 退款状态 */
    refundStatus: string;
    /** 备注 */
    remark: string;
  }

  /**
   * 参与人信息
   */
  interface IParticipant {
    /** 用户ID */
    userId: number;
    /** 用户名 */
    userName: string;
    /** 头像 */
    avatar: string;
    /** 手机号 */
    mobile: string;
    /** 订单列表 */
    orders: IParticipantOrder[];
    /** 总订单数 */
    totalOrders: number;
    /** 总支付金额 */
    totalPayAmount: number;
  }

  /**
   * 集合地信息
   */
  interface IGatheringLocation {
    /** 集合地名称 */
    locationName: string;
    /** 集合地地址 */
    locationAddress: string;
    /** 集合时间 */
    gatheringTime: string;
    /** 参与人列表 */
    participants: IParticipant[];
    /** 总参与人数 */
    totalParticipants: number;
    /** 总金额 */
    totalAmount: number;
  }

  /**
   * 活动参与人订单信息数据
   */
  interface IActivityParticipantOrderData {
    /** 集合地列表 */
    gatheringLocations: IGatheringLocation[];
  }

  /**
   * 获取活动参与人订单信息响应
   */
  interface IActivityParticipantOrderRes {
    code: number;
    message: string;
    data: IActivityParticipantOrderData;
  }
}
