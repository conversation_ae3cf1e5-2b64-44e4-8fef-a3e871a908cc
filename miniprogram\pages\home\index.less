page {
	background-color: #F7F7F7;
}

.home-bg {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 650rpx;
	z-index: -1;
	background-image: url("@{static-base-url}/home/<USER>");
	background-size: 100% 100%;
	clip-path: ellipse(140% 100% at 50% 0%);
}

.home-service {
	padding: 114rpx 40rpx 82rpx;
	.app-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 96rpx;
		.app-sign {
			display: flex;
			align-items: center;
			.app-logo {
				display: block;
				width: 168rpx;
				height: 64rpx;
			}
		}
		.app-greeting {
			color: #fff;
			font-size: 32rpx;
		}
	}

	.business-grid {
		display: flex;
		justify-content: space-between;
		.business-item {
			width: 156rpx;
			height: 156rpx;
			background: #fff;
			border-radius: 40rpx 40rpx 40rpx 40rpx;
			color: #66666E;
			font-size: 24rpx;
			line-height: 34rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			&.is-active {
				background: linear-gradient( 318deg, #0198FF 0%, #0DC0FE 100%);
				color: #fff;
			}
			&-icon {
				width: 64rpx;
				margin-bottom: 8rpx;
			}
		}
	}

	.quick-search-container {
		padding: 48rpx;
		margin-top: 38rpx;
		background-color: #fff;
		box-shadow: 0rpx 8rpx 28rpx 0rpx rgba(0,0,0,0.25);
		border-radius: 56rpx 56rpx 56rpx 56rpx;
		box-sizing: border-box;
		position: relative;
		&::before {
			content: "";
			position: absolute;
			top: -26rpx;
			left: 54rpx;
			width: 58rpx;
			height: 26rpx;
			background-image: url("@{static-base-url}/home/<USER>");
			background-size: 100% 100%;
			transition: left linear 0.3s;
		}
		&.flight {
			&::before {
				left: 221rpx;
			}
		}
		&.train {
			&::before {
				left: 388rpx;
			}
		}
		&.ticket {
			&::before {
				left: 555rpx;
			}
		}
	}
}