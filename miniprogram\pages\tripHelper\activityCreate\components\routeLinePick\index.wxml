<Skeleton wx:if="{{loading}}"></Skeleton>

<block wx:if="{{!loading && routeList.length}}">
	<view class="tabs-wrap">
		<view class="tab-item {{activeTab === item.value ? 'is-active' : null}}" wx:for="{{tabList}}" wx:key="value" bind:tap="handleSwitchTab" data-value="{{item.value}}">
			<image src="{{activeTab === item.value ? item.activeUrl : item.inActiveUrl}}" mode="widthFix" />
			<text>{{item.label}}</text>
		</view>
	</view>

	<view hidden="{{activeTab === 2}}">
		<scroll-view scroll-x class="list-wrap" scroll-into-view="{{toView}}">
			<view class="route-line-list">
				<view class="route-line-item {{routeId === item.routeId ? 'is-active' : ''}}" wx:for="{{routeList}}" wx:key="routeId" id="{{'item_' + index}}" bind:tap="handleRouteLinePick" data-route-id="{{item.routeId}}">
					<view class="main-image">
						<image src="{{item.picUrls[0]}}" mode="widthFix" />
					</view>
					<view class="route-line-name">{{item.name}}</view>
					<Checkbox value="{{routeId === item.routeId}}" size="40" />
				</view>
			</view>
		</scroll-view>

		<view class="see-more">
			<view class="trigger-item" bind:tap="handleJumpToRouteLineList">查看全部路线</view>
		</view>
	</view>
</block>

<view hidden="{{activeTab === 1 && routeList.length !== 0}}">
	<view class="card-wrap">
		<view class="form-item">
			<view class="upload-wrap">
				<view class="form-item-label">
					<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/photo.png'}}" mode="widthFix" />
					<text class="form-item-label-text">上传图片</text>
				</view>
				<view class="ai-wrap" bind:tap="handleOpenAIModal">
					AI识别
				</view>
			</view>
			<Uploader
				value="{{coverImages}}"
				showTitle="{{false}}"
				title-text="上传图片"
				max-count="{{9}}"
				bind:change="handleImageChange"
			/>
		</view>
		<view class="form-item">
			<view class="form-item-label">
				<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/draft.png'}}" mode="widthFix" />
				<text class="form-item-label-text">活动标题*</text>
			</view>
			<view class="form-item-content">
				<input value="{{title}}" bindinput="handleTitleChange" type="text" placeholder="填写活动标题" placeholder-style="font-weight: normal;color: #ccccce;" />
			</view>
		</view>
		<view class="form-item">
			<view class="form-item-label">
				<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/draft.png'}}" mode="widthFix" />
				<text class="form-item-label-text">活动介绍*</text>
			</view>
			<view class="form-item-content">
				<textarea value="{{description}}" bindinput="handleContentChange" type="text" class="textarea-input" auto-height placeholder="描述您的活动" maxlength="1500" placeholder-style="font-weight: normal;color: #ccccce;" />
				<text class="word-count" hidden="{{!description}}">{{description.length}}/1500</text>
			</view>
		</view>
	</view>
</view>

<!-- AI识别 -->
<LinkRecognition visible="{{showAIModal}}"  bind:update-visible="handleAIModalToggle" bindchange="handleValueChange" />