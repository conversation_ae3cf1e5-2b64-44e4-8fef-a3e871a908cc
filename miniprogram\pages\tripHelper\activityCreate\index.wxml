<view class="activity-create-page">
  <view class="steps-wrap">
    <view wx:for="{{steps}}" wx:key="value" class="step-item {{activeStep === item.value || item.value === 1 ? 'is-active' : null}} {{item.value === 1 && activeStep === 2 ? 'is-checked' : null}}" bind:tap="handleStepChange" data-step="{{item.value}}">
      <text>第{{item.value}}步：</text>
      <view hidden="{{activeStep === 1}}">
        <view wx:if="{{item.value === 1}}" class="active-project">
          <image src="{{activeProject.inactiveUrl}}" mode="widthFix"/>
          <text>{{activeProject.name}}</text>
        </view>
      </view>
      {{item.value === 1 && activeStep === 2 ? '' : item.title}}
    </view>
  </view>

  <view class="main-body">
    <view hidden="{{activeStep !== 1}}">
      <ProjectPick activeProject="{{activeProject}}" projectList="{{projectList}}" bind:change="handleProjectChange" bind:next="handleNextStep" />
    </view>
    <view hidden="{{activeStep !== 2}}">
      <Generator projectId="{{activeProject ? activeProject.id : 0}}" activityId="{{activityId}}" activeStep="{{activeStep}}" bind:tab-change="handleTabChange" bind:update-project="handleUpdateProject" />
    </view>
  </view>
</view>