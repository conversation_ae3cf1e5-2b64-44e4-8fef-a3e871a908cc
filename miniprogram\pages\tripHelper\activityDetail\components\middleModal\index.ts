interface ModalContent {
  title:string;
  content:string;
  cancelBtm:string,
  confirmBtm:string
}
Component({
  properties: {
    // 控制弹窗开关
    visible: {
      type: Boolean,
      value: false
    },
    content:{
      type:Object,
      value:{} as ModalContent
    }
  },
  methods: {
    // 阻止冒泡
    stopPropagation(){},
    closePopClick(){
      console.log('---')
      this.triggerEvent('close', { visible:false, type: 'modalTips'})
    }
  }
})