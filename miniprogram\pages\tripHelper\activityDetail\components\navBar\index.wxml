<view class="navbar-container" style="{{isOpacity ? 'background: rgba(0, 0, 0, 0)' : ''}}">
  <!-- 占位 -->
  <view class="navbar-placeholder" style="height: {{statusBarHeight}}px;"></view>
  <block>
    <!-- 通栏效果 -->
    <view class="navbar-content" style="height: {{navBarHeight}}px;" wx:if="{{isOpacity}}">
      <image class="navbar-left" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/back_white_icon.png" mode="" bindtap="handleBack"/>
    </view>
    <!-- 白底效果 -->
    <view class="navbar-content" style="height: {{navBarHeight}}px;" wx:else>
      <image class="navbar-left" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/back_black_icon.png" mode="" bindtap="handleBack"/>
      <text class="navbar-title">活动详情</text>
      <!-- 右侧留空，与胶囊按钮对齐 -->
      <view class="navbar-right"></view>
    </view>
  </block>
  

  
</view>