<!--pages/tripHelper/wallet/withdraw/index.wxml-->
<view class="withdraw-page">
  <!-- 提现金额部分 -->
  <view class="amount-section">
    <view class="amount-title">{{withdrawTitle}}</view>
    <view class="amount-input-container">
      <text class="currency-symbol">¥</text>
      <input
        class="amount-input"
        type="digit"
        placeholder="请输入提现金额"
        value="{{withdrawAmount}}"
        bindinput="onAmountInput"
      />
    </view>
    <view class="limit-text">{{limitText}}</view>
  </view>

  <!-- 姓名输入部分（条件显示） -->
  <view wx:if="{{showNameInput}}" class="name-section">
    <view class="name-title">填写身份证姓名</view>
    <input
      class="name-input"
      type="text"
      placeholder="请输入身份证姓名"
      value="{{userName}}"
      bindinput="onNameInput"
    />
    <view class="limit-text">微信转账需要提供姓名，如因姓名导致转账失败，请前往个人中心修改</view>
  </view>

  <!-- 提现方式部分 -->
  <view class="method-section">
    <view class="method-content">
      <image class="wechat-icon" src="{{wechatIcon}}" mode="aspectFit" />
      <view class="method-info">
        <view class="method-title">{{withdrawMethod.title}}</view>
        <view class="method-account">{{withdrawMethod.account}}</view>
      </view>
    </view>
  </view>

  <!-- 提现按钮 -->
  <view class="button-section">
    <view class="withdraw-button" bindtap="onWithdrawClick">
      提现
    </view>
  </view>
</view>
