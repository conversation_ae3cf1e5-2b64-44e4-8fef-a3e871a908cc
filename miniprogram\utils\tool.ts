/**
 * 工具函数集合
 * @file 常用工具函数
 */

/**
 * 格式化日期为指定格式
 * @param date - Date对象或日期字符串
 * @param format - 格式模板 (默认: 'YYYY.MM.DD')
 * @returns 格式化后的日期字符串
 * @example
 * formatDate(new Date(), 'YYYY.MM.DD') // => "2023.10.21"
 */
export const formatDate = (
  date: Date | string,
  format: string = 'YYYY-MM-DD'
): string => {
  // 创建Date对象
  let d: Date;
  if (typeof date === 'string') {
    d = new Date(date);
    if (isNaN(d.getTime())) {
      console.error('[formatDate] 无效的日期字符串:', date);
      return '';
    }
  } else {
    d = date;
  }

  // 获取日期各部分
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hours = d.getHours();
  const minutes = d.getMinutes();
  const seconds = d.getSeconds();

  // 补零函数（不使用padStart）
  const zeroPad = (num: number): string => {
    return num < 10 ? `0${num}` : `${num}`;
  };

  // 替换格式化字符串
  return format
    .replace(/YYYY/g, String(year))
    .replace(/MM/g, zeroPad(month))
    .replace(/DD/g, zeroPad(day))
    .replace(/HH/g, zeroPad(hours))
    .replace(/mm/g, zeroPad(minutes))
    .replace(/ss/g, zeroPad(seconds));
};

/**
 * 根据日期返回星期几
 * @param date 可以接受的日期格式：Date对象、时间戳或日期字符串
 * @param format 返回格式：'short'返回简写（如"周一"），'long'返回完整（如"星期一"），默认为'short'
 * @returns 星期几的字符串表示
 */
export const getDayOfWeek = (date: Date | string | number, format: 'short' | 'long' = 'short'): string => {
  // 创建Date对象
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    throw new Error('Invalid date input');
  }
  
  // 获取星期几的数字表示（0-6，0表示周日）
  const day = dateObj.getDay();
  
  // 定义星期几的中文表示
  const daysShort = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const daysLong = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  
  // 根据格式返回对应的星期几
  return format === 'short' ? daysShort[day] : daysLong[day];
};