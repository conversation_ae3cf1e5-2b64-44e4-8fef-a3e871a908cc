.tabs-wrap {
	background-color: #fff;
	border-radius: 32rpx;
	padding: 8rpx;
	display: flex;
	margin: 0 32rpx;
	justify-content: space-between;
	.tab-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		line-height: 84rpx;
		border-radius: 24rpx;
		image {
			margin-right: 16rpx;
			width: 24rpx;
			height: 24rpx;
		}
		&:last-child {
			margin-left: 10rpx;
		}
		&.is-active {
			color: #fff;
			background: linear-gradient( 138deg, @primary-color 0%, #0DC0FE 100%);
		}
	}
}

.list-wrap {
	margin-top: 32rpx;
}

.route-line-list {
	display: flex;
	align-items: center;
	.route-line-item {
		width: 216rpx;
		padding: 8rpx 8rpx 16rpx;
		flex-shrink: 0;
		box-sizing: border-box;
		background-color: #fff;
		border-radius: 32rpx;
		margin-right: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		&.is-active {
			.border(1px, solid, @primary-color, 32rpx);
		}
		&:first-child {
			margin-left: 24rpx;
		}
		.main-image {
			width: 100%;
			height: 180rpx;
			border-radius: 24rpx;
			overflow: hidden;
			image {
				width: 100%;
				height: 100%;
				border-radius: 24rpx;
			}
		}
		.route-line-name {
			color: @text-title-color;
			font-size: 24rpx;
			line-height: 34rpx;
			text-align: center;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			margin: 8rpx 0 16rpx;
		}
	}
	&::after {
		content: "";
		width: 8rpx;
		height: 1px;
		flex-shrink: 0;
	}
}

.see-more {
	display: flex;
	justify-content: center;
	.trigger-item {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		line-height: 34rpx;
		color: @text-title-color;
		margin: 24rpx 0 8rpx;
		&::after {
			content: "";
			width: 20rpx;
			height: 20rpx;
			background-image: url('@{static-base-url}/arrow.png');
			background-size: 100%;
		}
	}
}

.card-wrap {
	border-radius: 32rpx;
	padding: 32rpx 24rpx 24rpx;
	margin: 32rpx 24rpx 0;
	background: #fff;

	.form-item {
		padding: 32rpx 0;
		font-size: 28rpx;
		line-height: 40rpx;
		&-label {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			&-icon {
				width: 36rpx;
				height: 36rpx;
				margin-right: 16rpx;
			}
			&-text {
				color: @text-title-color;
			}
		}
		&-content {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			color: @text-title-color;
			background-color: @bg-light-gray-color;
			border-radius: 24rpx;
			padding: 32rpx;
			font-weight: bold;
			position: relative;
			input {
				line-height: 40rpx;
				display: block;
				width: 100%;
			}
			textarea {
				min-height: 156rpx;
				height: auto;
			}
			.word-count {
				position: absolute;
				right: 32rpx;
				bottom: 0;
				color: @text-light-color;
				font-size: 20rpx;
			}
		}
		.upload-wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.ai-wrap {
			display: flex;
			align-items: center;
			background: #EEF8FF;
			padding: 12rpx 24rpx;
			line-height: 40rpx;
			border-radius: 32rpx;
			color: @primary-color;
			&::before {
				content: "";
				width: 32rpx;
				height: 32rpx;
				background-image: url('@{static-base-url}/tripHelper/activity/ai.png');
				background-size: 100%;
				margin-right: 8rpx;
			}
		}
	}
}