/* 意见反馈页面样式 */
.feedback-page {
  background-color: #fff;
  min-height: 100vh;

  /* 通用标题样式 */
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }

  /* 意见类型选择区域 */
  .feedback-type-section {
    padding: 48rpx 24rpx;

    /* 类型选项容器 */
    .type-options {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
    }

    /* 单个类型选项 */
    .type-option {
      padding: 16rpx 28rpx;
      background-color: #F7F7F7;
      border-radius: 24rpx;
      font-size: 28rpx;
      color: #11111E;
      transition: all 0.3s ease;

      /* 选中状态 */
      &.selected {
        background-color: #568ded;
        color: #fff;
      }
    }
  }

  /* 详细意见输入区域 */
  .feedback-content-section {
    margin-top: 96rpx;
    padding: 0 24rpx;

    /* 输入框包装器 */
    .content-input-wrapper {
      background-color: #F7F7F7;
      border-radius: 16rpx;
      padding: 32rpx;
      position: relative;

      /* 文本输入框 */
      .content-input {
        width: 100%;
        min-height: 300rpx;
        max-height: 300rpx;
        font-size: 28rpx;
        color: #000;
        line-height: 1.6;
        border: none;
        outline: none;
        resize: none;
        overflow-y: auto;
        background: transparent;
      }

      /* 字符计数显示 */
      .char-count {
        position: absolute;
        bottom: 16rpx;
        right: 16rpx;
        font-size: 28rpx;
        color: #CCCCCE;
      }
    }
  }

  /* 图片上传区域 */
  .upload-section {
    padding: 0 24rpx;
    margin-top: 96rpx;

    /* 图片网格容器 - 水平滚动 */
    .upload-grid {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      gap: 16rpx;
      margin-top: 32rpx;
      -webkit-overflow-scrolling: touch;
    }

    /* 单个图片项容器 */
    .image-item {
      position: relative;
      width: 148rpx;
      height: 148rpx;
      margin-right: 16rpx;
      flex-shrink: 0;

      /* 已上传的图片 */
      .uploaded-image {
        width: 100%;
        height: 100%;
        border-radius: 32rpx;
        object-fit: cover;
      }

      /* 删除按钮 */
      .delete-btn {
        position: absolute;
        top: 0rpx;
        right: -8rpx;
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        /* 关闭图标 */
        .close-icon {
          width: 100%;
          height: 100%;
        }
      }

      /* 删除图标样式 */
      .delete-icon {
        color: #fff;
        font-size: 20rpx;
        font-weight: bold;
      }
    }

    /* 上传按钮 */
    .upload-btn {
      flex: 0 0 148rpx;
      width: 148rpx;
      height: 148rpx;
      background: #f7f7f7;
      border-radius: 24rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;

      /* 上传图标 */
      .upload-icon {
        font-size: 48rpx;
        margin-bottom: 8rpx;
        color: #66666e;
      }

      /* 图片图标 */
      .picture-icon {
        height: 48rpx;
        width: 48rpx;
        margin-bottom: 8rpx;
      }

      /* 上传文字 */
      .upload-text {
        font-size: 24rpx;
        color: #66666e;
      }

      /* 按下状态 */
      &:active {
        background-color: #eeeeee;
      }
    }
  }

  /* 提交按钮区域 */
  .submit-section {
    margin: 96rpx 16rpx 0;

    /* 提交按钮 */
    .submit-btn {
      height: 112rpx;
      display: flex;
      justify-content: center;
      text-align: center;
      background-color: #568DED;
      border-radius: 120rpx;
      width: 100%;
      font-size: 32rpx;
      color: #fff;
      border: none;
      transition: all 0.3s ease;

      /* 禁用状态 */
      &:disabled {
        background-color: #cccccc;
        color: #999999;
        cursor: not-allowed;
      }

      /* 按下状态 */
      &:active:not(:disabled) {
        background-color: #4a7bd9;
        transform: scale(0.98);
      }

      /* 加载状态 */
      &.loading {
        background-color: #cccccc;
        pointer-events: none;
      }
    }
  }
}