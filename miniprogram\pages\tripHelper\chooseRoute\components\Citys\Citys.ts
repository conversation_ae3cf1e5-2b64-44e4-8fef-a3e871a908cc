// pages/tripHelper/chooseRoute/components/Citys/Citys.ts
import appConfig from "../../../../../config/app.config";
import { getIpLocateApi } from "../../../../../api/tripHelper/routeLine";

interface IpLocateResponse {
  locationCode: string;
  locationName: string;
}
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的城市
    selectedCity: {
      type: Object,
      value: {} as { key: string; value: string }
    },
    // 热门城市列表，由父页面传递
    hotCities: {
      type: Array,
      value: [] as RouteLine.HotCity[]
    },
    // 区域列表，由父页面传递
    regionList: {
      type: Array,
      value: [] as RouteLine.RegionList[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl,
    currentLocation: '点击获取当前位置信息', // 当前位置默认文本
    locationStatus: 'default', // 定位状态：default-默认, loading-定位中, success-成功, error-失败
    historyList: [] as { key: string; value: string }[], // 历史记录
    alphabetList: ['热门', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'W', 'X', 'Y', 'Z'],
    scrollTargetId: '',
    locationCity:{} as IpLocateResponse
  },

  lifetimes: {
    attached() {
      let {selectedCity} = this.data;  
      let locationCity = wx.getStorageSync('locationCity') || {};
      if(locationCity.locationCode==selectedCity.key){
        this.setData({
          locationStatus: 'success',
          currentLocation: locationCity.locationName,
          locationCity
        })
      }
      // 如果有定位信息但未选中，也要显示定位城市名称
      else if(locationCity.locationName) {
        this.setData({
          currentLocation: locationCity.locationName,
          locationCity
        })
      }
      this.loadHistoryList()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 加载历史记录
    loadHistoryList() {
      try {
        const history = wx.getStorageSync('cityHistory') || [];
        this.setData({
          historyList: history.slice(0, 8) // 最多显示8个
        });
      } catch (error) {
        console.error('加载历史记录失败:', error)
      }
    },

    // 保存城市到历史记录
    saveCityToHistory(city: { key: string; value: string }) {
      try {
        let history = wx.getStorageSync('cityHistory') || [];
        // 移除重复项（以key为唯一标识）
        history = history.filter((item: { key: string; value: string }) => item.key !== city.key);
        // 添加到开头
        history.unshift(city);
        // 最多保存20个
        history = history.slice(0, 20);
        wx.setStorageSync('cityHistory', history);
        this.loadHistoryList();
      } catch (error) {
        console.error('保存历史记录失败:', error);
      }
    },

    // 清空历史记录
    clearHistory() {
      wx.showModal({
        title: '提示',
        content: '确定要清空历史记录吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              wx.removeStorageSync('cityHistory')
              this.setData({
                historyList: []
              })
              wx.displayToast({
                title: '已清空',
                icon: 'success'
              })
            } catch (error) {
              console.error('清空历史记录失败:', error)
            }
          }
        }
      })
    },

    // 选择城市
    selectCity(e: any) {
      const { key, value,replace=true } = e.currentTarget.dataset;
      if (!value) return;
      
      // 判断是否点击已选中的城市，如果是则取消选中
      const { selectedCity } = this.properties;
      if (selectedCity && selectedCity.key === key && replace) {
        // 取消选中
        this.triggerEvent('citySelect', {
          key: '',
          value: ''
        });
        return;
      }
      
      // 保存到历史记录
      this.saveCityToHistory({ key, value});
      // 触发选择事件
      this.triggerEvent('citySelect', {
        key, value
      });
    },

    // 选择当前位置
    selectCurrentLocation() {
      let {locationCity,locationStatus} = this.data;
      if(locationCity.locationCode){
        this.selectCity({
          currentTarget: {
            dataset: {
              key: locationCity.locationCode,
              value: locationCity.locationName
            }
          }
        });
        return;
      }
      // 如果当前是失败状态，重新定位
      if (locationStatus === 'error') {
        this.getCurrentLocation();
        return;
      }
      
      // 如果当前是默认状态，开始定位
      if (locationStatus === 'default') {
        this.getCurrentLocation();
        return;
      }
      
      // 如果定位成功，选择当前位置
      // if (this.data.locationStatus === 'success') {
      //   this.selectCity({
      //     currentTarget: {
      //       dataset: {
      //         key: this.data.locationCity.locationCode,
      //         value: this.data.locationCity.locationName
      //       }
      //     }
      //   });
      // }
    },
    
    // 获取当前位置信息
    async getCurrentLocation() {
      // 设置定位中状态
      this.setData({
        locationStatus: 'loading',
        currentLocation: '定位中',
        locationCity:{} as IpLocateResponse
      });
      
      try {
        const res = await getIpLocateApi();
        
        if (res.code === 200) {
          wx.setStorageSync('locationCity', res.data);
          // 定位成功
          this.setData({
            locationStatus: 'success',
            currentLocation: res.data.locationName,
            locationCity:res.data
          });
          this.selectCity({
            currentTarget: {
              dataset: {
                key: res.data.locationCode,
                value: res.data.locationName,
                replace:false
              }
            }
          });
        } else {
          // 定位失败
          this.setData({
            locationStatus: 'error',
            currentLocation: '定位失败，点击重新定位'
          });
        }
      } catch (error) {
        console.error('定位失败:', error);
        // 定位失败
        this.setData({
          locationStatus: 'error',
          currentLocation: '定位失败，点击重新定位'
        });
      }
    },

    // 字母索引点击
    onAlphabetClick(e: any) {
      const { letter } = e.currentTarget.dataset;
      const id = letter === '热门' ? 'scroll-to-hot' : `scroll-to-${letter}`;
      this.setData({ scrollTargetId: id });
    }
  }
})