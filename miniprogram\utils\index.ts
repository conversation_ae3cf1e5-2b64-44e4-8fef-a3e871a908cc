import appConfig from "../config/app.config";

/** 是否是生产 */
export const isProdEnv = appConfig.env === "prod";

/**
 * 延迟指定秒数
 * @param time 指定秒数
 * @returns
 */
export const delay = (time: number) =>
  new Promise((resolve) => setTimeout(resolve, time));

/**
 * 防抖函数
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @returns 返回防抖处理后的函数
 */
export function debounce<T extends (...args: any[]) => void>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: Nullable<number> = null;

  return (...args: Parameters<T>) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay) as unknown as number;
  };
}

/**
 * 节流函数
 * @param fn 要执行的函数
 * @param delay 间隔时间（毫秒）
 * @param options 配置项：{ leading: 是否立即执行, trailing: 是否在间隔结束后执行 }
 * @returns 返回节流处理后的函数
 */
export function throttle<T extends (...args: any[]) => void>(
  fn: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean } = { leading: true, trailing: true }
): (...args: Parameters<T>) => void {
  let lastExecTime = 0;
  let timer: Nullable<number> = null;
  const { leading = true, trailing = true } = options;

  return (...args: Parameters<T>) => {
    const now = Date.now();

    // Leading 执行（立即执行）
    if (leading && (now - lastExecTime >= delay || lastExecTime === 0)) {
      fn(...args);
      lastExecTime = now;
    } else if (trailing) {
      // Trailing 执行（延迟执行）
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        fn(...args);
        lastExecTime = now;
        timer = null;
      }, delay - (now - lastExecTime)) as unknown as number;
    }
  };
}

/**
 * 检查是否为正整数
 * @param str 数字字符串
 */
export const isPositiveInteger = (str: string) => /^[1-9]\d*$/.test(str);