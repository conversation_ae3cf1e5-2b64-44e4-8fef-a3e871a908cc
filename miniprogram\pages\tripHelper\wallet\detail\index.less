.transaction-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;

    .loading-text {
      color: #999;
      font-size: 28rpx;
    }
  }

  .participant-order-container {
    width: 100%;

    .gathering-location-list {
      width: 100%;

      .gathering-location-item {
        background-color: #fff;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        overflow: hidden;

        .location-header {
          padding: 24rpx;
          border-bottom: 1rpx solid #f0f0f0;

          .location-info {
            margin-bottom: 16rpx;

            .location-name {
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 8rpx;
            }

            .location-address {
              font-size: 28rpx;
              color: #666;
              margin-bottom: 8rpx;
            }

            .gathering-time {
              font-size: 26rpx;
              color: #999;
            }
          }

          .location-stats {
            display: flex;
            gap: 32rpx;

            .stat-item {
              display: flex;
              flex-direction: column;
              align-items: center;

              .stat-label {
                font-size: 24rpx;
                color: #999;
                margin-bottom: 4rpx;
              }

              .stat-value {
                font-size: 28rpx;
                font-weight: 600;
                color: #333;
              }
            }
          }
        }

        .participants-list {
          width: 100%;

          .participant-item {
            border-bottom: 1rpx solid #f0f0f0;
            padding: 24rpx;

            &:last-child {
              border-bottom: none;
            }

            .participant-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 16rpx;

              .participant-info {
                display: flex;
                align-items: center;
                flex: 1;

                .participant-avatar {
                  width: 80rpx;
                  height: 80rpx;
                  border-radius: 40rpx;
                  margin-right: 16rpx;
                }

                .participant-details {
                  flex: 1;

                  .participant-name {
                    font-size: 30rpx;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4rpx;
                  }

                  .participant-mobile {
                    font-size: 26rpx;
                    color: #666;
                  }
                }
              }

              .participant-stats {
                display: flex;
                gap: 24rpx;
              }
            }

            .orders-list {
              width: 100%;

              .order-item {
                background-color: #f8f9fa;
                border-radius: 12rpx;
                padding: 20rpx;
                margin-bottom: 12rpx;

                &:last-child {
                  margin-bottom: 0;
                }

                .order-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 12rpx;

                  .order-no {
                    font-size: 26rpx;
                    color: #666;
                  }

                  .order-status {
                    font-size: 24rpx;
                    padding: 4rpx 12rpx;
                    border-radius: 12rpx;
                    background-color: #e3f2fd;
                    color: #1976d2;
                  }
                }

                .order-details {
                  margin-bottom: 12rpx;

                  .order-detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8rpx;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .detail-label {
                      font-size: 26rpx;
                      color: #666;
                    }

                    .detail-value {
                      font-size: 26rpx;
                      color: #333;
                      font-weight: 500;
                    }
                  }
                }

                .order-time {
                  display: flex;
                  align-items: center;
                  margin-bottom: 8rpx;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .time-label {
                    font-size: 24rpx;
                    color: #999;
                  }

                  .time-value {
                    font-size: 24rpx;
                    color: #666;
                  }
                }

                .order-remark {
                  display: flex;
                  align-items: flex-start;
                  margin-top: 8rpx;

                  .remark-label {
                    font-size: 24rpx;
                    color: #999;
                    margin-right: 8rpx;
                  }

                  .remark-value {
                    font-size: 24rpx;
                    color: #666;
                    flex: 1;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;

    .empty-text {
      color: #999;
      font-size: 28rpx;
    }
  }
}
