<!-- pages/tripHelper/chooseRoute/components/Filter/Filter.wxml -->
<view class="filter-container">
    <!-- 城市周边按钮 -->
    <view class="filter-item {{cityLabel.value || cityExpanded ? 'active' : ''}}" bind:tap="onCityClick">
        <text class="filter-text city-label">{{cityLabel.value || '城市周边'}}</text>
        <view class="filter-content">
            <t-icon name="chevron-down" size="32rpx" class="filter-arrow {{cityExpanded ? 'rotated' : ''}}" />
        </view>
    </view>
    <!-- 出行天数按钮 -->
    <view class="filter-item {{daysLabel || daysExpanded ? 'active' : ''}}" bind:tap="onDaysClick">
        <text class="filter-text days-label">{{daysLabel || '出行天数'}}</text>
        <view class="filter-content">
            <t-icon name="chevron-down" size="32rpx" class="filter-arrow {{daysExpanded ? 'rotated' : ''}}" />
        </view>
    </view>
</view>