<!--pages/tripHelper/my/edit/index.wxml-->
<view class="edit-page">
  <!-- 头像设置 -->
  <view class="edit-item">
    <text class="edit-label">头像</text>
    <view class="edit-content">
      <button class="avatar-button" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="avatar-image" src="{{userInfo.avatar || defaultAvatarUrl}}" mode="aspectFill"></image>
      </button>
      <image class="arrow-icon" src="{{arrowIcon}}" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 昵称设置 -->
  <view class="edit-item" bindtap="onNicknameClick">
    <text class="edit-label">昵称</text>
    <view class="edit-content">
      <text class="edit-value {{!userInfo.nickname ? 'placeholder-text' : ''}}">{{displayNickname}}</text>
      <image class="arrow-icon" src="{{arrowIcon}}" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 性别设置 -->
  <view class="edit-item" bindtap="onGenderClick">
    <text class="edit-label">性别</text>
    <view class="edit-content">
      <text class="edit-value {{userInfo.gender === '0' ? 'placeholder-text' : ''}}">{{displayGender}}</text>
      <image class="arrow-icon" src="{{arrowIcon}}" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 手机号显示（不可编辑） -->
  <view class="edit-item disabled">
    <text class="edit-label">手机号</text>
    <view class="edit-content">
      <text class="edit-value">{{userInfo.mobile}}</text>
    </view>
  </view>
</view>

<!-- 昵称编辑弹框 -->
<t-popup visible="{{showNicknameModal}}" placement="bottom" bind:visible-change="onNicknamePopupChange">
  <view class="popup-container">
    <view class="popup-header">
      <text class="popup-cancel" bindtap="onNicknameCancelClick">取消</text>
      <text class="popup-title">昵称</text>
      <text class="popup-confirm" bindtap="onNicknameConfirmClick">确定</text>
    </view>
    <view class="popup-content">
      <input
        class="nickname-input"
        placeholder="填写昵称"
        value="{{nicknameInput}}"
        bindinput="onNicknameInput"
        maxlength="20"
        auto-focus
      />
    </view>
  </view>
</t-popup>

<!-- 性别选择器 -->
<t-picker
  visible="{{showGenderPicker}}"
  title="性别"
  cancelBtn="取消"
  confirmBtn="确定"
  value="{{[userInfo.gender]}}"
  bindchange="onGenderPickerChange"
  bindcancel="onGenderPickerCancel"
>
  <t-picker-item options="{{genderOptions}}"></t-picker-item>
</t-picker>
