<view class="transaction-detail-page">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 参与人订单信息 -->
  <view class="participant-order-container" wx:if="{{!loading && participantOrderData}}">
    <!-- 集合地列表 -->
    <view class="gathering-location-list">
      <view
        class="gathering-location-item"
        wx:for="{{participantOrderData.gatheringLocations}}"
        wx:key="locationName"
      >
        <!-- 集合地信息 -->
        <view class="location-header">
          <view class="location-info">
            <view class="location-name">{{item.locationName}}</view>
            <view class="location-address">{{item.locationAddress}}</view>
            <view class="gathering-time">集合时间：{{item.gatheringTime}}</view>
          </view>
          <view class="location-stats">
            <view class="stat-item">
              <text class="stat-label">参与人数</text>
              <text class="stat-value">{{item.totalParticipants}}人</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">总金额</text>
              <text class="stat-value">¥{{item.totalAmount}}</text>
            </view>
          </view>
        </view>

        <!-- 参与人列表 -->
        <view class="participants-list">
          <view
            class="participant-item"
            wx:for="{{item.participants}}"
            wx:for-item="participant"
            wx:key="userId"
          >
            <!-- 参与人基本信息 -->
            <view class="participant-header">
              <view class="participant-info">
                <image
                  class="participant-avatar"
                  src="{{participant.avatar || defaultAvatar}}"
                  mode="aspectFill"
                />
                <view class="participant-details">
                  <view class="participant-name">{{participant.userName}}</view>
                  <view class="participant-mobile">{{participant.mobile}}</view>
                </view>
              </view>
              <view class="participant-stats">
                <view class="stat-item">
                  <text class="stat-label">订单数</text>
                  <text class="stat-value">{{participant.totalOrders}}</text>
                </view>
                <view class="stat-item">
                  <text class="stat-label">支付金额</text>
                  <text class="stat-value">¥{{participant.totalPayAmount}}</text>
                </view>
              </view>
            </view>

            <!-- 订单列表 -->
            <view class="orders-list">
              <view
                class="order-item"
                wx:for="{{participant.orders}}"
                wx:for-item="order"
                wx:key="orderId"
              >
                <view class="order-header">
                  <view class="order-no">订单号：{{order.orderNo}}</view>
                  <view class="order-status {{order.orderStatus}}">{{order.orderStatusDesc}}</view>
                </view>
                <view class="order-details">
                  <view class="order-detail-item">
                    <text class="detail-label">订单金额</text>
                    <text class="detail-value">¥{{order.orderAmount}}</text>
                  </view>
                  <view class="order-detail-item">
                    <text class="detail-label">已支付</text>
                    <text class="detail-value">¥{{order.paidAmount}}</text>
                  </view>
                  <view class="order-detail-item">
                    <text class="detail-label">人数</text>
                    <text class="detail-value">成人{{order.adultCount}}人 儿童{{order.childCount}}人</text>
                  </view>
                  <view class="order-detail-item" wx:if="{{order.refundAmount > 0}}">
                    <text class="detail-label">退款金额</text>
                    <text class="detail-value">¥{{order.refundAmount}}</text>
                  </view>
                </view>
                <view class="order-time">
                  <text class="time-label">下单时间：</text>
                  <text class="time-value">{{order.orderTime}}</text>
                </view>
                <view class="order-time" wx:if="{{order.payTime}}">
                  <text class="time-label">支付时间：</text>
                  <text class="time-value">{{order.payTime}}</text>
                </view>
                <view class="order-remark" wx:if="{{order.remark}}">
                  <text class="remark-label">备注：</text>
                  <text class="remark-value">{{order.remark}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && !participantOrderData}}">
    <view class="empty-text">暂无参与人订单信息</view>
  </view>
</view>
