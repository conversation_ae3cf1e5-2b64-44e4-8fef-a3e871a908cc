.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .popup-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: -100rpx;
    width: 598rpx;

    .image-top {
      width: 598rpx;
      height: 72rpx;
    }

    .img-box {
      width: 598rpx;
      height: 426rpx;
      background-size: 100% 100%;
      border-radius: 40rpx 184rpx 0rpx 0rpx;

      .image-middle {
        width: 598rpx;
        height: 82rpx;
      }
    }

    .bottom-box {
      position: relative;
      top: -56rpx;

      .price-box {
        width: 598rpx;
        height: 132rpx;
        background-image: url("https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_mid.png");
        background-size: 100% 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .content {
          margin-right: 30rpx;
          margin-bottom: 10rpx;
          display: flex;
          align-items: center;
          transform: rotate(-5deg);

          .left {
            font-weight: 500;
            font-size: 64rpx;
            color: #33333E;
            letter-spacing: 3rpx;
            font-style: italic;
          }

          .right {
            margin-left: 8rpx;
            display: flex;
            flex-direction: column;

            .t1 {
              font-size: 24rpx;
              color: #33333E;
              font-style: italic;
            }

            .t2 {
              line-height: 1;
              font-size: 20rpx;
              color: #33333E;
            }
          }
        }
      }

      .content-box {
        margin-top: -1rpx;
        width: 598rpx;
        background-color: #FFFFFF;
        border-radius: 0 0 40rpx 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 14rpx 0 34rpx 0;

        .left {
          margin-left: 32rpx;

          .title {
            font-weight: 500;
            font-size: 40rpx;
            color: #11111E;
            max-width: 360rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
          }

          .time-address-box {
            margin-top: 8rpx;
            display: flex;
            align-items: center;

            image {
              width: 24rpx;
              height: 24rpx;
              margin-right: 8rpx;
            }

            text {
              font-size: 24rpx;
              color: #99999E;
            }

            image:last-child {
              margin-left: 24rpx;
            }
          }

          .time-address-box text:nth-of-type(1) {
            padding-right: 24rpx;
          }
        }

        .right {
          margin-right: 32rpx;
          display: flex;
          flex-direction: column;
          align-items: center;

          image {
            width: 136rpx;
            height: 136rpx;
          }

          text {
            font-size: 20rpx;
            color: #99999E;
            margin-top: 4rpx;
          }
        }
      }
    }
  }

  .popup-bottom {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #FFFFFF;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .content {
      padding: 40rpx 30rpx 20rpx 30rpx;

      .btm-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          background-color: #FFFFFF;
          padding: 0;

          .btm {
            background: #F7F7F7;
            width: 80rpx;
            height: 80rpx;
            border-radius: 113rpx 113rpx 113rpx 113rpx;
            image {
              width: 40rpx;
              height: 40rpx;
              margin: 20rpx;
            }
          }

          text {
            margin-top: 24rpx;
            font-size: 20rpx;
            color: #66666E;
          }
        }
      }

      .cancel {
        display: flex;
        justify-content: center;
        margin-top: 60rpx;

      }
    }
  }
}