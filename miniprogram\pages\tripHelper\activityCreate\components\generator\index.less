.creater-page {
	height: calc(100vh - 76rpx);
	padding: 32rpx 0 180rpx;
	box-sizing: border-box;
	overflow-y: auto;
	background: @bg-gray-color;

	.creater-body {
		padding-bottom: 98rpx;
	}

	.card-wrap {
		border-radius: 32rpx;
		padding: 0 24rpx;
		margin: 24rpx 24rpx 0;
		background: #fff;
	}

	.cell-item {
		.border(1px, solid, @bg-gray-color, 0, bottom);
		padding: 32rpx 0;
		&:last-child {
			&::after {
				display: none;
			}
		}
	}

	.form-item {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		&-label {
			display: flex;
			align-items: center;
			&-icon {
				width: 36rpx;
				height: 36rpx;
				margin-right: 16rpx;
			}
			&-text {
				color: @text-title-color;
			}
		}
		&-content {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			color: @text-title-color;
			font-weight: bold;
			&.is-placeholder {
				font-weight: normal;
				color: @placeholder-text-color;
			}
			&::after {
				content: "";
				display: block;
				width: 24rpx;
				height: 24rpx;
				background-image: url('@{static-base-url}/arrow.png');
				background-size: 100%;
				margin-left: 24rpx;
			}

			.qrcode {
				width: 32rpx;
				height: 32rpx;
				background-image: url('@{static-base-url}/tripHelper/activity/qrcode.png');
				background-size: 100%;
			}
		}
		&-tips {
			color: @warning-color;
			font-size: 20rpx;
			padding-left: 52rpx;
			line-height: 28rpx;
			margin-top: 4rpx;
		}
	}
	.data-item {
		border-radius: 24rpx;
		background-color: @bg-light-gray-color;
		padding: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: @text-title-color;
		font-size: 28rpx;
		line-height: 40rpx;
		margin-top: 16rpx;
		.poi-text {
			flex: 1;
			min-width: 0;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			margin: 0 16rpx;
			font-weight: bold;
		}
		.delete-trigger {
			width: 24rpx;
			height: 24rpx;
			background-image: url('@{static-base-url}/tripHelper/chooseRoute/deleteIcon.png');
			background-size: 100%;
		}
		.policy-title {
			font-size: 28rpx;
			font-weight: bold;
			line-height: 48rpx;
		}
		.policy-desc {
			color: @text-color;
			font-size: 24rpx;
			line-height: 42rpx;
		}
	}
	.pb-24rpx {
		padding-bottom: 24rpx;
	}

	.wait-replace-wrap {
		display: flex;
		align-items: center;
		padding: 24rpx;
		margin-top: 32rpx;
		box-sizing: border-box;
		background: @border-color;
		border-radius: 24rpx;
		width: 318rpx;
		.wait-replace-label {
			display: flex;
			flex-direction: column;
			margin-right: 80rpx;
			image {
				width: 36rpx;
				height: 36rpx;
			}
			.explain-wrap {
				display: flex;
				align-items: center;
				margin-top: 16rpx;
				font-size: 28rpx;
				color: @text-title-color;
				line-height: 40rpx;
			}
		}
	}
	// 操作区
	.operate-wrap {
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 99;
		width: 100%;
		background: #fff;
		padding: 16rpx 32rpx 68rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		.draft-wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			color: @placeholder-text-color;
			font-size: 20rpx;
			line-height: 28rpx;
			width: 130rpx;
			margin-right: 46rpx;
			.draft-icon {
				width: 48rpx;
				height: 48rpx;
				margin-bottom: 4rpx;
				background-image: url('@{static-base-url}/tripHelper/activity/draft.png');
				background-size: 100%;
			}
		}
		.trigger-item {
			flex: 1;
			height: 96rpx;
			border-radius: 96rpx;
			color: #fff;
			background: @primary-color;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}