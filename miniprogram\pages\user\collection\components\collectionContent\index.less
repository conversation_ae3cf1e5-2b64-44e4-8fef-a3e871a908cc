/* 收藏内容展示组件样式 */

/* 收藏内容容器 */
.collection-content {
  margin: 16rpx;

  /* 编辑模式下添加底部间距，避免被底部操作栏遮挡 */
  &.edit-mode {
    padding-bottom: 200rpx; /* 底部操作栏高度 */
  }
}

/* 项目包装器 */
.item-wrapper {
  margin-bottom: 16rpx;
  position: relative;
  overflow: hidden;
}

/* 项目容器 */
.item-container {
  position: relative;
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;

  &.swiped {
    transform: translateX(-160rpx);
  }

  .checkbox-container {
    padding-left: 32rpx;
    padding-right: 16rpx;
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateX(-20rpx);
    transition: all 0.3s ease;
  }
}

/* 编辑模式下复选框滑入动画 */
.item-wrapper.edit-mode .checkbox-container {
  opacity: 1;
  transform: translateX(0);
}

/* 复选框图标样式 */
.checkbox-icon {
  width: 36rpx;
  height: 36rpx;
  transition: all 0.3s ease;

  /* 点击效果 */
  &:active {
    transform: scale(0.95);
  }
}

/* 卡片 */
.card {
  flex: 1;
  display: flex;
  margin: 0 16rpx;
  background-color: #fff;
  border-radius: 40rpx; /* 左侧圆角，右侧直角与删除按钮连接 */
  /* 酒店卡片 */
  .hotel-card {
    width: calc(100vw - 48rpx);
    position: relative; /* 为::after伪类提供定位基准 */
    background-color: #fff;
    border-radius: 40rpx; /* 左侧圆角，右侧直角与删除按钮连接 */

    /* 滑动状态下的伪类，创建与删除按钮背景色相同的连接区域 */
    &.swiped::after {
      content: '';
      position: absolute;
      right: -160rpx; /* 延伸到删除按钮区域 */
      top: 0;
      bottom: 0;
      width: 180rpx;
      background-color: #fff1f0; /* 与删除按钮背景色相同 */
      border-radius: 0 32rpx 32rpx 0; /* 右侧圆角 */
      z-index: -1; /* 置于删除按钮下方 */
    }
  }
}

/* 右滑删除按钮 */
.delete-btn {
  border-radius: 0rpx 40rpx 40rpx 0rpx;
  position: absolute;
  margin-right: 16rpx;
  right: -150rpx;
  top: 0;
  bottom: 0;
  width: 114rpx;
  background-color: #fff1f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: right 0.3s ease;

  .close-icon {
    height: 36rpx;
    width: 36rpx;
  }
  .delete-text {
    font-size: 24rpx;
    color: #f04838;
    margin-top: 8rpx;
  }
}
