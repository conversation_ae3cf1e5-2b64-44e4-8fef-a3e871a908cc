/* 通用下拉刷新和上拉加载列表组件样式 */
.refresh-load-list {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  /* 下拉刷新头部 */
  .refresh-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    z-index: 1;
    background-color: #f5f5f5;
    transform: translateY(-120rpx);
    transition: transform 0.3s ease;

    .refresh-content {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .refresh-status {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .refresh-icon {
          width: 32rpx;
          height: 32rpx;
          transition: transform 0.3s ease;

          &.rotate {
            transform: rotate(180deg);
          }
        }

        .refresh-text {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }

  /* 内容容器 */
  .content-container {
    width: 100%;
    height: 100vh;
    transition: transform 0.3s ease;
  }

  /* 初始加载状态 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 160rpx 0;
    gap: 24rpx;

    .loading-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  /* 列表容器 */
  .list-container {
    width: 100%;
  }

  /* 空状态 */
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 160rpx 32rpx;
    gap: 24rpx;

    .empty-text {
      font-size: 32rpx;
      color: #666;
      font-weight: 500;
    }

    .empty-desc {
      font-size: 28rpx;
      color: #999;
      text-align: center;
      line-height: 1.5;
    }
  }

  /* 上拉加载更多 */
  .load-more-container {
    padding: 32rpx 0;
    
    .load-more-item {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      
      .load-more-text {
        font-size: 28rpx;
        color: #999;
        
        &.no-more {
          color: #ccc;
        }
        
        &.pull-tip {
          color: #ddd;
        }
      }
    }
  }

  /* 页面级滚动模式容器 */
  .page-scroll-container {
    width: 100%;
    min-height: auto;

    .list-container {
      width: 100%;
    }

    .loading-container {
      padding: 40rpx;
      text-align: center;

      .loading-text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #999;
      }
    }

    .load-more-container {
      padding: 40rpx 0;
      text-align: center;

      .load-more-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;

        .pull-tip-icon {
          width: 32rpx;
          height: 32rpx;
        }

        .load-more-text {
          font-size: 24rpx;
          color: #999;

          &.no-more {
            color: #ccc;
          }

          &.pull-tip {
            color: #ddd;
          }
        }
      }
    }
  }
}


