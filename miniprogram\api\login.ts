// 登录相关API
import { request } from '../utils/request';

/**
 * 微信小程序登录
 * @param params 登录参数
 * @returns 登录响应
 */
export const wxMiniLogin = (params: Login.WxMiniLoginRequest): Promise<Request.IResponseResult<Login.LoginResponse>> => {
  return request<Login.LoginResponse>({
    showLoading: true,
    url: '/hotel/user/auth/miniWx/login',
    method: 'POST',
    data: params
  });
};

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = (): Promise<Request.IResponseResult<Login.UserInfo>> => {
  return request<Login.UserInfo>({
    url: '/hotel/user/info',
    method: 'GET'
  });
};

/**
 * 更新用户信息
 * @param params 用户信息参数
 * @returns 更新结果
 */
export const updateUserInfo = (params: Login.UpdateUserInfoRequest): Promise<Request.IResponseResult<Login.UpdateUserInfoResponse>> => {
  return request<Login.UpdateUserInfoResponse>({
    url: '/hotel/user/info/update',
    method: 'POST',
    data: params
  });
};

/**
 * 退出登录
 * @returns 退出结果
 */
export const logout = (): Promise<Request.IResponseResult<any>> => {
  return request<any>({
    url: '/hotel/user/auth/logout',
    method: 'POST'
  });
};
