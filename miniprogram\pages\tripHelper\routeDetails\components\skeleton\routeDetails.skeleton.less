/* 自定义骨架屏样式 */

/* 骨架屏容器 */
.skeleton-container {
  width: 100%;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 基础骨架屏元素样式 */
.skeleton-item {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
  border-radius: 8rpx;
}

/* 导航栏骨架 */
.skeleton-navbar {
  position: relative;
  background: #fff;
}

.skeleton-status-bar {
  height: 88rpx;
  background: #fff;
}

.skeleton-nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.skeleton-back-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-nav-title {
  width: 200rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-nav-right {
  width: 48rpx;
  height: 48rpx;
}

/* 轮播图骨架 */
.skeleton-swiper {
  position: relative;
  height: 500rpx;
  background: #f5f5f5;
}

.skeleton-swiper-item {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-swiper-dots {
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16rpx;
}

.skeleton-dot {
  width: 16rpx;
  height: 8rpx;
  border-radius: 4rpx;
  background: rgba(255, 255, 255, 0.4);
}

.skeleton-dot-active {
  width: 48rpx;
  background: rgba(255, 255, 255, 0.8);
}

/* 路线信息骨架 */
.skeleton-route-info {
  padding: 32rpx;
  background: #fff;
  margin-top: 32rpx;
}

.skeleton-route-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.skeleton-route-left {
  flex: 1;
}

.skeleton-route-title {
  width: 400rpx;
  height: 56rpx;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-route-tags {
  display: flex;
  gap: 16rpx;
}

.skeleton-tag {
  width: 120rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-route-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.skeleton-location-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-location-text {
  width: 120rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

/* 描述区域骨架 */
.skeleton-description {
  padding: 32rpx;
  background: #fff;
  margin-top: 32rpx;
}

.skeleton-section-title {
  width: 160rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-text-lines {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.skeleton-text-line {
  height: 32rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-text-line-short {
  width: 60%;
}

/* 行程详情骨架 */
.skeleton-itinerary {
  padding: 32rpx;
  background: #fff;
  margin-top: 32rpx;
}

.skeleton-day-section {
  margin-bottom: 48rpx;
}

.skeleton-day-section:last-child {
  margin-bottom: 0;
}

.skeleton-day-title {
  width: 120rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin-bottom: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-spot-item {
  display: flex;
  margin-bottom: 48rpx;
}

.skeleton-spot-item:last-child {
  margin-bottom: 0;
}

.skeleton-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32rpx;
  position: relative;
}

.skeleton-spot-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
  z-index: 2;
}

.skeleton-timeline-line {
  position: absolute;
  top: 56rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 4rpx;
  height: 200rpx;
  background: #e0e0e0;
  border-radius: 2rpx;
}

.skeleton-spot-content {
  flex: 1;
  padding-bottom: 32rpx;
}

.skeleton-spot-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.skeleton-spot-name {
  width: 200rpx;
  height: 40rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-spot-type {
  width: 120rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

.skeleton-spot-desc {
  margin-bottom: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.skeleton-spot-images {
  display: flex;
  gap: 16rpx;
}

.skeleton-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}

/* 底部按钮骨架 */
.skeleton-bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
}

.skeleton-action-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 37%, #f0f0f0 63%);
  background-size: 400px 100%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
} 