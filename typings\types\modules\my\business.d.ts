/**
 * 业务相关类型定义
 * 用于定义项目中特定业务逻辑的类型
 */

/**
 * 收藏相关类型定义
 */
declare namespace Collection {
  /**
   * 收藏项目基础接口
   */
  interface BaseItem {
    /** 项目ID */
    id: string;
    /** 项目标题 */
    title: string;
    /** 项目图片 */
    image?: string;
    /** 创建时间 */
    createTime: string;
    /** 是否选中（编辑模式下使用） */
    selected?: boolean;
  }

  /**
   * 酒店收藏项目
   */
  interface HotelItem extends BaseItem {
    /** 酒店名称 */
    hotelName: string;
    /** 酒店地址 */
    address: string;
    /** 酒店价格 */
    price: number;
    /** 酒店评分 */
    rating: number;
  }

  /**
   * 通用收藏项目类型
   */
  type Item = HotelItem | BaseItem;

  /**
   * 收藏列表类型
   */
  type List = Item[];

  /**
   * Tab类型
   */
  type TabType = 'favorite' | 'viewed' | 'stayed' | 'liked';

  /**
   * 分页参数
   */
  interface PageParams {
    page: number;
    hasMore: boolean;
  }
}

/**
 * 用户信息相关类型定义
 */
declare namespace UserInfo {
  /**
   * 性别选项
   */
  interface GenderOption {
    label: string;
    value: string;
  }

  /**
   * 菜单项
   */
  interface MenuItem {
    icon: string;
    title: string;
    path: string;
  }

  /**
   * 头像选项
   */
  interface AvatarOption {
    url: string;
    selected: boolean;
  }
}

/**
 * 反馈相关类型定义
 */
declare namespace Feedback {
  /**
   * 反馈类型选项
   */
  interface TypeOption {
    label: string;
    value: string;
  }

  /**
   * 反馈表单数据
   */
  interface FormData {
    selectedType: string;
    feedbackContent: string;
    uploadedImages: string[];
    uploadedImageUrls: string[];
  }
}

/**
 * 组件事件详情类型
 */
declare namespace ComponentEvent {
  /**
   * 组件触发事件详情类型
   */
  interface Detail {
    /** 项目ID */
    itemId?: string;
    /** 索引 */
    index?: number;
    /** 滑动距离 */
    deltaX?: number;
    /** 滑动时间 */
    deltaTime?: number;
    /** 昵称 */
    nickname?: string;
    /** 酒店数据（转换后的格式） */
    hotelData?: {
      hotelId: string;
      lowRate: string;
      currencySymbol: string;
      hotel: {
        hotelName: string;
        thumbNailUrl: string;
        review: { score: number; starRate: number };
        districtName: string;
        businessZoneName: string;
        features: string[];
      };
      originalData: Collection.Item;
    };
    /** 原始收藏数据 */
    originalData?: Collection.Item;
    /** 其他自定义数据 */
    [key: string]: unknown;
  }
}
