/* pages/order/list/components/orderCard/index.less */
.order-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .status-section {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .status-label {
        margin-left: 16rpx;
        font-size: 24rpx;
        color: #66666E;
      }
    }

    .status-gang {
      padding: 0 20rpx;
      color: #F0F0F0;
    }

    .status-text {
      font-size: 24rpx;
      color: #11111E;
      font-weight: 500;
    }
  }

  .hotel-info {
    margin-bottom: 12rpx;

    .hotel-name {
      font-size: 32rpx;
      color: #11111E;
      font-weight: 600;
      line-height: 44rpx;
      margin-bottom: 8rpx;
    }

    .hotel-address {
      font-size: 24rpx;
      color: #11111E;
    }
  }

  .order-details {
    margin-bottom: 16rpx;

    .date-info {
      margin-bottom: 4rpx;

      .date-text {
        font-size: 28rpx;
        color: #11111E;
      }
    }
  }

  .card-footer {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    flex-direction: column;

    .price-section {
      display: flex;
      align-items: baseline;
      gap: 8rpx;
      text-align: right;

      .price-label {
        font-size: 20rpx;
        color: #568DED;
      }

      .currency {
        font-size: 20rpx;
        color: #568DED;
        font-weight: 500;
      }

      .price {
        font-size: 44rpx;
        color: #568DED;
        font-weight: 600;
      }
    }

    .action-buttons {
      margin-top: 32rpx;
      display: flex;
      gap: 16rpx;

      .action-btn {
        padding: 16rpx 44rpx;
        line-height: 72rpx;
        border-radius: 48rpx;
        font-size: 28rpx;
        text-align: center;
        font-weight: bold;
        min-width: 160rpx;
        transition: all 0.3s;

        &.primary {
          background-color: #F7F7F7;
          color: #33333E;

          &:active {
            background-color: #F7F7F7;
          }
        }

        &.secondary {
          background-color: #568DED;
          color: #fff;

          &:active {
            background-color: #568DED;
          }
        }
      }
    }
  }

  &:active {
    background-color: #fafafa;
  }
}