// pages/hotel/hotelList/components/MoreFilter/MoreFilter.ts

// 移除重复的接口定义，使用全局类型定义或在需要时从外部导入
interface FilterOption {
  typeId: number;
  filterId: string;
  name: string;
  nameEn?: string | null;
  describe?: string | null;
  multi: number; // 0:单选 1:多选
  parentTypeId: number;
  subFilters?: FilterOption[];
  grey?: any | null;
  selected?: boolean; // 选中状态
}

interface SelectedFilter {
  typeId: number;
  filterId: string;
  parentTypeId: number;
  category: string;
}

interface FilterChangeEvent {
  selectedFilters: SelectedFilter[];
  selectedCount: number;
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 筛选选项数据
    filterOptions: {
      type: Array,
      value: [] as FilterOption[]
    },
    // 已选中的筛选项（从父组件传入）
    selectedFilters: {
      type: Array,
      value: [] as SelectedFilter[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 当前选中的一级分类索引
    currentCategoryIndex: 0,
    // 滚动到的锚点ID
    scrollIntoView: '',
    // 各分类的展开状态，key格式: "typeId_level"
    expandStates: {} as Record<string, boolean>,
    // 内部维护的选中筛选项
    internalSelectedFilters: [] as SelectedFilter[]
  },

  /**
   * 组件生命周期
   */
  observers: {
    'filterOptions'(newVal: FilterOption[]) {
      if (newVal && newVal.length > 0) {
        // 初始化时设置第一个分类为激活状态
        this.setData({
          currentCategoryIndex: 0
        });
      }
    },
    'selectedFilters'(newVal: SelectedFilter[]) {
      // 同步外部传入的选中状态到内部字段
      this.setData({
        internalSelectedFilters: newVal || []
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击左侧分类导航
    onCategoryNavClick(e: any) {
      const { index, typeId } = e.currentTarget.dataset;
      
      this.setData({
        currentCategoryIndex: index,
        scrollIntoView: `category-${typeId}`
      });
      
      // 清除滚动锚点，避免影响后续滚动
      setTimeout(() => {
        this.setData({
          scrollIntoView: ''
        });
      }, 300);
    },

    // 获取展开状态key
    getExpandKey(typeId: number, level: string): string {
      return `${typeId}_${level}`;
    },

    // 切换展开状态
    toggleExpand(e: any) {
      const { typeId, level } = e.currentTarget.dataset;
      const expandKey = this.getExpandKey(typeId, level);
      const { expandStates } = this.data;
      
      this.setData({
        [`expandStates.${expandKey}`]: !expandStates[expandKey]
      });
    },

    // 获取显示的选项列表（处理展开收起）
    getDisplayItems(items: FilterOption[], parentTypeId: number, level: string): FilterOption[] {
      if (!items || items.length <= 6) {
        return items || [];
      }
      
      const expandKey = this.getExpandKey(parentTypeId, level);
      const isExpanded = this.data.expandStates[expandKey];
      
      return isExpanded ? items : items.slice(0, 6);
    },

    // 检查是否需要展开按钮
    needExpandButton(items: FilterOption[]): boolean {
      return items && items.length > 6;
    },

    // 检查是否展开
    isExpanded(typeId: number, level: string): boolean {
      const expandKey = this.getExpandKey(typeId, level);
      return !!this.data.expandStates[expandKey];
    },

    // 检查选项是否选中
    isOptionSelected(option: FilterOption): boolean {
      return this.data.internalSelectedFilters.some(
        item => item.typeId === option.typeId && item.filterId === option.filterId
      );
    },

    // 检查一级类目是否有选中项（根据parentTypeId判断）
    categoryHasSelected(categoryTypeId: number): boolean {
      return this.data.internalSelectedFilters.some(
        item => item.parentTypeId === categoryTypeId
      );
    },

    // 检查是否为三级结构
    isThreeLevelStructure(item: FilterOption): boolean {
      return !!(item.subFilters && item.subFilters.length > 0 && 
                item.subFilters[0].subFilters && item.subFilters[0].subFilters.length > 0);
    },

    // 检查是否为二级结构
    isTwoLevelStructure(item: FilterOption): boolean {
      return !!(item.subFilters && item.subFilters.length > 0 && 
                (!item.subFilters[0].subFilters || item.subFilters[0].subFilters.length === 0));
    },

    // 点击选项
    onOptionClick(e: any) {
      const { option, parentOption } = e.currentTarget.dataset;
      const { internalSelectedFilters } = this.data;
      
      // 判断是单选还是多选
      const isMulti = parentOption ? parentOption.multi === 1 : option.multi === 1;
      
      // 检查是否已选中
      const existingIndex = internalSelectedFilters.findIndex(
        item => item.typeId === option.typeId && item.filterId === option.filterId
      );
      
      let newSelectedFilters: SelectedFilter[];
      
      if (existingIndex >= 0) {
        // 取消选中
        newSelectedFilters = internalSelectedFilters.filter((_, index) => index !== existingIndex);
      } else {
        // 添加选中
        const newSelection: SelectedFilter = {
          typeId: option.typeId,
          filterId: option.filterId,
          parentTypeId: option.parentTypeId,
          category: 'filter'
        };
        
        if (isMulti) {
          // 多选：直接添加
          newSelectedFilters = [...internalSelectedFilters, newSelection];
        } else {
          // 单选：先移除同类型的其他选择，再添加新选择
          newSelectedFilters = internalSelectedFilters.filter(item => item.typeId !== option.typeId);
          newSelectedFilters.push(newSelection);
        }
      }
      
      this.setData({
        internalSelectedFilters: newSelectedFilters
      });

      // 触发选择变更事件
      this.triggerEvent('filterChange', {
        selectedFilters: newSelectedFilters,
        selectedCount: newSelectedFilters.length
      });
    },

    // 重置按钮点击
    onResetClick() {
      this.setData({
        internalSelectedFilters: []
      });
      
      // 触发重置事件
      this.triggerEvent('moreFilterReset');
    },

    // 完成按钮点击
    onConfirmClick() {
      const { internalSelectedFilters } = this.data;
      
      // 统一的关闭弹窗事件
      this.triggerEvent('filterPopupClose', {
        selectedFilters: internalSelectedFilters,
        filterType: 'filter'
      });
    }
  }
})