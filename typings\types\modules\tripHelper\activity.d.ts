declare namespace Activity {
	interface IProject {
		/** 激活icon */
		activeUrl: string;
		/** 未激活icon */
		inactiveUrl: string;
		/** 项目名称 */
		name: string;
		/** 项目编码 */
        id: number;
	}

	interface ICost {
		/** 费用类型 */
		type: number;
		/** 金额 */
		perCost: Nullable<number>;
	}

	interface IPlace extends ILocation {
		/** 集合时间 */
		startTime: string;
		/** 集合地点名称 */
		placeName: string;
	}

	/** 创建/更新活动参数 */
	interface ICreateUpdateActivityPayload {
		id?: number;
		/** 活动标题 */
		title: string;
		/** 活动描述 */
		description: string;
		/** 图片集合 */
		coverImages: string[];
		/** 路线id */
		routeId: Nullable<number>;
		/** 活动类型 */
		activityType: Nullable<number>;
		/** 活动开始时间 */
		startTime: string;
		/** 活动结束时间 */
		endTime: string;
		/** 活动最大人数 */
		ticketCount: Nullable<number>;
		/** 活动最少人数 */
		grouponCount: Nullable<number>;
		/** 报名截止时间 */
		deadlineTime: string;
		/** 微信群二维码 */
		wxImage: string;
		/** 是否允许候补 1:允许 0:不允许 */
		allowWaiting: number;
		/** 取消政策 */
		cancelPolicyId: Nullable<number>;
		/** 集合地点 */
		assemblyPlaces: Array<IPlace>;
		/** 收费信息 */
		perCost: Array<ICost>;
	}

	interface IPolicy {
		id: number;
		name: string;
		desc: string;
	}


	interface IAIAnalysisResult {
		/** 图片 */
		images: string[];
		/** 内容 */
		output: string;
		/** 标题 */
		title: string;
	}
}