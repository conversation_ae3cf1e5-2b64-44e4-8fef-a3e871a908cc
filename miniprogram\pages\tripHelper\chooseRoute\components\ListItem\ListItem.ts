// pages/tripHelper/chooseRoute/components/ListItem/ListItem.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 路线数据
    routeData: {
      type: Object,
      value: {}
    },
    // 是否选中
    selected: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 选择状态图标
    noSelectIcon: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/chooseRoute/noSelect.png',
    selectedIcon: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/chooseRoute/seleceted.png',
    punchedIcon: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/chooseRoute/punched.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击选择按钮 - 直接处理选中状态切换
    onSelectClick() {
      const routeData = this.properties.routeData as any;
      const newSelectedState = !this.properties.selected;

      
      // 通知父组件状态变化，让父组件处理状态管理
      this.triggerEvent('selectRoute', {
        routeId: routeData.routeId, // 使用routeId而不是id
        selected: newSelectedState,
        routeData: this.properties.routeData
      });
    },

    // 点击右侧内容区域 - 直接跳转到路线详情页
    onContentClick() {
      const routeData = this.properties.routeData as any;

      wx.navigateTo({
        url: `/pages/tripHelper/routeDetails/routeDetails?routeId=${routeData.routeId}` // 使用routeId而不是id
      });
    }
  }
})