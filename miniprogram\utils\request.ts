import { isProdEnv, delay } from './index';
import appConfig from '../config/app.config';
import { isLoginExpired } from './userInfo';
import { LoginManager } from './loginManager';
import { showToast } from '../utils/wx';
import { StorageKeyEnum, APIResponseCode } from '../enum/index';

const isRequestSuccess = (
  code: number,
): boolean => code === APIResponseCode.SUCCESS;

/**
 * 构建当前页面的完整URL（包含参数）
 */
const buildCurrentPageUrl = (page: WechatMiniprogram.Page.Instance<any, any> | undefined): string => {
  if (!page) return '';

  const route = page.route || '';
  const options = page.options || {};

  // 构建参数字符串
  const params = Object.keys(options)
    .map(key => `${key}=${encodeURIComponent(options[key] || '')}`)
    .join('&');

  // 返回完整路径
  return params ? `/${route}?${params}` : `/${route}`;
};

export const baseRequest = <
  D extends boolean | string | number | AnyObject | ArrayBuffer
>({
  baseURL = appConfig.baseUrl + '/api',
  method = 'GET',
  controller,
  timeout = appConfig.requestTimeout,
  params = {},
  ...config
}: Request.IRequestOption<any>): Promise<
  WechatMiniprogram.RequestSuccessCallbackResult<Request.IResponseResult<D>>
> => {
  return new Promise((resolve, reject) => {
    if (!config.header) config.header = {};

    const token = wx.getStorageSync(StorageKeyEnum.Token);
 
    if (token) {
      config.header['HT-Token'] = token;
    }

    if (method.toUpperCase() === 'GET' && Object.keys(params).length && !config.url.includes('?')) {
      /**  */
      config.url += `?${Object.entries(params).map((item) => {
        return `${item[0]}=${item[1]}`;
      }).join('&')}`;
    }

    // 接口需求登录，且没有 token，跳转至登录页
    // if (config.forceLogin && !token) {
    //   // TODO: 跳转至登录
    //   return;
    // }

    Object.assign(config.header, appConfig.defaultHeaders);

    if (!isProdEnv) {
      // console.info(`request:\t${config.url}  接口的最终传参: \n`, config.data);
    }

    if (config.mockData) {
      resolve({
        data: config.mockData,
      } as WechatMiniprogram.RequestSuccessCallbackResult<Request.IResponseResult<D>>);
      // console.info(`request:\t${config.url}  mockData: \n`, config.mockData);
      return;
    }

    const requestTask = wx.request({
      ...config,
      timeout,
      url: `${baseURL}${config.url}`,
      method,
      success: (
        res: WechatMiniprogram.RequestSuccessCallbackResult<
          Request.IResponseResult<D>
        >,
      ) => {
        // 未登录状态，处理token过期
        if (isLoginExpired(res.data.code) && !config.ignoreLoginExpired) {
          // 设置token过期状态
          LoginManager.handleTokenExpired();

          // 检查当前页面是否已经是登录页
          const currentPages = getCurrentPages();
          const currentPage = currentPages[currentPages.length - 1];
          const currentRoute = currentPage ? currentPage.route : '';

          // 如果当前不是登录页，才跳转到登录页
          if (currentRoute !== 'pages/login/index') {
            // 构建当前页面的完整路径作为backUrl
            const currentPageWithParams = buildCurrentPageUrl(currentPage);
            LoginManager.navigateToLogin(currentPageWithParams, true);
          }

          // 重要：必须调用reject，否则Promise会一直pending，loading不会消失
          // 创建一个特殊的错误对象，标记为登录过期，避免显示错误提示
          const loginExpiredError = new Error('登录已过期，请重新登录');
          (loginExpiredError as any).isLoginExpired = true;
          reject(loginExpiredError);
          return;
        }
        // console.info(`request:\t${config.url}  接口的响应内容: \n`, res);
        resolve(res);
      },
      fail: (err) => {
        reject(err);
      },
    });

    if (controller) {
      controller.task = requestTask;
    }
  });
};

/**
 * 做了错误处理的 request，需要原始的 request 可以用 baseRequest
 *
 * 1. 对于 request 报错的调用，toast 提示 error 对象的 message || '请重试'
 * 2. 对于 http 状态码 不为 200 或 code 不为 200 的 response，toast 提示 接口返回的 message || '请重试!'
 */
export const request = async <
  Result extends boolean | string | number | AnyObject | ArrayBuffer = AnyObject
>(
  options: Request.IRequestOption,
): Promise<Request.IResponseResult<Result>> => {
  // 处理loading显示
  const shouldShowLoading = options.showLoading // 默认不显示loading

  if (shouldShowLoading) {
    const loadingOptions: WechatMiniprogram.ShowLoadingOption = {
      title: '',
      mask: true,
      ...options.loadingOptions
    };
    wx.showLoading(loadingOptions);
  }

  try {
    const [res] = await Promise.all([
      baseRequest<Result>(options),
      delay(options?.delayResponse ?? 0),
    ]);

    // 确保 res.data 是对象类型，如果不是则包装成对象
    if (typeof res.data !== 'object' || res.data === null) {
      // 如果响应数据不是对象，包装成标准格式
      const originalData = res.data;
      console.warn('API响应数据不是对象格式:', {
        url: options.url,
        statusCode: res.statusCode,
        originalData: originalData
      });

      res.data = {
        code: res.statusCode === 200 ? 200 : (res.statusCode || -1),
        data: originalData,
        message: res.statusCode === 200 ? '请求成功' : '请求失败'
      };
    }

    // 只有在数据格式正确且请求失败时才显示错误提示
    if (res.data && typeof res.data === 'object' && res.data.code && !isRequestSuccess(res.data.code) && options.ignoreApiError) {
      showToast({
        title: res.data.message || '请重试!',
      });

      if (!isProdEnv) {
        // console.log(
        //   `request:\t${options.url}  ApiError: \n`,
        //   JSON.stringify(res, null, 2),
        // );
      }
    }

    // 安全地添加 isSuccess 属性
    if (res.data && typeof res.data === 'object') {
      res.data.isSuccess = isRequestSuccess(res.data?.code);
    }

    return res.data;
  } catch (err: any) {
    // 如果是登录过期错误，不显示toast提示
    if (!options.ignoreApiError && !err.isLoginExpired) {
      showToast({
        title: err.message || '请重试',
      });
    }

    if (!isProdEnv) {
      // console.log(
      //   `request:\t${options.url}  ResponseError: \n`,
      //   JSON.stringify(err, null, 2),
      // );
    }

    return {
      isSuccess: false,
      code: -1,
      data: null as unknown as Result,
      message: err?.errMsg || err?.message || '',
    };
  } finally {
    // 隐藏loading
    if (shouldShowLoading) {
      wx.hideLoading();
    }
  }
};