import { getWithdrawRecords } from '../../../../api/wallet'



Page({
  data: {
    // 提现记录列表（直接存储接口返回的数据）
    withdrawRecords: [] as Wallet.IWithdrawRecord[],

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: false,

    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false
  },

  onLoad() {
    // 页面加载时获取提现记录
    this.loadWithdrawRecords();
  },

  /**
   * 加载提现记录列表
   */
  async loadWithdrawRecords(isLoadMore = false) {
    try {
      if (!isLoadMore) {
        this.setData({ loading: true });
      } else {
        this.setData({ loadingMore: true });
      }

      // 调用真实API接口
      const response = await getWithdrawRecords({
        page: this.data.pageNum,
        size: this.data.pageSize
      });

      if (response.code === 200 && response.data !== undefined) {
        // 直接使用返回的数组数据
        const records = response.data;

        if (isLoadMore) {
          // 上拉加载更多
          this.setData({
            withdrawRecords: [...this.data.withdrawRecords, ...records],
            pageNum: this.data.pageNum + 1,
            // 根据返回数据量判断是否还有更多
            hasMore: records.length >= this.data.pageSize
          });
        } else {
          // 首次加载
          this.setData({
            withdrawRecords: records,
            pageNum: 2,
            // 根据返回数据量判断是否还有更多
            hasMore: records.length >= this.data.pageSize
          });
        }
      } else {
        console.error('获取提现记录失败:', response.message);
        wx.showToast({
          title: response.message || '获取提现记录失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载提现记录异常:', error);
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      });
    }
  },



  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({
      refreshing: true,
      pageNum: 1
    });
    this.loadWithdrawRecords();
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }
    this.loadWithdrawRecords(true);
  },


})
