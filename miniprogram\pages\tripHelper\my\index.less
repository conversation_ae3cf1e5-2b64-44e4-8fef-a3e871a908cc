/* pages/tripHelper/my/index.wxss */
page {
  background-color: #F3F3F3;
  padding-bottom: 164rpx; /* 为自定义tabbar预留空间 */
}

.trip-my-container {
  min-height: 100vh;

  /* 固定在顶部的导航栏 */
  .fixed-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    transition: opacity 0.3s ease, visibility 0.3s ease;

    .weui-navigation-bar {
      position: relative;
    }
  }
}

  .header-section {
    position: relative;
    height: 594rpx;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .top-buttons {
      position: absolute;
      bottom: 32rpx;
      right: 32rpx;
      display: flex;
      gap: 16rpx;

      .edit-btn {
        color: #33333E;
        font-size: 24rpx;
        height: 58rpx;
        padding: 12rpx 38rpx;
        text-align: center;
        line-height: 58rpx;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 120rpx;
      }
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 120rpx;

      .user-avatar-container {
        position: relative;
        margin-bottom: 16rpx;

        .user-avatar {
          width: 128rpx;
          height: 128rpx;
          border-radius: 87rpx;
          border: 4rpx solid #fff;
        }

        .gender-icon {
          position: absolute;
          right: 0rpx;
          bottom: 0rpx;
          width: 24rpx;
          height: 24rpx;
          padding: 10rpx;
          border-radius: 80rpx;
          // 背景色通过内联样式动态设置
        }
      }

      .user-nickname {
        font-size: 28rpx;
        // 颜色通过内联样式动态设置
      }
    }
  }

  .menu-section {
    background: #ffffff;
    margin: 32rpx 32rpx 0;
    border-radius: 32rpx;
    height: 194rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .menu-icon-container {
        position: relative;
        width: 64rpx;
        height: 64rpx;
        margin-bottom: 16rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .menu-icon {
          width: 64rpx;
          height: 64rpx;
        }

        .message-badge {
          position: absolute;
          top: -16rpx;
          height: 32rpx;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          display: flex;
          align-items: center;
          justify-content: center;

          .badge-text {
            color: #ffffff;
            font-size: 21.33rpx;
            font-weight: 500;
            text-align: center;
          }
        }
      }

      .menu-title {
        color: #66666E;
        font-size: 24rpx;
      }
    }
  }

  .activity-list {
    padding: 24rpx 32rpx;

    .activity-item {
      position: relative;
      background: #ffffff;
      border-radius: 32rpx;
      margin-bottom: 24rpx;
      padding: 24rpx;

      .activity-top {
        display: flex;
        position: relative;

        .activity-image {
          width: 144rpx;
          height: 144rpx;
          border-radius: 16rpx;
          margin-right: 24rpx;
          flex-shrink: 0;
        }

        .activity-info {
          flex: 1;
          display: flex;
          flex-direction: column;

          .activity-title {
            color: #000;
            font-size: 32rpx;
            margin-bottom: 16rpx;
          }

          .activity-detail {
            .detail-row {
              display: flex;
              align-items: center;
              margin-bottom: 8rpx;

              .detail-icon {
                width: 24rpx;
                height: 24rpx;
                margin-right: 16rpx;
              }

              .detail-text {
                color: #33333E;
                font-size: 24rpx;
              }
            }
          }
        }

        .status-bg {
          position: absolute;
          top: -50rpx;
          right: -36rpx;
          width: 192rpx;
          height: 192rpx;
        }
      }

      .divider {
        height: 1rpx;
        background: #F0F0F0;
        margin: 24rpx 0;
      }

      .activity-bottom {
        display: flex;
        justify-content: flex-end;

        .action-buttons {
          display: flex;
          gap: 16rpx;

          .action-btn {
            flex: 1;
            height: 72rpx;
            border-radius: 120rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;

            &.secondary {
              width: 148rpx;
              background: #F7F7F7;
              color: #33333E;
            }

            &.primary {
              width: 130rpx;
              background: #0198FF;
              color: #ffffff;
            }
          }
        }
      }
    }
  }

  // 骨架屏样式
  .skeleton-container {
    .skeleton-item {
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      padding: 32rpx;

      .skeleton-top {
        display: flex;
        gap: 24rpx;
        margin-bottom: 24rpx;

        .skeleton-image {
          width: 144rpx;
          height: 144rpx;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          border-radius: 12rpx;
          animation: skeleton-shimmer 1.5s ease-in-out infinite;
        }

        .skeleton-info {
          flex: 1;

          .skeleton-title {
            width: 228rpx;
            height: 44rpx;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 4rpx;
            margin-bottom: 16rpx;
            animation: skeleton-shimmer 1.5s ease-in-out infinite;
          }

          .skeleton-detail {
            .skeleton-row {
              display: flex;
              align-items: center;
              gap: 12rpx;
              margin-bottom: 12rpx;

              .skeleton-text {
                width: 60%;
                height: 34rpx;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                border-radius: 4rpx;
                animation: skeleton-shimmer 1.5s ease-in-out infinite;
              }
            }
          }
        }
      }

      .skeleton-divider {
        height: 2rpx;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        margin: 24rpx 0;
        animation: skeleton-shimmer 1.5s ease-in-out infinite;
      }

      .skeleton-bottom {
        .skeleton-buttons {
          display: flex;
          justify-content: flex-end;
          gap: 16rpx;

          .skeleton-btn {
            width: 148rpx;
            height: 72rpx;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 120rpx;
            animation: skeleton-shimmer 1.5s ease-in-out infinite;
          }
        }
      }
    }
  }

/* 骨架屏动画 */
@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}