/**
 * 编辑模式底部操作栏组件
 * 用于编辑模式下的全选和批量操作功能
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /** 是否显示操作栏 */
    visible: {
      type: Boolean,
      value: false
    },
    /** 是否全选 */
    isAllSelected: {
      type: Boolean,
      value: false
    },
    /** 操作按钮文本 */
    actionText: {
      type: String,
      value: '确定删除'
    },
    /** 未选中图标 */
    circleIcon: {
      type: String,
      value: ''
    },
    /** 选中图标 */
    circleActIcon: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换全选状态
     */
    onToggleSelectAll() {
      this.triggerEvent('toggleselectall');
    },

    /**
     * 批量操作
     */
    onBatchAction() {
      this.triggerEvent('batchaction');
    }
  }
});
