.main{
  background-color: #F7F7F7;
  height: 100vh;
  width: 100vw;
  .flex-center{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .flex-between{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .flex-between-start{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  .flex-start-center{
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .flex-start-start{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
  }
}
.top-box{
  width: 100%;
  padding: 24rpx 32rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #F0F0F0;
  border-bottom: 1rpx solid #F0F0F0;
  
  .left{
    width: 144rpx;
    height: 144rpx;
    background: #F3F3F3;
    border-radius:16rpx;
  }
  .right{
    margin-left: 24rpx;
    .one{
      width: 424rpx;
      height: 50rpx;
      background: #F3F3F3;
    }
    .two{
      margin-top: 16rpx;
      width: 82rpx;
      height: 34rpx;
      background: #F3F3F3;
    }
  }
}
.top-info-box{
  padding: 24rpx 32rpx;
  background: #FFFFFF;
  .item{
    margin-top: 48rpx;
  }
  .left{
    width: 114rpx;
    height: 40rpx;
    background: #F3F3F3;
  }
  .one{
    width: 192rpx;
    height: 40rpx;
    background: #F3F3F3;
  }
  .two{
    width: 524rpx;
    height: 88rpx;
    background: #F3F3F3;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-bottom: 8rpx;
  }
  .three{
    width: 412rpx;
    height: 60rpx;
    background: #F3F3F3;
    margin-bottom: 8rpx;
  }
  .four{
    width: 142rpx;
    height: 40rpx;
    background: #F3F3F3;
  }
  .five{
    width: 114rpx;
    height: 40rpx;
    background: #F3F3F3;
  }
}
.bottom-box{
  margin-top: 24rpx;
  padding: 32rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  background: #FFFFFF;

  .title{
    width: 154rpx;
    height: 40rpx;
    background: #F3F3F3;
  }

  .table {
    margin-top: 24rpx;
    border: 2rpx solid #F3F3F3;
    width: 686rpx;
    overflow: hidden;

    .table-row {
      display: flex;

      .table-cell {
        border-right: 2rpx solid #F3F3F3;
        border-bottom: 2rpx solid #F3F3F3;
        box-sizing: border-box;
        height: 84rpx;
        flex: 1;
      }

      /* 第一列固定宽度 */
      .table-cell:nth-child(1) {
        width: 296rpx;
        flex: none;
      }

      .table-cell:last-child {
        border-right: none;
      }
    }
    .head .table-cell{
      background: #F3F3F3;
    }
  
    /* 最后一行去掉底部边框 */
    .table-row:last-child .table-cell {
      border-bottom: none;
    }
  }
}