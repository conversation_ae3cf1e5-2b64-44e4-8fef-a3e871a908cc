// pages/order/list/components/searchPopup/index.ts
import { getOrderFilterConditions } from '../../../../../api/order';
import { getImageUrl } from '../../../../../utils/images';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer(newVal: boolean, oldVal: boolean) {
        console.log('搜索弹框 visible 属性变化:', { oldVal, newVal });
        if (newVal && !oldVal) {
          console.log('搜索弹框从隐藏变为显示');
        } else if (!newVal && oldVal) {
          console.log('搜索弹框从显示变为隐藏');
        }
      }
    },
    currentKeyword: {
      type: String,
      value: '',
      observer(newVal: string) {
        // 当父组件的搜索关键词变化时，同步到弹框
        this.setData({
          keyword: newVal
        });
      }
    },
    currentCategory: {
      type: String,
      value: '',
      observer(newVal: string) {
        // 当父组件的分类状态变化时，同步到弹框
        this.setData({
          selectedCategory: newVal
        });
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    keyword: '',
    searchHistory: [] as string[],
    categoryList: [] as any[], // 存储完整的选项对象，包含key和value
    filterGroups: [] as any[],
    selectedCategory: '', // 当前选中的分类key
    loading: false,
    searchIcon: getImageUrl('user/search.png')
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('搜索弹框组件已挂载');

      // 加载搜索历史
      this.loadSearchHistory();

      // 如果没有历史记录，添加一些示例数据用于演示
      if (this.data.searchHistory.length === 0) {
        this.setData({
          searchHistory: ['机票', '酒店']
        });
      }

      // 尝试加载分类数据
      console.log('组件挂载时尝试加载分类数据');
      this.loadCategoryData();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 加载分类数据
     */
    async loadCategoryData() {
      if (this.data.loading) {
        console.log('正在加载中，跳过重复请求');
        return;
      }

      console.log('开始执行loadCategoryData方法');

      try {
        this.setData({ loading: true });

        console.log('开始加载搜索分类数据...');
        const response = await getOrderFilterConditions();

        console.log('搜索分类API响应:', response);

        if (response && response.data && Array.isArray(response.data)) {
          // 获取最后一个分组的选项（旅行产品）
          const lastGroup = response.data[response.data.length - 1];
          if (lastGroup && lastGroup.options && Array.isArray(lastGroup.options)) {
            console.log('最后一个分组数据:', lastGroup);
            console.log('最后一个分组标题:', lastGroup.title);
            console.log('最后一个分组选项:', lastGroup.options);

            this.setData({
              filterGroups: response.data,
              categoryList: lastGroup.options // 直接使用完整的选项数据，包含key和value
            });

            console.log('搜索分类数据加载成功:', {
              groups: response.data.length,
              lastGroupTitle: lastGroup.title,
              categoryList: lastGroup.options
            });
          } else {
            console.error('最后一个分组数据格式不正确:', lastGroup);
          }
        } else {
          console.error('搜索分类数据格式不正确:', response);
        }
      } catch (error) {
        console.error('加载搜索分类数据异常:', error);
      } finally {
        this.setData({ loading: false });
      }
    },

    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
      try {
        const history = wx.getStorageSync('order_search_history') || [];
        this.setData({
          searchHistory: history.slice(0, 10) // 最多显示10条历史记录
        });
      } catch (error) {
        console.error('加载搜索历史失败:', error);
      }
    },

    /**
     * 保存搜索历史
     */
    saveSearchHistory(keyword: string) {
      try {
        let history = wx.getStorageSync('order_search_history') || [];
        
        // 移除重复项
        history = history.filter((item: string) => item !== keyword);
        
        // 添加到开头
        history.unshift(keyword);
        
        // 限制历史记录数量
        history = history.slice(0, 20);
        
        wx.setStorageSync('order_search_history', history);
        
        // 更新显示
        this.setData({
          searchHistory: history.slice(0, 10)
        });
      } catch (error) {
        console.error('保存搜索历史失败:', error);
      }
    },

    /**
     * 输入框变化事件
     */
    onInputChange(e: WechatMiniprogram.Input) {
      this.setData({
        keyword: e.detail.value
      });
    },

    /**
     * 清除输入框内容
     */
    onClearInput() {
      this.setData({
        keyword: ''
      });
    },

    /**
     * 搜索按钮点击事件
     */
    onSearchClick() {
      const keyword = this.data.keyword.trim();
      const selectedCategory = this.data.selectedCategory;

      // 如果既没有关键词也没有选中分类，则不执行搜索
      if (!keyword && !selectedCategory) {
        console.log('没有搜索关键词和选中分类，不执行搜索');
        return;
      }

      // 如果有关键词，保存搜索历史
      if (keyword) {
        this.saveSearchHistory(keyword);
      }

      // 触发搜索事件，传递关键词和状态
      this.triggerEvent('search', {
        keyword: keyword,
        status: selectedCategory // 传递选中的分类作为status
      });

      console.log('触发搜索事件:', { keyword, status: selectedCategory });

      // 关闭弹框
      this.onCloseClick();
    },

    /**
     * 历史记录点击事件
     */
    onHistoryClick(e: WechatMiniprogram.BaseEvent) {
      const { keyword } = e.currentTarget.dataset;
      this.setData({
        keyword
      });
      this.onSearchClick();
    },

    /**
     * 分类搜索点击事件
     */
    onCategoryClick(e: WechatMiniprogram.BaseEvent) {
      const { key } = e.currentTarget.dataset;
      console.log('分类搜索点击:', key);

      // 如果点击的是已选中的分类，则取消选中
      if (this.data.selectedCategory === key) {
        console.log('取消选中分类:', key);
        this.setData({
          selectedCategory: ''
        });
      } else {
        console.log('选中分类:', key);
        this.setData({
          selectedCategory: key
        });
      }
    },

    /**
     * 清空搜索历史
     */
    onClearHistory() {
      wx.showModal({
        title: '提示',
        content: '确定要清空搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              wx.removeStorageSync('order_search_history');
              this.setData({
                searchHistory: []
              });
              wx.showToast({
                title: '已清空',
                icon: 'success'
              });
            } catch (error) {
              console.error('清空搜索历史失败:', error);
            }
          }
        }
      });
    },

    /**
     * 遮罩层点击事件
     */
    onMaskClick() {
      this.onCloseClick();
    },

    /**
     * 关闭按钮点击事件
     */
    onCloseClick() {
      // 不重置状态，保持当前的搜索关键词和分类选择
      this.triggerEvent('close');
    },

    /**
     * 关闭弹框
     */
    onClose() {
      this.onCloseClick();
    },

    /**
     * popup visible-change 事件
     */
    onVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      const { visible } = e.detail;

      console.log('搜索弹框 visible-change 事件触发:', visible);

      if (visible) {
        console.log('搜索弹框显示，开始加载分类数据...');
        // 弹框显示时加载分类数据
        this.loadCategoryData();

        // 恢复父组件传递的状态
        this.setData({
          keyword: this.properties.currentKeyword || '',
          selectedCategory: this.properties.currentCategory || ''
        });
      }

      if (!visible) {
        console.log('搜索弹框隐藏，触发close事件');
        this.triggerEvent('close');
      }
    }
  }
});
