import { isPositiveInteger } from '../../../../../utils/index';
import { AgeGroupEnum } from '../../../../../enum/index';

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    priceList: {
      type: Array,
      value: [] as Activity.ICreateUpdateActivityPayload['perCost']
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    price: '',
    childPrice: '',
    elderlyPrice: ''
  },
  observers: {
    visible() {
      if (this.data.visible) {
        const list = this.data.priceList ?? [];
        const priceItem = list.find(item => item.type === AgeGroupEnum.ADULT);
        const childItem = list.find(item => item.type === AgeGroupEnum.CHILD);
        const elderlyItem = list.find(item => item.type === AgeGroupEnum.ELDERLY);
        this.setData({
          price: priceItem ? String(priceItem.perCost) : '',
          childPrice: childItem ? String(childItem.perCost) : '',
          elderlyPrice: elderlyItem ? String(elderlyItem.perCost) : ''
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.triggerEvent('update-visible', e.detail);
    },
    handleClose() {
      this.triggerEvent('update-visible', { visible: false })
    },
    handleSubmit() {
      if (!this.data.price) {
        wx.showToast({
          title: '成人价格不能为空',
          icon: 'none',
          duration: 3000
        })
        return;
      }

      if (!isPositiveInteger(this.data.price) || (this.data.childPrice && !isPositiveInteger(this.data.childPrice)) || (this.data.elderlyPrice && !isPositiveInteger(this.data.elderlyPrice))) {
        wx.showToast({
          title: '价格格式必须为正整数',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      const list = [
        {
          type: AgeGroupEnum.ADULT,
          perCost: +this.data.price
        }
      ]
      if (this.data.childPrice) {
        list.push({
          type: AgeGroupEnum.CHILD,
          perCost: +this.data.childPrice
        })
      }
      if (this.data.elderlyPrice) {
        list.push({
          type: AgeGroupEnum.ELDERLY,
          perCost: +this.data.elderlyPrice
        })
      }
      this.triggerEvent('change', {
        value: list
      });
      this.triggerEvent('update-visible', { visible: false });
    },
    handleValueChange(e: WechatMiniprogram.CustomEvent<{ value: string }, {}, { field: string; }>) {
      const field = e.currentTarget.dataset.field
      this.setData({
        [field]: e.detail.value
      })
    }
  }
})