<!--pages/tripHelper/my/index.wxml-->
<view class="trip-my-container">
  <!-- 固定在顶部的导航栏 - 默认隐藏，滑动时显示 -->
  <view class="fixed-navbar" style="opacity: {{navbarOpacity}}; visibility: {{navbarVisible ? 'visible' : 'hidden'}};">
    <navigationBar
      theme="white"
      title="{{navbarTitle}}"
      color="#000000"
      back="{{false}}"
      back-icon-color="#000000"
      title-size="32rpx"
      show-back="{{false}}"
    />
  </view>

  <!-- 头部背景区域 - 始终显示 -->
  <view class="header-section" style="background-image: url({{bgImage}})">
    <!-- 顶部按钮组 -->
    <view class="top-buttons">
      <view class="edit-btn" bindtap="onEditClick">{{isLoggedIn ? '编辑' : '立即登录'}}</view>
    </view>
    <!-- 用户信息区域 -->
    <view class="user-info">
      <view class="user-avatar-container" bindtap="onAvatarClick">
        <image class="user-avatar" src="{{displayAvatar}}" mode="aspectFill"></image>
        <!-- 性别标识 -->
        <image wx:if="{{showGenderIcon}}" class="gender-icon" src="{{genderIcon}}" mode="aspectFit" style="background-color: {{genderIconBgColor}};"></image>
      </view>
      <text class="user-nickname" >{{displayNickname}}</text>
    </view>
  </view>

  <!-- 功能菜单区域 - 始终显示 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="onMessageClick">
      <view class="menu-icon-container">
        <image class="menu-icon" src="{{currentMessageIcon}}" mode="aspectFit"></image>
        <!-- 消息红点（仅登录状态显示） -->
        <view wx:if="{{isLoggedIn && messageCount > 0}}" class="message-badge" style="background-image: url({{messageBadgeBg}}); width: {{messageCount >= 10 ? '64rpx' : '32rpx'}}; right: {{messageCount >= 10 ? '-48rpx' : '-24rpx'}};">
          <text class="badge-text">{{messageCount > 99 ? '99+' : messageCount}}</text>
        </view>
      </view>
      <text class="menu-title" style="color: {{menuTextColor}};">消息中心</text>
    </view>

    <view class="menu-item" bindtap="onOrderClick">
      <view class="menu-icon-container">
        <image class="menu-icon" src="{{currentOrderIcon}}" mode="aspectFit"></image>
      </view>
      <text class="menu-title" style="color: {{menuTextColor}};">报名订单</text>
    </view>

    <view class="menu-item" bindtap="onWalletClick">
      <view class="menu-icon-container">
        <image class="menu-icon" src="{{currentWalletIcon}}" mode="aspectFit"></image>
      </view>
      <text class="menu-title" style="color: {{menuTextColor}};">我的钱包</text>
    </view>
  </view>

  <!-- 活动订单列表区域 - 使用refreshLoadList组件（页面级滚动模式） -->
  <refreshLoadList
    enable-refresh="{{false}}"
    enable-load-more="{{true}}"
    use-page-scroll="{{true}}"
    disabled="{{false}}"
    refreshing="{{refreshing}}"
    loading-more="{{loadingMore}}"
    has-more="{{hasMore}}"
    is-empty="{{!isLoadingActivityOrders && (!isLoggedIn || (activityOrders && activityOrders.length === 0))}}"
    loading="{{isLoadingActivityOrders}}"
    hasCustomEmpty="{{false}}"
    hasCustomLoading="{{true}}"
    statusDisplayType="{{emptyStateType}}"
    statusDisplayTitle="我们一起拼车出发"
    statusDisplayStyle="margin-top: 100rpx;"
    bind:refresh="onRefresh"
    bind:loadmore="onLoadMore"
    bind:statusbuttonclick="onStatusDisplayButtonClick"
  >
    <!-- 自定义加载状态插槽 - 骨架屏 -->
    <view slot="loading">
      <view class="skeleton-container">
        <view wx:for="{{[{titleWidth: '80%'}, {titleWidth: '65%'}, {titleWidth: '75%'}]}}" wx:key="titleWidth" class="skeleton-item">
          <!-- 上半部分骨架 -->
          <view class="skeleton-top">
            <!-- 图片骨架 -->
            <view class="skeleton-image"></view>
            <!-- 信息骨架 -->
            <view class="skeleton-info">
              <view class="skeleton-title" style="width: {{item.titleWidth}};"></view>
              <view class="skeleton-detail">
                <view class="skeleton-row">
                  <view class="skeleton-text" style="width: 70%;"></view>
                </view>
                <view class="skeleton-row">
                  <view class="skeleton-text" style="width: 70%;"></view>
                </view>
              </view>
            </view>
          </view>
          <!-- 分割线 -->
          <view class="skeleton-divider"></view>
          <!-- 下半部分骨架 -->
          <view class="skeleton-bottom">
            <view class="skeleton-buttons">
              <view class="skeleton-btn"></view>
              <view class="skeleton-btn"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 活动订单列表内容 -->
    <!-- 活动订单列表 -->
    <view class="activity-list">
      <!-- 有数据时显示列表 -->
      <view wx:if="{{activityOrders && activityOrders.length > 0}}">
        <view wx:for="{{activityOrders}}" wx:key="id" class="activity-item">
          <!-- 上半部分：图片和基本信息 -->
          <view class="activity-top">
            <!-- 活动图片 -->
            <image class="activity-image" src="{{item.coverImages[0]}}" mode="aspectFill"></image>

            <!-- 活动信息 -->
            <view class="activity-info">
              <text class="activity-title">{{item.title}}</text>

              <view class="activity-detail">
                <view class="detail-row">
                  <image class="detail-icon" src="{{timeIcon}}" mode="aspectFit"></image>
                  <text class="detail-text">{{item.startTime}}</text>
                </view>
                <view class="detail-row">
                  <image class="detail-icon" src="{{addressIcon}}" mode="aspectFit"></image>
                  <text class="detail-text">{{item.routeInfo.journeyInfos[0].locations[0].location.name}}</text>
                </view>
              </view>
            </view>

            <!-- 状态背景图 -->
            <image class="status-bg" src="{{item.statusImage}}" mode="aspectFit"></image>
          </view>

          <!-- 分割线 -->
          <view class="divider"></view>

          <!-- 下半部分：操作按钮 -->
          <view class="activity-bottom">
            <view class="action-buttons">
              <!-- 查看名单按钮 -->
              <view
                wx:if="{{item.showDetailBtn}}"
                class="action-btn secondary"
                bindtap="onViewListClick"
                data-id="{{item.id}}"
              >
                <text>查看名单</text>
              </view>

              <!-- 打卡按钮 -->
              <view
                wx:if="{{item.showCheckinBtn}}"
                class="action-btn secondary"
                bindtap="onClockInClick"
                data-id="{{item.id}}"
              >
                <text>{{item.ifCheckIn ? '已打卡' : '打卡'}}</text>
              </view>

              <!-- 签到按钮 -->
              <view
                wx:if="{{item.showSignInBtn}}"
                class="action-btn secondary"
                bindtap="onSignInClick"
                data-id="{{item.id}}"
              >
                <text>{{item.ifSignIn ? '已签到' : '签到'}}</text>
              </view>

              <!-- 管理按钮 -->
              <view
                wx:if="{{item.showActivityManageBtn}}"
                class="action-btn primary"
                bindtap="onManageClick"
                data-id="{{item.id}}"
              >
                <text>管理</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </refreshLoadList>
</view>

<!-- 自定义底部tabbar -->
<customTabbar current="{{2}}" bind:change="onTabChange"></customTabbar>
