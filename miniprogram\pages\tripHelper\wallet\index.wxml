<view class="wallet-container">
  <!-- 固定在顶部的导航栏 -->
  <view class="fixed-navbar" style="background-color: {{navbarBgColor}};">
    <navigationBar
      theme="{{navbarTheme}}"
      title="我的钱包"
      color="#000000"
      back-icon-color="#000000"
      title-size="32rpx"
      bind:back="onNavigationBack"
    />
  </view>

  <!-- 页面内容区域 -->
  <view class="page-content">
    <!-- 头部背景区域 -->
    <view class="header-bg" style="background-image: url({{headBgIcon}})">
      <!-- 当前可提现金额 -->
      <view class="balance-section">
        <view class="balance-amount">
          <text class="currency-symbol">¥</text>
          <text class="amount-number">{{walletBalance.entryMoney || 0}}</text>
        </view>
        <view class="balance-label">当前可提现金额</view>
      </view>

      <!-- 历史总收入和本月收入 -->
      <view class="income-section">
        <view class="income-item">
          <view class="income-amount">¥{{walletBalance.waitCommission || 0}}</view>
          <view class="income-label">历史总收入</view>
        </view>
        <view class="income-item">
          <view class="income-amount">¥{{walletBalance.sumMonthMoney || 0}}</view>
          <view class="income-label">本月收入</view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="btn-record" bindtap="onWithdrawRecord">提现记录</view>
        <view
          class="btn-withdraw {{walletBalance.entryMoney > 0 ? 'active' : 'disabled'}}"
          bindtap="{{walletBalance.entryMoney > 0 ? 'onWithdraw' : 'onWithdrawDisabled'}}"
        >
          一键提现
        </view>
      </view>
    </view>

    <!-- 交易记录列表区域 - 使用refreshLoadList组件（页面级滚动模式） -->
    <refreshLoadList
      enable-refresh="{{false}}"
      enable-load-more="{{true}}"
      use-page-scroll="{{true}}"
      disabled="{{false}}"
      refreshing="{{refreshing}}"
      loading-more="{{loadingMore}}"
      has-more="{{hasMore}}"
      is-empty="{{!loading && transactionList.length === 0}}"
      loading="{{loading}}"
      hasCustomEmpty="{{false}}"
      hasCustomLoading="{{true}}"
      statusDisplayType="data-empty"
      statusDisplayTitle="我们一起拼车出发"
      statusDisplayStyle="margin-top: 100rpx;"
      bind:refresh="onRefresh"
      bind:loadmore="onLoadMore"
    >
      <!-- 自定义加载状态插槽 - 骨架屏 -->
      <view slot="loading">
        <view class="skeleton-container">
          <view wx:for="{{[1, 2, 3]}}" wx:key="*this" class="skeleton-item">
            <!-- 交易卡片头部骨架 -->
            <view class="skeleton-header">
              <view class="skeleton-left">
                <view class="skeleton-icon"></view>
                <view class="skeleton-info">
                  <view class="skeleton-title"></view>
                  <view class="skeleton-date"></view>
                </view>
              </view>
              <view class="skeleton-right">
                <view class="skeleton-label"></view>
                <view class="skeleton-amount-row">
                  <view class="skeleton-amount"></view>
                  <view class="skeleton-arrow"></view>
                </view>
              </view>
            </view>
            <!-- 交易详情骨架 -->
            <view class="skeleton-details">
              <view wx:for="{{[1, 2, 3]}}" wx:key="*this" class="skeleton-detail-item">
                <view class="skeleton-detail-label"></view>
                <view class="skeleton-detail-amount"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 交易记录列表 -->
      <view class="transaction-list">
        <view class="transaction-card" wx:for="{{transactionList}}" wx:key="activityId" bindtap="onTransactionDetailClick" data-id="{{item.activityId}}">
          <!-- 卡片头部 -->
          <view class="transaction-header">
            <view class="header-left">
              <image class="transaction-icon" src="{{getImageUrl('tripHelper/my/wallet.png')}}" />
              <view class="transaction-info">
                <view class="transaction-title">{{item.activityName}}</view>
                <view class="transaction-date">{{item.startTime}} ~ {{item.endTime}}</view>
              </view>
            </view>
            <view class="header-right" bindtap="onTransactionDetailClick" data-id="{{item.activityId}}">
              <view class="total-income-label">总收款</view>
              <view class="total-amount-row">
                <view class="total-amount">¥{{item.actualTotalSumAmount}}</view>
                <image class="arrow-icon" src="{{arrowIcon}}" mode="aspectFit" />
              </view>
            </view>
          </view>

          <!-- 交易详情列表 -->
          <view class="transaction-details">
            <view class="detail-item">
              <view class="detail-label">订单总金额</view>
              <view class="detail-right">
                <view class="detail-amount income">+ ¥{{item.totalSumAmount}}</view>
              </view>
            </view>
            <view class="detail-item" wx:if="{{item.insuranceAmount > 0}}">
              <view class="detail-label">保险费</view>
              <view class="detail-right">
                <view class="detail-amount expense">- ¥{{item.insuranceAmount}}</view>
              </view>
            </view>
            <view class="detail-item" wx:if="{{item.platformServiceAmount > 0}}">
              <view class="detail-label">平台服务费</view>
              <view class="detail-right">
                <view class="detail-amount expense">- ¥{{item.platformServiceAmount}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </refreshLoadList>
  </view>
</view>
