<!--报名列表页-->
<view class="main" wx:if="{{memberList && memberList.length > 0}}">
  <view class="content">
    <view class="memberlist-item" wx:for="{{memberList}}" wx:key="index">
      <text class="title" wx:if="{{item.assemblyPlaceDTO}}">
        <text>{{item.time}}</text>
        <text class="t1">{{item.assemblyPlaceDTO.placeName}}</text>
        <text class="t2">{{item.singIndesc}}</text>
      </text>
      <view class="member-item" wx:for="{{item.userDTOList}}" wx:for-item="user" wx:key="userId" >
        <view class="left">
          <view class="head-pic-box">
            <image class="head-pic" src="{{user.avatar}}" />
            <!-- 性别 0未知 1男 2女 -->
            <view wx:if="{{user.sex > 0}}" class="icon-box {{user.sex === 2 ? 'girl' : 'boy'}}">
              <image 
                class="icon" 
                src="{{'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/' + (user.sex === 2 ? 'girl' : 'boy') + '_icon.png'}}" 
                mode="aspectFit"
              />
            </view>
          </view>
          <view>
            <view class="name-box">
              <text>{{user.userName}}</text>
              <view wx:if="{{user.isSignIn}}">已签到</view>
            </view>
            <view class="des-box" wx:if="{{user.perNumDTO && user.perNumDTO.length > 0}}">
              <!-- 身份 0老人 1成人 2儿童 -->
              <text 
                wx:for="{{user.perNumDTO}}" 
                wx:for-item="per" 
                wx:key="index">{{AgeGroupDisplay[item.type]}}*{{per.count}}</text>
            </view>
          </view>
        </view>
        <image wx:if="{{user.mobile}}" data-mobile="{{user.mobile}}" bindtap="makePhoneCall" class="right" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/tel_icon.png" mode="" />
      </view>
    </view>
  </view>
</view>