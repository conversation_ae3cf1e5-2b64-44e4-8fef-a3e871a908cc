<view class="popup-mask" wx:if="{{visible}}" bindtap="hidePopup">
  <view class="popup-content" catchtap="stopPropagation">
    <image class="image-top" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_top.png" mode=""/>
    <view class="img-box" style="background-image: url('{{data.headImg}}')">
      <image class="image-middle" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_middle_new.png" mode=""/>
    </view>
    <view class="bottom-box">
      <view class="price-box">
        <view class="content">
          <text class="left">{{data.integerPrice}}</text>
          <view class="right">
            <text class="t1">{{data.decimalPrice}}</text>
            <text class="t2">元/人</text>
          </view>
        </view>
      </view>
      <view class="content-box">
        <view class="left">
          <text class="title">{{data.title}}</text>
          <view class="time-address-box">
            <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_time_icon.png" mode=""/>
            <text>{{data.startDate}}</text>
            <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_address_icon.png" mode=""/>
            <text>{{data.cityName}}</text>
          </view>
        </view>
        <view class="right">
          <image src="{{data.wxImage}}" mode=""/>
          <text>微信扫一扫</text>
        </view>
      </view>
    </view>
  </view>
  <view class="popup-bottom" catchtap="stopPropagation">
    <view class="content">
      <view class="btm-list">
      <button class="item" open-type="share">
        <view class="btm"><image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/wechat_icon.png" mode=""/></view>
        <text>微信</text>
      </button>
      <button class="item" bindtap="saveImage">
        <view class="btm"><image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/download_icon.png" mode=""/></view>
        <text>保存图片</text>
      </button>
      </view>
      <text class="cancel" bindtap="hidePopup">取消</text>
    </view>
  </view>
  <!-- 隐藏的Canvas（用于生成图片） -->
  <canvas 
    canvas-id="shareCanvas" 
    style="position:absolute;left:-9999px;width:{{canvasWidth}}px;height:{{canvasHeight}}px;transform:scale({{pixelRatio}});transform-origin:0 0;">
  </canvas>

  <!-- 预览生成的图片（调试用） -->
  <!-- <view wx:if="{{debugMode}}" style="margin-top:20px;border:1px solid #ccc;">
    <image wx:if="{{shareImagePath}}" src="{{shareImagePath}}" mode="widthFix" style="width:100%;"/>
    <text>生成的图片预览：</text>
  </view> -->
</view>