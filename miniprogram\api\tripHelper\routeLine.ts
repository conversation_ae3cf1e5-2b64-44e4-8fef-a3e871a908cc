import { request } from "../../utils/request";

// 获取线路列表
export const getRouteListApi = ( data : RouteLine.RouteListParams ) => request<RouteLine.RouteListResponse>({
  url: `/trip/activity/v1/route/select`,
  method: "POST",
  data
})

// 获取线路详情
export const  routeDetailApi = (routeId : string) => request<RouteLine.RouteDetailResponse>({
  url: `/trip/activity/v1/route/detail?routeId=${routeId}`,
  method: "GET",
})

// 获取区域选择
export const getRegionSelectApi = () => request<RouteLine.RegionSelectResponse>({
  url: `/trip/activity/v1/region/select`,
  method: "GET"
})

// 获取ip定位
export const getIpLocateApi = () => request<RouteLine.IpLocateResponse>({
  url: `/trip/activity/v1/ip/locate`,
  method: "GET"
})