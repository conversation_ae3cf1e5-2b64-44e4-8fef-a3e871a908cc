<!-- 我的页面 - 用户个人中心主页面 -->
<view class="user-my-page">
  <!-- 头部区域：包含背景图和用户信息展示 -->
  <view class="head" style="background-image: url({{headBgIcon}})">
    <!-- 已登录状态：显示用户头像和昵称 -->
    <view class="info" wx:if="{{isLogin}}">
      <image class="info-icon" src="{{displayAvatar}}"></image>
      <text class="info-text">{{displayNickname}}</text>
    </view>
    <!-- 未登录状态：显示登录提示和按钮 -->
    <view class="no-login" wx:else>
      <text class="no-login-text">登录翎游，无限旅游</text>
      <text class="no-login-button">登录/注册</text>
    </view>
  </view>
  
  <!-- 订单管理区域标题 -->
  <view class="title">
    <text class="text">我的订单</text>
  </view>
  
  <!-- 订单状态快捷入口：全部订单、待支付、未出行、退款/售后 -->
  <view class="order-tab">
    <view class="order-tab-item" wx:for="{{orderTabs}}" wx:key="id" data-id="{{item.id}}" bindtap="onOrderTabClick">
      <image class="order-tab-icon" src="{{item.icon}}"></image>
      <text class="order-tab-title">{{item.title}}</text>
    </view>
  </view>
  
  <!-- 用户功能菜单：收藏、反馈、设置等 -->
  <view class="user-menu">
    <!-- 菜单项：绑定点击事件，传递页面路径 -->
    <view class="menu-item" wx:for="{{userMenus}}" wx:key="path" data-path="{{item.path}}" bindtap="onMenuClick">
      <image class="menu-icon" src="{{item.icon}}"></image>
      <text class="menu-title">{{item.title}}</text>
      <image class="arrow-icon" src="{{arrowIcon}}"></image>
    </view>
  </view>
</view>