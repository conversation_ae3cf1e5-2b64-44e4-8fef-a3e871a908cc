import { getImageUrl } from "../../../utils/images";

// pages/user/seting/index.ts
Page({
  data: {
    setingMenus: [{
      title: '个人资料',
      path: '/pages/user/userInfo/index'
    }, {
      title: '关于我们',
      path: '/pages/user/aboutUs/index'
    }, {
      title: '账号注销',
      path: '/pages/user/accountCancellationStart/index'
    }],
    // 箭头图标，用于列表项右侧
    arrowIcon: getImageUrl('arrow.png'),
  },
  /**
   * 处理菜单项点击事件
   * @param e 事件对象
   */
  onMenuClick(e: WechatMiniprogram.BaseEvent) {
    const { path } = e.currentTarget.dataset;

    if (path) {
      // 现在不需要事件监听，直接跳转即可，因为使用store同步
      wx.navigateTo({
        url: path,
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },
})