page {
	background: @bg-gray-color;
}

.clock-ino {
	background-image: url('@{static-base-url}/tripHelper/clockIn/bg.png');
	background-size: 100% 400rpx;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;
	height: 100vh;
	.clock-content {
		flex: 1;
		height: 0;
	}
	.evaluate-wrap {
		.star-title {
			padding-top: 32rpx;
			text-align: center;
			color: @placeholder-text-color;
			display: flex;
			align-items: baseline;
			justify-content: center;
			&.is-active {
				color: @primary-color;
			}
			.star-num {
				font-size: 96rpx;
				font-weight: bold;
			}
			.star-text {
				font-size: 28rpx;
				margin-left: 8rpx;
			}
		}
		.evaluate-tips {
			color: @text-title-color;
			font-size: 24rpx;
			line-height: 34rpx;
			padding-left: 128rpx;
			margin-bottom: 16rpx;
		}
		.star-list {
			padding-left: 128rpx;
			display: flex;
			align-items: center;
			margin-bottom: 78rpx;
			.star-item {
				background: @placeholder-text-color;
				width: 80rpx;
				height: 80rpx;
				border-radius: 16rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 24rpx;
				&::before {
					content: "";
					width: 60rpx;
					height: 60rpx;
					background-image: url('@{static-base-url}/tripHelper/clockIn/star.png');
					background-size: 100%;
					background-repeat: no-repeat;
				}
				&.is-active {
					background: linear-gradient( 138deg, @primary-color 0%, #0DC0FE 100%);
				}
			}
		}
	}
}

.form-wrap {
	padding: 0 24rpx 50rpx;
	.evaluate-form {
		background-color: #fff;
		border-radius: 24rpx;
		padding: 32rpx;
		
		textarea {
			font-size: 28rpx;
			width: 100%;
			min-height: 236rpx;
			box-sizing: border-box;
		}
		.text-count {
			text-align: right;
			color: @text-light-color;
			font-size: 20rpx;
			line-height: 44rpx;
			height: 44rpx;
			margin-top: 4rpx;
		}
		.uploader-wrap {
			margin-top: 32rpx;
		}
	}
	.place-wrap {
		background: #fff;
		border-radius: 40rpx;
		margin-top: 24rpx;
		padding: 32rpx 24rpx;
		display: flex;
		align-items: center;
		font-size: 28rpx;
		.place-value {
			flex: 1;
			text-align: right;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
			min-width: 0;
			margin-left: 58rpx;
			font-weight: bold;
			&.is-placeholder {
				color: @placeholder-text-color;
			}
		}
		&::after {
			content: "";
			display: block;
			width: 24rpx;
			height: 24rpx;
			background-image: url('@{static-base-url}/arrow.png');
			background-size: 100%;
			margin-left: 24rpx;
		}
	}
}


.operate-wrap {
	padding: 16rpx 32rpx 68rpx;
	box-sizing: border-box;
	background: #fff;
	display: flex;
	flex-shrink: 0;
	.submit-trigger {
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		height: 108rpx;
		background: @primary-color;
		border-radius: 120rpx;
		width: 100%;
		font-size: 32rpx;
		font-weight: bold;
	}
}