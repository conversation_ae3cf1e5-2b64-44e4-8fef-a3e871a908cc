<t-popup placement="bottom" visible="{{visible}}" bind:visible-change="handleVisibleChange">
	<view class="popup-body">
		<view class="popup-title">
			<text></text>
			<text class="main-title">取消政策</text>
			<text class="close-trigger" bind:tap="handleClose"></text>
		</view>
		<view class="popup-content">
			<view class="tips-wrap">
				<view class="tips-title">平台将更具取消政策自动退款</view>
				<view class="tips-desc">发布后，退款政策不支持二次修改，请谨慎确认</view>
			</view>
			<scroll-view scroll-y="{{true}}" class="policy-list">
				<view>
					<view class="policy-item" wx:for="{{list}}" wx:key="id" catch:tap="handlePolicyClick" data-value="{{item.id}}">
						<Checkbox size="40" value="{{val}}" trueValue="{{item.id}}" />
						<view class="policy-content">
							<view class="policy-title">{{item.name}}</view>
							<rich-text class="policy-desc" nodes="{{item.desc}}"/>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
	<view class="operate-wrap">
		<button bind:tap="handleSubmit">确认</button>
	</view>
</t-popup>