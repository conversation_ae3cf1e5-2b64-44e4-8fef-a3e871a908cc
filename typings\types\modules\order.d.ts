/**
 * 订单相关类型定义
 */
declare namespace Order {
  /**
   * 订单状态枚举
   */
  enum OrderStatus {
    /** 全部 */
    ALL = '',
    /** 待付款 */
    PENDING_PAYMENT = '0',
    /** 未出行 */
    NOT_TRAVELED = '1',
    /** 带点评 */
    TO_REVIEW = '2',
    /** 已完成 */
    COMPLETED = '3',
    /** 已取消 */
    CANCELLED = '4',
    /** 退款中 */
    REFUNDING = '5',
    /** 已退款 */
    REFUNDED = '6'
  }

  /**
   * Tab类型
   */
  type TabType = 'all' | 'pending_payment' | 'not_traveled' | 'to_review';

  /**
   * 订单列表项接口（对应API返回数据）
   */
  interface IOrderItem {
    /** 订单ID */
    id: string;
    /** 酒店ID */
    hotelId: string;
    /** 房间类型ID */
    roomTypeId: string;
    /** 订单号 */
    orderNo: string;
    /** 订单状态显示文本 */
    showOrderStatus: string;
    /** 订单状态 */
    orderStatus: number;
    /** 订单金额 */
    orderAmount: string;
    /** 订单创建时间 */
    orderCreateTime: string;
    /** 订单支付时间 */
    orderPayTime: string | null;
    /** 酒店名称 */
    hotelName: string;
    /** 入住日期 */
    arriveDate: string;
    /** 离店日期 */
    departDate: string;
    /** 入住晚数 */
    nightCount: number;
    /** 房间类型名称 */
    roomTypeName: string;
    /** 房间数量 */
    roomCount: number;
    /** 床型描述 */
    bedDesc: string | null;
    /** 房间描述 */
    roomDesc: string;
    /** 酒店地址 */
    hotelAddress: string;
    /** 区域ID */
    areaId: string;
    /** 区域名称 */
    areaName: string;
  }

  /**
   * 处理后的订单项（用于页面显示）
   */
  interface OrderItem { 
    /** 订单ID */
    orderId: string;
    /** 订单号 */
    orderNo: string;
    /** 订单状态 */
    status: string;
    /** 订单状态文本 */
    statusText: string;
    /** 创建时间 */
    createTime: string;
    /** 支付时间 */
    payTime: string | null;
    /** 订单金额 */
    actualAmount: number;
    /** 酒店名称 */
    hotelName: string;
    /** 入住日期 */
    checkInDate: string;
    /** 离店日期 */
    checkOutDate: string;
    /** 房间类型 */
    roomType: string;
    /** 房间数量 */
    roomCount: number;
    /** 入住晚数 */
    nights: number;
  }

  /**
   * 订单列表
   */
  type OrderList = OrderItem[];

  /**
   * 订单列表查询参数接口
   */
  interface IOrderListReq {
    /** 页码 */
    pageIndex: number;
    /** 每页数量 */
    pageSize: number;
    /** 订单状态 */
    status: string;
    /** 搜索关键词 */
    keyword: string;
    /** 订单日期（年份） */
    orderDate: string;
  }

  /**
   * 订单列表响应接口
   */
  interface IOrderListRes {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 订单列表数据 */
    data: IOrderItem[];
    /** 是否成功 */
    success: boolean;
  }

  /**
   * 删除订单请求参数接口
   */
  interface IDeleteOrderReq {
    /** 订单号 */
    orderNo: string;
  }

  /**
   * 删除订单响应接口
   */
  interface IDeleteOrderRes {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 响应数据 */
    data: any;
    /** 是否成功 */
    success: boolean;
  }

  /**
   * Tab配置
   */
  interface TabConfig {
    /** Tab标识 */
    value: TabType;
    /** Tab标签 */
    label: string;
    /** 对应的订单状态 */
    status?: OrderStatus;
  }

  /**
   * 分页参数
   */
  interface PageParams {
    page: number;
    hasMore: boolean;
  }

  /**
   * 筛选条件选项
   */
  interface FilterOption {
    key: string;
    value: string;
  }

  /**
   * 筛选条件分组
   */
  interface FilterGroup {
    title: string;
    type: string;
    options: FilterOption[];
  }

  /**
   * 筛选条件API响应
   */
  interface FilterConditionsResponse {
    code: number;
    message: string;
    data: FilterGroup[];
    success: boolean;
  }

  /**
   * 筛选表单数据
   */
  interface FilterFormData {
    orderStatus: string;
    orderDate: string;
    type: string;
  }

  // ===== 活动订单相关类型定义 =====

  /**
   * 活动订单状态枚举
   */
  enum ActivityOrderStatus {
    /** 待支付 */
    PENDING = 0,
    /** 已支付 */
    HAD_PAID = 1,
    /** 候补中 */
    WAITING_CANDIDATE = 2,
    /** 已退款 */
    REFUND = 3,
    /** 已取消 */
    CANCELLED = 4
  }

  /**
   * 活动订单列表请求参数
   */
  interface ActivityOrderListRequest {
    /** 页码 */
    pageIndex: number;
    /** 每页大小 */
    pageSize: number;
  }

  /**
   * 后端返回的单个活动订单数据
   */
  interface ActivityOrderResponse {
    /** 活动ID */
    activityId: number;
    /** 活动名称 */
    activityName: string;
    /** 创建时间 */
    createTime: string;
    /** 订单状态 */
    orderStatus: number;
    /** 订单状态描述 */
    orderStatusDesc: string;
    /** 订单金额 */
    totalAmount: number;
    /** 购买老年数量 */
    oldPersonCount: number;
    /** 老年价格 */
    oldPersonPrice: number;
    /** 购买儿童数量 */
    childrenCount: number;
    /** 儿童价格 */
    childrenPrice: number;
    /** 购买成人数量 */
    adultCount: number;
    /** 成人价格 */
    adultPrice: number;
    /** 订单号 */
    orderNo: string;
    /** 图片地址 */
    mainImagUrl: string;
    /** 退款金额 */
    refundAmount: number;
    /** 退款比例 */
    refundRate: string;
  }

  /**
   * 活动订单列表响应
   * 注意：接口直接返回数组，不是包含data字段的对象
   */
  type ActivityOrderListResponse = ActivityOrderResponse[];

  /**
   * 页面显示的订单详情项
   */
  interface OrderDetailItem {
    /** 票种名称 */
    name: string;
    /** 价格 */
    price: number;
    /** 数量 */
    quantity: number;
  }

  /**
   * 页面显示的活动订单项
   */
  interface ActivityOrderItem {
    /** 活动ID */
    id: number;
    /** 活动名称 */
    title: string;
    /** 活动图片 */
    image: string;
    /** 报名时间 */
    registrationTime: string;
    /** 订单状态 */
    status: string;
    /** 状态文本 */
    statusText: string;
    /** 订单金额 */
    orderAmount: number;
    /** 订单详情 */
    orderDetails: OrderDetailItem[];
    /** 退款金额 */
    refundAmount: number | null;
    /** 退款比例 */
    refundRatio: string | null;
    /** 订单号 */
    orderNo: string;
  }
}
