/**
 * 作为应用的统一状态管理，业务状态管理请维护到modules目录下
 */
import { observable, action } from 'mobx-miniprogram';
import type { Store } from "./typings";

const toMobxStore = <T extends Record<string, any>>(store: T): T => {
  const ret = {} as T;

  Object.keys(store).forEach((key) => {
    const property = key as keyof T;

    if (typeof store[property] === "function") {
      ret[property] = action(store[property]);
    } else {
      ret[property] = store[property];
    }
  });

  return ret;
};

export const store = observable(
  toMobxStore({
    // 数据字段
    geoInfo: {
      lat: 39.909187,
      lng: 116.397455,
      lastUpdatedTime: 0
    },
    cityInfo: {
      cityId: '0101',
      cityName: '北京市'
    },
    userInfo: {
      openid: '',
      unionid: '',
      avatar: '',
      nickname: '',
      gender: '',
      birthday: '',
      email: '',
      mobile: '',
      userId: 0,
      isDetailLoaded: false
    },

    token: '',

    // actions
    setUserInfo: function (payload) {
      this.userInfo = {
        ...this.userInfo,
        ...payload
      }
    },
    setUserDetail: function (payload) {
      this.userInfo = {
        ...this.userInfo,
        ...payload,
        isDetailLoaded: true
      }
    },
    updateUserField: function (field, value) {
      this.userInfo = {
        ...this.userInfo,
        [field]: value
      }
    },
    setGeoInfo(payload) {
      this.geoInfo = {
        ...payload,
        lastUpdatedTime: Date.now()
      }
    },
    setToken(token) {
      this.token = token;
    }
  } as Store)
)