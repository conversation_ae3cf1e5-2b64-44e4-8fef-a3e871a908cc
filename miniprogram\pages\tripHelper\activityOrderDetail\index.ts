import { getOrderDetailApi,checkinfoApi } from '../../../api/tripHelper/activityOrderDetail';
import { wechatPayApi } from '../../../api/tripHelper/activityOrder'

interface PageOptions {
  orderNo?: string | number;
  activityId?:string | number;
}
// 1. PENDING(0, "待支付")
// 2. HAD_PAID(1, "已支付")
// 3. WAITING_CANDIDATE(2, "候补中")
// 4. REFUND(3, "已退款")
// 5. CANCELLED(4, "已取消")
const baseUrl = 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrderDetail/'
const baseInfo = [
  {
    btmTxt:'去支付',
    icon:baseUrl + 'loading_icon.png',
    iconClass:'icon rotating',
  },
  {
    btmTxt:'申请退款',
    icon:baseUrl + 'check_icon.png',
    iconClass:'icon',
  },
  {
    btmTxt:'申请退款',
    icon:baseUrl + 'loading_icon.png',
    iconClass:'icon rotating',
  },
  {
    btmTxt:'',
    icon:baseUrl + 'cancel_icon.png',
    iconClass:'icon',
  },
  {
    btmTxt:'',
    icon:baseUrl + 'cancel_icon.png',
    iconClass:'icon',
  }
]
// 配置映射表
const fieldMappings = [
  { field: 'orderNo', title: '订单编号' },
  { field: 'totalTicketsCount', title: '购票数量' },
  { field: 'totalAmount', title: '订单金额' },
  { field: 'paymentType', title: '支付方式' },
  { field: 'createTime', title: '创建时间' },
  { field: 'paymentTime', title: '付款时间' }
];
Page({
  /** 页面的初始数据 */
  data: {
    isLoading:true,
    isPullDownRefresh:false,
    pageOptions:{} as PageOptions,
    orderDetail:null as null | ActivityOrderDetail.IOrderDetailResponse,           
    showRefundVisible:false,
    refundInfo:{},
    comment:null as null | ActivityOrderDetail.ICheckInfoResponse, 
  },

  /** 生命周期函数--监听页面加载*/
  onLoad(options : PageOptions) {
    const { orderNo } = options

    // 处理orderNo不存在的情况
    if (!orderNo) {
      wx.showToast({ title: '缺少订单号', icon: 'none' });
      return;
    }

    this.setData({
      pageOptions:options
    },() => {
      this.initData()
      this.initComment()
    })
  },
  /** 页面相关事件处理函数--监听用户下拉动作 */
  onPullDownRefresh() {
    this.setData({
      isPullDownRefresh:true
    },() => {
      this.initData()
    })
  },
  async initData(event?:WechatMiniprogram.CustomEvent|string , needLoading?:boolean){
    try{
      const { pageOptions } = this.data
      let loading:boolean = false
      if(typeof needLoading === 'boolean'){
        loading = needLoading
      }else if(typeof event === 'object' && typeof (event as any).detail === 'object'){
        const { needLoading } = (event as WechatMiniprogram.CustomEvent).detail
        loading = needLoading
      }

      const { code , isSuccess, data ,message } = await getOrderDetailApi({orderNo:pageOptions.orderNo!},loading)

      if(code === 200 && isSuccess && data){
        let orderDetail: ActivityOrderDetail.IOrderDetailResponse = data
        const { orderStatus } = orderDetail

        // 处理按钮文案,图标，图标样式
        orderDetail['statuBar'] = baseInfo[orderStatus].btmTxt
        orderDetail['iconUrl'] = baseInfo[orderStatus].icon
        orderDetail['iconClass'] = baseInfo[orderStatus].iconClass

        // 使用reduce生成订单信息列表
        orderDetail['orderInfoList'] = fieldMappings.reduce((list, { field, title }) => {
          const value = orderDetail[field as keyof typeof orderDetail];
          if (value !== undefined && value !== null && value !== '') {
            list.push({ title, value });
          }
          return list;
        }, []as Array<{title: string, value: any}>);

        this.setData({
          orderDetail,
          isLoading:false
        })
      }else{
        wx.showToast({ title: message , icon: 'none' });
      }

    }catch(error){
      wx.showToast({ title: '页面加载失败，请稍后重试', icon: 'none' });
    }

    if(this.data.isPullDownRefresh){
      wx.stopPullDownRefresh();
    }
  },
  // 获取点评信息
  async initComment(){
    try {
      const { pageOptions } = this.data
      const { code , isSuccess, data } = await checkinfoApi({activityId:pageOptions.activityId!})
      
      if(code === 200 && isSuccess && data){
        this.setData({comment: data})
      }else{
        this.setData({comment: null});
      }
    } catch (error) {
      this.setData({comment: null});
    }
  },
  // 头部按钮点击
  statubarClick(){
    const { orderDetail } = this.data
    if (!orderDetail) {
      return;
    }

    const { orderStatus,orderNo } = orderDetail

    switch(orderStatus){
      case 0:
        this.goPay()
        break;
      case 1:
      case 2:
        const { totalAmount,refundAmount} = orderDetail;

        // 定义人群类型及其对应的字段
        const personCategories = [
          { type: '成人', countKey: 'adultCount', priceKey: 'adultPrice' },
          { type: '儿童', countKey: 'childrenCount', priceKey: 'childrenPrice' },
          { type: '老人', countKey: 'oldPersonCount', priceKey: 'oldPersonPrice' }
        ];
        // 动态生成 personList，只包含数量 > 0 的类别
        const personList = personCategories
          .filter(({ countKey }) => orderDetail[countKey] > 0)
          .map(({ type, countKey, priceKey }) => ({
            type,
            price: orderDetail[priceKey].toFixed(2),
            num: orderDetail[countKey]
          }));
        
        // 退款表格
        this.setData({
          refundInfo:{
            orderNo,
            personList,
            refundAmount:refundAmount?.toFixed(2),
            totalAmount:totalAmount?.toFixed(2)
          },
          showRefundVisible:true
        })
        break;
      default:
        break;
    }
    
  },
  // 去支付
  async goPay(){
    const { orderDetail } = this.data
    if(!orderDetail){
      return
    }

    const { orderNo,activityName,activityId,totalAmount } = orderDetail
    const param = {
      orderNo,
      activityId,
      orderPayMoney:totalAmount,
      goodsName:activityName
    }
    const { code , isSuccess, data ,message } = await wechatPayApi(param)
    if(code === 200 && isSuccess && data){
      const { timeStamp,nonceStr,prepayId,signType,paySign } = data
      
      wx.requestPayment({
        timeStamp: timeStamp,
          nonceStr: nonceStr,
          package: prepayId, 
          signType: signType,
          paySign: paySign,
        success : (res) => {
          console.log('支付成功:', res);
          this.initData('',true)
        },
        fail : (err) => {
          wx.showToast({ title: err?.errMsg, icon: 'none' });
        }
      });
    }else{
      wx.showToast({ title: message, icon: 'none' });
    }
  }
})