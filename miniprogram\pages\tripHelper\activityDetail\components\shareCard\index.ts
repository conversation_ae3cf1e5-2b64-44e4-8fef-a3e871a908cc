// components/share-popup/share-popup.ts
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    data :{
      type:Object,
      value:{} as ActivityDetail.ShareDataParams
    }
  },

  data: {
    canvasWidth: 300,
    canvasHeight: 500,
    pixelRatio: 1,
    shareImagePath: '',
    debugMode: true
  } as {
    canvasWidth: number;
    canvasHeight: number;
    pixelRatio: number;
    shareImagePath: string;
    debugMode: boolean;
  },

  lifetimes: {
    attached() {
      this.setData({
        pixelRatio: wx.getSystemInfoSync().pixelRatio
      });
    }
  },

  methods: {
    // 关闭弹窗
    hidePopup(){
      this.setData({ visible: false });
    },

    // 阻止冒泡
    stopPropagation(){},

    // 下载卡片
    async saveImage() {
      try {
        wx.showLoading({ title: '生成图片中', mask: true });

        // 1. 确保尺寸正确
        await this.calculateCanvasSize();

        // 2. 绘制内容
        await this.drawShareCanvas();

        // 3. 导出图片
        const tempFilePath = await new Promise<string>((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvasId: 'shareCanvas',
            success: (res) => resolve(res.tempFilePath),
            fail: reject
          }, this);
        });

        // 4. 保存到相册
        await this.saveToAlbum(tempFilePath);
        this.setData({ shareImagePath: tempFilePath });

        wx.showToast({ title: '保存成功' });
      } catch (err) {
        console.error('Save image failed:', err);
        this.handleSaveError(err as WechatMiniprogram.GeneralCallbackResult);
      } finally {
        wx.hideLoading();
      }
    },

    // 尺寸转换
    calculateCanvasSize(): Promise<void> {
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query.select('.popup-content').boundingClientRect((rect) => {
          if (!rect || rect.width <= 0 || rect.height <= 0) {
            console.error('Invalid canvas size', rect);
            return;
          }
          const { width,height } = rect // 物理像素
          const { pixelRatio } = this.data

          this.setData({
            canvasWidth: width * pixelRatio,
            canvasHeight: height * pixelRatio
          }, resolve);
        }).exec();
      });
    },
    // rpx 转px 
    RPXTOPX(rpx:number){
      const systemInfo = wx.getSystemInfoSync();
      const windowWidth = systemInfo.windowWidth;
      return (rpx / 750) * windowWidth
    },
    // 绘制
    async drawShareCanvas(): Promise<void> {
      return new Promise((resolve) => {
        const ctx = wx.createCanvasContext('shareCanvas', this);
        const dpr = this.data.pixelRatio;

        // 1. 绘制透明背景（如需白色背景则取消注释）
        // ctx.setFillStyle('#ffffff');
        // ctx.fillRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

        // 2. 绘制内容
        this.drawContent(ctx, dpr).finally(() => {
          ctx.draw(false, () => {
            console.log('Canvas render completed');
            resolve();
          });
        });
      });
    },
    // 绘制内容
    async drawContent(
      ctx: WechatMiniprogram.CanvasContext,
      dpr: number
    ): Promise<void> {
      try {

        const { data:{headImg,price,integerPrice,decimalPrice,title,startDate,wxImage,cityName} } = this.data

        // 1. 绘制顶部装饰条
        const topImg = await this.loadImage(
          'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_top.png'
        );
        if (topImg) {
          ctx.drawImage(
            topImg,
            0,
            0,
            this.data.canvasWidth,
            this.RPXTOPX(72) * dpr  // 原样式 height: 72rpx
          );
        }
    
        // 2. 绘制主图区域（带圆角背景）
        const mainBgImg = await this.loadImage(headImg);
        if (mainBgImg) {
          // 绘制圆角矩形背景（匹配样式中的 border-radius）
          this.drawRoundRect(
            ctx,
            0,
            this.RPXTOPX(72) * dpr,              // 紧接在顶部条下方
            this.data.canvasWidth,
            this.RPXTOPX(426) * dpr,             // 原样式 height: 426rpx
            this.RPXTOPX(40) * dpr,
            this.RPXTOPX(184) * dpr,
            0,
            0
          );
          ctx.save();
          ctx.clip();
          ctx.drawImage(
            mainBgImg,
            0,
            this.RPXTOPX(72) * dpr,
            this.data.canvasWidth,
            this.RPXTOPX(426) * dpr
          );
          ctx.restore();
        }

        // 3. 绘制一起出去玩卡片
        const titleImg = await this.loadImage(
          'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_middle_new.png'
        );
        if(titleImg){
          ctx.drawImage(
            titleImg,
            0,
            this.RPXTOPX(72) * dpr,
            this.data.canvasWidth,
            this.RPXTOPX(82) * dpr
          );
        }

        // 4.绘制我们一起拼车出发
        const priceBgImg = await this.loadImage(
          'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_mid.png'
        );
        if (priceBgImg) {
          ctx.drawImage(
            priceBgImg,
            0,
            this.RPXTOPX(442) * dpr ,
            this.data.canvasWidth,
            this.RPXTOPX(132) * dpr    // 原样式 height: 132rpx
          );
        }
        
        // 5.绘制价格
        ctx.save();

        // 计算价格长度（一位数字宽度大概40rpx)
        const integerDigits = this.countIntegerDigits(price);
        const priceWith = integerDigits * 40

        // 5.1 移动到旋转中心点（价格区域的中心位置）
        const centerX = this.data.canvasWidth - (this.RPXTOPX(priceWith+44+30) * dpr);
        const centerY = this.RPXTOPX(462 + 76) * dpr;
        ctx.translate(centerX, centerY);

        // 5.2 旋转坐标系
        ctx.rotate(-5 * Math.PI / 180);

        // 5.3 绘制价格文字（以旋转后的坐标系为基准）
        ctx.setFontSize(this.RPXTOPX(64) * dpr);
        ctx.setFillStyle('#33333E');
        ctx.setTextAlign('left'); // 改为左对齐

        // 5.4 主价格"180"（左侧）
        ctx.fillText(integerPrice, 0, 0);

        // 5.5 小数".00"（右上）
        ctx.setFontSize(this.RPXTOPX(24) * dpr);
        ctx.fillText(
          decimalPrice,
          this.RPXTOPX(priceWith) * dpr,
          -this.RPXTOPX(29) * dpr
        );

        // 5.6 单位"元/人"（右下）
        ctx.setFontSize(this.RPXTOPX(20) * dpr);
        ctx.fillText(
          '元/人',
          this.RPXTOPX(priceWith) * dpr,
          -this.RPXTOPX(5) * dpr
        );
        ctx.restore();

        
        // 7 绘制底部信息区域
        ctx.save();
        this.drawRoundRect(
          ctx,
          0,
          this.RPXTOPX(72 + 426 + 132 - 56 - 1) * dpr, // 精确定位
          this.data.canvasWidth,
          this.RPXTOPX(200) * dpr,  // 估算高度
          0,
          0,
          this.RPXTOPX(40) * dpr,
          this.RPXTOPX(40) * dpr  // 仅底部有圆角
        );
        ctx.setFillStyle('#FFFFFF');
        ctx.fill();
    
        // 7.1 活动标题（左侧）
        ctx.setFontSize(this.RPXTOPX(40) * dpr);  // 原样式 font-size: 40rpx
        ctx.setFillStyle('#11111E');
        const FIXtitle = title
        const wtapTitle = FIXtitle.length > 8 ? FIXtitle.slice(0,7) + '···' : 'FIXtitle'
        this.wrapText(
          ctx,
          wtapTitle,
          this.RPXTOPX(32) * dpr,  // margin-left: 32rpx
          this.RPXTOPX(72 + 426 + 132 - 56 + 14 + 40 + 36) * dpr, // 垂直起始位置
          this.RPXTOPX(360) * dpr,  // max-width: 360rpx
          this.RPXTOPX(50) * dpr     // line-height
        );
    
        // 7.2 时间/地点信息
        ctx.setFontSize(this.RPXTOPX(24) * dpr);
        ctx.setFillStyle('#99999E');
        // 时间图标
        const timeIcon = await this.loadImage(
          'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_time_icon.png'
        );
        if (timeIcon) {
          ctx.drawImage(
            timeIcon,
            this.RPXTOPX(32) * dpr,
            this.RPXTOPX(72 + 426 + 132 - 56 + 14 + 40 + 24 + 36 + 8) * dpr,
            this.RPXTOPX(24) * dpr,
            this.RPXTOPX(24) * dpr
          );
        }
        ctx.fillText(
          startDate,
          this.RPXTOPX(32 + 24 + 8) * dpr,
          this.RPXTOPX(72 + 426 + 132 - 56 + 14 + 40 + 44 + 36 + 8) * dpr
        );
          
        // 地点图标
        const addressIcon = await this.loadImage(
          'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_address_icon.png'
        );
        if (addressIcon) {
          ctx.drawImage(
            addressIcon,
            this.RPXTOPX(32 + 24 + 8 + 100 + 24 + 24) * dpr,
            this.RPXTOPX(72 + 426 + 132 - 56 + 14 + 40 + 24 + 36 + 8) * dpr,
            this.RPXTOPX(24) * dpr,
            this.RPXTOPX(24) * dpr
          );
        }
        ctx.fillText(
          cityName,
          this.RPXTOPX(32 + 24 + 8 + 100 + 24 + 24 + 8 + 24) * dpr,
          this.RPXTOPX(72 + 426 + 132 - 56 + 14 + 40 + 44 + 36 + 8) * dpr
        );
    
        // 7.3 右侧二维码
        const qrImg = await this.loadImage(wxImage);
        if (qrImg) {
          ctx.drawImage(
            qrImg,
            this.data.canvasWidth - this.RPXTOPX(32 + 136) * dpr,  // margin-right: 32rpx
            this.RPXTOPX(72 + 426 + 132 - 56 + 14) * dpr,
            this.RPXTOPX(136) * dpr,
            this.RPXTOPX(136) * dpr
          );
          
          // 二维码下方文字
          ctx.setFontSize(this.RPXTOPX(20) * dpr);
          ctx.setTextAlign('center');
          ctx.fillText(
            '微信扫一扫',
            this.data.canvasWidth - this.RPXTOPX(32 + 68) * dpr,
            this.RPXTOPX(72 + 426 + 132 - 56 + 14 + 136 + 20) * dpr
          );
        }
        ctx.restore();
    
      } catch (err) {
        console.error('Draw content failed:', err);
      }
    },
    // 绘制圆角矩形
    drawRoundRect(
      ctx: WechatMiniprogram.CanvasContext,
      x: number,
      y: number,
      width: number,
      height: number,
      tl: number = 0,
      tr: number = 0,
      br: number = 0,
      bl: number = 0
    ): void {
      ctx.beginPath();
      ctx.moveTo(x + tl, y);
      ctx.lineTo(x + width - tr, y);
      ctx.arcTo(x + width, y, x + width, y + tr, tr);
      ctx.lineTo(x + width, y + height - br);
      ctx.arcTo(x + width, y + height, x + width - br, y + height, br);
      ctx.lineTo(x + bl, y + height);
      ctx.arcTo(x, y + height, x, y + height - bl, bl);
      ctx.lineTo(x, y + tl);
      ctx.arcTo(x, y, x + tl, y, tl);
      ctx.closePath();
    },
    // 绘制文字
    wrapText(
      ctx: WechatMiniprogram.CanvasContext,
      text: string,
      x: number,
      y: number,
      maxWidth: number,
      lineHeight: number
    ): void {
      const words = text.split('');
      let line = '';

      for (let n = 0; n < words.length; n++) {
        const testLine = line + words[n];
        const metrics = ctx.measureText(testLine);
        if (metrics.width > maxWidth && n > 0) {
          ctx.fillText(line, x, y);
          line = words[n];
          y += lineHeight;
        } else {
          line = testLine;
        }
      }
      ctx.fillText(line, x, y);
    },
    // 获取图片
    loadImage(url: string): Promise<string | undefined> {
      return new Promise((resolve) => {
        wx.getImageInfo({
          src: url,
          success: (res) => resolve(res.path),
          fail: (err) => {
            console.error('Load image failed:', url, err);
            resolve(undefined);
          }
        });
      });
    },
    // 用于计算价格宽度
    countIntegerDigits(price: string | number): number {
      // 处理千分位逗号并转为字符串
      const str = String(price).replace(/,/g, '');
      
      // 正则匹配：可选负号 + 整数部分
      const match = str.match(/^-?(\d+)(?:\.\d+)?/);
      
      // 返回整数部分位数（若无匹配返回0）
      return match ? match[1].length : 0;
    },
    // 保存到相册
    async saveToAlbum(filePath: string): Promise<void> {
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath,
          success: () => {
            wx.hideLoading();
            wx.showToast({ title: '保存成功' });
          },
          fail: (err) => {
            wx.hideLoading();
            if (err.errMsg.includes('auth')) {
              this.showAuthModal();
            }else{
              wx.showToast({ title: '保存失败', icon: 'none' });
            }
            reject(err);
          }
        });
      });
    },
    // 访问相册权限
    showAuthModal(): void {
      wx.showModal({
        title: '提示',
        content: '需要您授权保存图片到相册',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting();
          }
        }
      });
    },
    // 权限问题相关提示
    handleSaveError(err: WechatMiniprogram.GeneralCallbackResult): void {
      let errMsg = '保存失败';
      if (err.errMsg.includes('auth')) {
        errMsg = '需要相册权限';
        this.showAuthModal();
      } else if (err.errMsg.includes('canvas')) {
        errMsg = '生成图片失败，请重试';
      }
      wx.showToast({
        title: errMsg,
        icon: 'none',
        duration: 2000
      });
    },
  },
});