#!/usr/bin/env node

import * as ci from 'miniprogram-ci';
import * as fs from 'fs';
import * as path from 'path';

interface BuildConfig {
  appid: string;
  privateKeyPath: string;
  projectPath: string;
  version: string;
  desc: string;
  robot?: number;
  enableEs7?: boolean;
  verbose?: boolean;
}

export class MiniProgramCI {
  private config: BuildConfig;

  constructor(config: BuildConfig) {
    this.config = config;
  }

  /**
   * 构建npm包
   */
  async buildNpm(): Promise<void> {
    console.log('开始构建npm包...');
    
    try {
      // 检查package.json是否存在
      const packageJsonPath = path.join(this.config.projectPath, 'package.json');
      if (!fs.existsSync(packageJsonPath)) {
        throw new Error('package.json不存在');
      }

      // 检查node_modules是否存在
      const nodeModulesPath = path.join(this.config.projectPath, 'node_modules');
      if (!fs.existsSync(nodeModulesPath)) {
        throw new Error('node_modules不存在，请先运行 npm install');
      }

      // 构建npm包
      await ci.packNpmManually({
        packageJsonPath: packageJsonPath,
        miniprogramNpmDistDir: path.join(this.config.projectPath, 'miniprogram'),
        ignores: []
      });

      console.log('npm包构建完成');
    } catch (error) {
      console.error('npm包构建失败:', error);
      throw error;
    }
  }

  /**
   * 上传代码
   */
  async uploadCode(): Promise<void> {
    console.log('开始上传代码...');
    
    try {
      // 检查私钥文件是否存在
      if (!fs.existsSync(this.config.privateKeyPath)) {
        throw new Error(`私钥文件不存在: ${this.config.privateKeyPath}`);
      }

      // 读取私钥
      const privateKey = fs.readFileSync(this.config.privateKeyPath, 'utf8');

      // 创建项目对象
      const project = new ci.Project({
        appid: this.config.appid,
        type: 'miniProgram',
        projectPath: this.config.projectPath,
        privateKey: privateKey,
        ignores: ['node_modules/**/*']
      });

      // 上传代码
      const uploadResult = await ci.upload({
        project: project,
        version: this.config.version,
        desc: this.config.desc,
        robot: this.config.robot || 1,
        setting: {
          es6: true,
          minify: true,
          autoPrefixWXSS: true
        }
      });

      console.log('代码上传成功:', uploadResult);
    } catch (error) {
      console.error('代码上传失败:', error);
      throw error;
    }
  }

  /**
   * 预览代码
   */
  async previewCode(): Promise<void> {
    console.log('开始预览代码...');
    
    try {
      // 检查私钥文件是否存在
      if (!fs.existsSync(this.config.privateKeyPath)) {
        throw new Error(`私钥文件不存在: ${this.config.privateKeyPath}`);
      }

      // 读取私钥
      const privateKey = fs.readFileSync(this.config.privateKeyPath, 'utf8');

      // 创建项目对象
      const project = new ci.Project({
        appid: this.config.appid,
        type: 'miniProgram',
        projectPath: this.config.projectPath,
        privateKey: privateKey,
        ignores: ['node_modules/**/*']
      });

      // 预览代码
      const previewResult = await ci.preview({
        project: project,
        version: this.config.version,
        setting: {
          es6: true,
          minify: true,
          autoPrefixWXSS: true
        }
      });

      console.log('代码预览成功:', previewResult);
    } catch (error) {
      console.error('代码预览失败:', error);
      throw error;
    }
  }

  /**
   * 完整的构建流程
   */
  async build(): Promise<void> {
    try {
      console.log('开始完整构建流程...');
      
      // 1. 构建npm包
      await this.buildNpm();
      
      // 2. 上传代码
      await this.uploadCode();
      
      console.log('构建流程完成');
    } catch (error) {
      console.error('构建流程失败:', error);
      throw error;
    }
  }
}

// 命令行参数解析
function parseArgs(): BuildConfig {
  const args = process.argv.slice(2);
  const config: BuildConfig = {
    projectPath: process.cwd(),
    appid: '',
    privateKeyPath: '',
    version: '1.0.0',
    desc: 'CI自动部署',
    robot: 1,
    enableEs7: true,
    verbose: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case '--project':
        if (nextArg) {
          config.projectPath = nextArg;
          i++;
        }
        break;
      case '--appid':
        if (nextArg) {
          config.appid = nextArg;
          i++;
        }
        break;
      case '--private-key':
        if (nextArg) {
          config.privateKeyPath = nextArg;
          i++;
        }
        break;
      case '--version':
        if (nextArg) {
          config.version = nextArg;
          i++;
        }
        break;
      case '--desc':
        if (nextArg) {
          config.desc = nextArg;
          i++;
        }
        break;
      case '--robot':
        if (nextArg) {
          config.robot = parseInt(nextArg);
          i++;
        }
        break;
      case '--no-es7':
        config.enableEs7 = false;
        break;
      case '--verbose':
        config.verbose = true;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
    }
  }

  return config;
}

// 显示帮助信息
function showHelp(): void {
  console.log(`
微信小程序 CI 工具

用法: ts-node build/ci.ts [选项]

选项:
  --project <path>          项目路径 (默认: 当前目录)
  --appid <appid>           小程序 appid
  --private-key <path>      上传密钥路径
  --version <version>       版本号 (默认: 1.0.0)
  --desc <description>      上传描述 (默认: CI自动部署)
  --robot <number>          机器人编号 (默认: 1)
  --no-es7                  禁用 ES7 语法
  --verbose                 详细输出
  --help, -h                显示帮助信息

示例:
  ts-node build/ci.ts --appid wxappid --private-key build/private.key
  ts-node build/ci.ts --version 1.2.3 --desc "修复bug"
  `);
}

// 主函数
async function main(): Promise<void> {
  try {
    const config = parseArgs();
    
    // 验证必要参数
    if (!config.appid) {
      throw new Error('请提供 appid 参数');
    }
    if (!config.privateKeyPath) {
      throw new Error('请提供 private-key 参数');
    }

    const ciRunner = new MiniProgramCI(config);
    await ciRunner.build();
    
    console.log('构建完成！');
  } catch (error) {
    console.error('构建失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (typeof require !== 'undefined' && (require as any).main === module) {
  main();
}

export default MiniProgramCI; 