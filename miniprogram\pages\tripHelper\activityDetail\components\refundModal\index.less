.bottom-modal-content{
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  .pop-title{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32rpx 32rpx 32rpx 76rpx;

    text{
      width: 598rpx;
      text-align: center;
      font-weight: 500;
      font-size: 36rpx;
      line-height: 36rpx;
      color: #11111E;
    }
    image{
      width: 36rpx;
      height: 36rpx;
      margin-left: 8rpx;
    }
  }

  .refund-table {
    margin: 24rpx 32rpx;
    border: 2rpx solid #F3F3F3;
    overflow: hidden;
    font-size: 28rpx;

    .table-row {
      display: flex;

      .table-cell {
        flex: 1;
        padding: 24rpx 40rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        border-right: 2rpx solid #F3F3F3;
        border-bottom: 2rpx solid #F3F3F3;
        box-sizing: border-box;
        min-height: 84rpx;
      }

      .table-cell:last-child {
        border-right: none;
      }

      .head .table-cell{
        font-weight: 500;
      }
    }
  
    /* 最后一行去掉底部边框 */
    .table-row:last-child .table-cell {
      border-bottom: none;
    }
  }
}