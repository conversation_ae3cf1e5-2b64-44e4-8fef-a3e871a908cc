<!--pages/tripHelper/routeDetails/components/NavBar/NavBar.wxml-->
<!-- 自定义导航栏 -->
<view 
  class="custom-nav-bar"
  style="height: {{navBarHeight}}px; background: {{backgroundColor}};"
>
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏内容区域 -->
  <view class="nav-content">
    <!-- 返回按钮 -->
    <view class="nav-back" bind:tap="onBackClick">
      <view 
        class="back-icon"
        style="border-color: {{iconColor}};"
      ></view>
    </view>
    
    <!-- 标题区域 -->
    <view 
      class="nav-title {{titleVisible ? 'visible' : 'hidden'}}"
      style="opacity: {{titleOpacity}}; max-width: {{maxTitleWidth}};"
    >
      <text class="title-text">{{routeName}}</text>
    </view>
    
    <!-- 右侧占位（保持布局平衡） -->
    <view class="nav-right"></view>
  </view>
</view>