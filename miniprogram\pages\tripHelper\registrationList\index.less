.main{
  background-color: #F3F3F3;
  overflow-y: scroll;
  overflow-x: hidden;
  height: 100vh;
  width: 100vw;
  .content{
    padding-bottom: constant(safe-area-inset-bottom); /* 兼容iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom); /* 兼容iOS >= 11.2 */
  }
}
.memberlist-item{
  margin: 24rpx 16rpx 0 16rpx;
  padding: 32rpx 24rpx;
  background: #FFFFFF;
  border-radius: 40rpx;

  .title{
    font-weight: 500;
    font-size: 32rpx;
    color: #11111E;
    vertical-align: middle;
    .t1{
      margin-left: 8rpx;
    }
    .t2{
      font-weight: 400;
      font-size: 24rpx;
      color: #99999E;
      margin-left: 8rpx;
    }
  }
  .member-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 54rpx;
    .left{
      display: flex;
      align-items: center;
      .head-pic-box{
        position: relative;
        width: 72rpx;
        height: 72rpx;
        .head-pic {
          width: 72rpx;
          height: 72rpx;
          border-radius: 50%;
          overflow: hidden;
        }
        .icon-box{
          width: 24rpx;
          height: 24rpx;
          border-radius: 48rpx 48rpx 48rpx 48rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          right: 0;
          bottom: 0;
        }
        .girl{
          background: #FF4EAA;
        }
        .boy{
          background: #0198FF;
        }
        .icon{
          width: 14.4rpx;
          height: 14.4rpx;
        }
      }
      .name-box{
        display: flex;
        align-items: center;
        margin-left: 16rpx;

        text{
          font-weight: 400;
          font-size: 28rpx;
          color: #33333E;
        }
        view{
          margin-left: 24rpx;
          width: 94rpx;
          height: 44rpx;
          background: #E6FEFF;
          border-radius: 16rpx 16rpx 16rpx 16rpx;
          line-height: 44rpx;
          font-size: 20rpx;
          color: #02C397;
          text-align: center;
        }
      }
      .des-box{
        display: flex;
        align-items: center;
        margin-top: 10rpx;
        text{
          margin-left: 16rpx;
          font-size: 20rpx;
          color: #99999E;
        }
      }
    }
    .right{
      width: 40rpx;
      height: 40rpx;
    }
  }
  
}
