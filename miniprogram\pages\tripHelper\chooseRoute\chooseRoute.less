/* pages/tripHelper/chooseRoute/chooseRoute.wxss */
.chooseRoutePage {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f5f5f5;
}

.search-box{
    background: #fff;
    padding: 0 32rpx;
    width: 100%;
    height: 108rpx;
    box-sizing: border-box;
    .search-box-content{
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #F9F9F9;
        border-radius: 54rpx;
        padding: 0 32rpx;
        box-sizing: border-box;
        .search-icon{
            width: 40rpx;
            height: 40rpx;
        }
        .search-input{
            flex: 1;
            height: 100%;
            margin: 0 24rpx;
            font-size: 32rpx;
            color: #333;
            background: transparent;
            border: none;
            outline: none;
        }
    }
}

.route-list {
  flex: 1;
  height: 0;
}

.route-list-content {
  padding-bottom: 200rpx; // 为确定按钮留出空间
  
  .load-more-status {
    text-align: center;
    padding: 32rpx 0;
    
    .loading-more, .no-more {
      text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 100rpx 0;
    
    text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.confirm-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  padding: 30rpx 32rpx 50rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  
  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background-color: #568DED;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    
    &:active {
      background-color: #4a7bd9;
    }
  }
}

/* 页面级弹窗样式 */
.page-popup-container {
  position: fixed;
  top: 198rpx; /* search-box(108) + filter(90) 的高度 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1001;
}

.popup-content-wrapper {
  position: relative;
  z-index: 1002;
  background-color: #fff;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  overflow: hidden; /* 防止内容溢出圆角 */
}

.popup-content {
  background-color: #fff;
  height: 100%;
  /* 不再需要 max-height、overflow-y、height 等滚动相关样式 */
}
