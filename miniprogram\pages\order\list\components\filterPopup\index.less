/* pages/order/list/components/filterPopup/index.less */
.filter-content {
  height: 100vh;
  display: flex;
  margin-top: 160rpx;
  flex-direction: column;
  background-color: #fff;

  .filter-body {
    flex: 1;
    padding: 0 32rpx;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;

      .loading-text {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .filter-section {
    margin-bottom: 40rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #33333E;
      margin-bottom: 16rpx;
    }

    .option-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .option-item {
        border-radius: 24rpx;
        font-size: 28rpx;
        text-align: center;
        width: 178rpx;
        height: 66rpx;
        line-height: 66rpx;
        color: #11111E;
        background-color: #F3F3F3;
        transition: all 0.2s ease;

        &.selected {
          background-color: #568DED;
          color: #fff;
          border-color: #568DED;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .filter-footer {
    position: fixed;
    bottom: 68rpx;
    left: 18%;
    right: 0;
    display: flex;
    gap: 24rpx;
    padding: 32rpx;
    background-color: #fff;
    z-index: 1000;
    .reset-btn{
      color: #11111E;
      background-color: #F3F3F3;
    }
    .confirm-btn{
      background-color: #568DED;
    }
    .reset-btn,
    .confirm-btn {
      font-weight: normal;
      font-size: 32rpx;
      flex: 1;
      height: 96rpx;
      border-radius: 120rpx;
    }
  }
}