<!--components/customTabbar/index.wxml-->
<view class="custom-tabbar">
  <!-- 正常的三个tab布局 -->
  <view
    wx:for="{{tabList}}"
    wx:key="text"
    class="tab-item {{current === index ? 'active' : ''}}"
    data-index="{{index}}"
    data-path="{{item.path}}"
    bind:tap="switchTab"
  >
    <!-- 所有位置都放图标，中间位置放占位图标 -->
    <image
      wx:if="{{index !== 1}}"
      class="tab-icon"
      src="{{current === index && item.iconActiveUrl ? item.iconActiveUrl : item.iconUrl}}"
      mode="aspectFit"
    ></image>
    <view wx:else class="tab-icon placeholder-icon"></view>
    <view class="tab-text">{{item.text}}</view>
  </view>

  <!-- 脱离文档流的中间圆形按钮，覆盖在中间tab上方 -->
  <view class="floating-center-btn" data-index="1" data-path="{{tabList[1].path}}" bind:tap="switchTab">
    <image class="center-icon" src="{{tabList[1].iconUrl}}" mode="aspectFit"></image>
  </view>
</view>
