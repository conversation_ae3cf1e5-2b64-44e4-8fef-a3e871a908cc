var tool = require('../../../../../utils/tool.wxs');

/**
 * 
 * @param startDate 开始日期
 * @param endDate 结束日期
 */
function activeDateFormat(start, end) {
  if (!start || !end) return '';
  // 只取日期部分
  var startDateStr = start.split(' ')[0];
  var endDateStr = end.split(' ')[0];
  var startParts = startDateStr.split('-');
  var endParts = endDateStr.split('-');
  var startYear = parseInt(startParts[0]);
  var startMonth = parseInt(startParts[1]);
  var startDay = parseInt(startParts[2]);
  var endYear = parseInt(endParts[0]);
  var endMonth = parseInt(endParts[1]);
  var endDay = parseInt(endParts[2]);
  var startDateObj = getDate(startYear, startMonth - 1, startDay);
  var endDateObj = getDate(endYear, endMonth - 1, endDay);
  // 计算天数差
  var diffDays = Math.round((endDateObj.getTime() - startDateObj.getTime()) / (24 * 60 * 60 * 1000)) + 1;
  // 用tool.formatDateTime格式化
  var startStr = tool.formatDateTime(start, 'MM.DD 周dd');
  var endStr = tool.formatDateTime(end, 'MM.DD 周dd');
  if (startDateStr === endDateStr) {
    return startStr;
  } else {
    return startStr + '-' + endStr + ' ' + diffDays + '天';
  }
}

module.exports = {
  activeDateFormat: activeDateFormat
};
  