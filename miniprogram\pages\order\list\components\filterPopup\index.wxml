<!--pages/order/list/components/filterPopup/index.wxml-->
<t-popup
  visible="{{visible}}"
  placement="right"
  bind:visible-change="onVisibleChange"
  custom-style="width: 82vw;"
>
  <view class="filter-content">

    <!-- 筛选内容 -->
    <scroll-view class="filter-body" scroll-y="{{true}}">
      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{loading}}">
        <t-loading theme="circular" size="48rpx" color="#0052d9" />
        <text class="loading-text">加载筛选条件中...</text>
      </view>

      <!-- 动态筛选条件列表 -->
      <view class="filter-section" wx:for="{{filterGroups}}" wx:key="type" wx:if="{{!loading}}">
        <view class="section-title">{{item.title}}</view>
        <view class="option-list">
          <view
            class="option-item {{selectedFilters[item.type] === option.key ? 'selected' : ''}}"
            wx:for="{{item.options}}"
            wx:for-item="option"
            wx:key="key"
            data-type="{{item.type}}"
            data-key="{{option.key}}"
            bindtap="onOptionSelect"
          >
            {{option.value}}
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="filter-footer">
      <t-button
        theme="light"
        size="large"
        bind:tap="onReset"
        class="reset-btn"
      >
        重置
      </t-button>
      <t-button
        theme="primary"
        size="large"
        bind:tap="onConfirm"
        class="confirm-btn"
      >
        完成
      </t-button>
    </view>
  </view>
</t-popup>
