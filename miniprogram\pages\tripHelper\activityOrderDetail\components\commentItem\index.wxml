<view class="comment-content" wx:if="{{commemtData}}">
  <view class="img-list">
    <image wx:for="{{processedImgList}}" src="{{item}}" wx:key="index" data-url="{{item}}" bindtap="viewImage"/>
  </view>
  <view class="star-box">
    <image wx:for="{{starImages}}" src="{{item}}" wx:key="index" mode=""/>
    <text>{{commemtData.recommendStar}}星</text>
  </view>
  <text class="desc-txt" wx:if="{{commemtData.comment}}">{{commemtData.comment}}</text>
  <view class="address-box" wx:if="{{commemtData.position && commemtData.position.name}}">
    <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrderDetail/location_dis_icon.png" mode=""/>
    <text>{{commemtData.position.name}}</text>
  </view>
</view>