.content{
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  .pop-title{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32rpx 32rpx 32rpx 76rpx;

    text{
      width: 598rpx;
      text-align: center;
      font-weight: 500;
      font-size: 36rpx;
      line-height: 36rpx;
      color: #11111E;
    }
    image{
      width: 36rpx;
      height: 36rpx;
      margin-left: 8rpx;
    }
  }
  .refund-info{
    padding: 24rpx 32rpx;

    .between-item{
      display: flex;
      justify-content: space-between;
      align-self: flex-start;
      margin-bottom: 48rpx;
  
      .title{
        font-weight: 500;
        font-size: 28rpx;
        color: #66666E;
      }
      .txt{
        font-size: 28rpx;
        color: #33333E;
      }
      .blod{
        font-weight: 500;
      }
      .price-list{
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .price-item{
          margin-bottom: 20rpx;
  
          .txt:nth-child(n + 2) {
            margin-left: 16rpx;
          }
        }
      }
      .price-list .price-item:last-child{
        margin-bottom: 0;
      }
    }

    .activity-refund{
      .title-box{
        display: flex;
        align-items: center;
        image{
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
        .txt{
          font-weight: 500;
          font-size: 28rpx;
          color: #33333E;
        }
        .desc{
          font-size: 20rpx;
          color: #99999E;
          margin-left: 8rpx;
          flex: 1;
        }
      }
      .refund-table {
        margin-top: 24rpx;
        border: 2rpx solid #F1F6FF;
        overflow: hidden;
        font-size: 24rpx;
    
        .table-row {
          display: flex;
    
          .table-cell {
            flex: 1;
            padding: 8rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            border-right: 2rpx solid #F1F6FF;
            border-bottom: 2rpx solid #F1F6FF;
            box-sizing: border-box;
            min-height: 84rpx;
          }
    
          /* 第一列固定宽度 */
          .table-cell:nth-child(1) {
            width: 296rpx;
            flex: none; /* 禁止伸缩 */
          }
    
          /* 后面列均分剩余空间 */
          .table-cell:nth-child(n+2) {
            flex: 1; /* 均分剩余空间 */
          }
    
          .table-cell:last-child {
            border-right: none;
          }
        }
        .head .table-cell{
          font-weight: 500;
          background: #F1F6FF;
        }
      
        /* 最后一行去掉底部边框 */
        .table-row:last-child .table-cell {
          border-bottom: none;
        }
      }
      .bottom-desc{
        margin-top: 16rpx;
        font-size: 20rpx;
        color: #99999E;
      }
    }
  }
  .bottom-bar-box{
    border-top: 2rpx solid #F7F7F7;
    padding: 16rpx 32rpx;

    view{
      width: 100%;
      height: 108rpx;
      background: #0198FF;
      border-radius: 120rpx 120rpx 120rpx 120rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      line-height: 108rpx;
      text-align: center;
    }
  }

}