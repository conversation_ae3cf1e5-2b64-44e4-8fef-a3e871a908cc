import { getAvatar, updateUserInfo } from "../../../api/my";
import { ComponentWithComputedStore } from '../../../core/componentWithStoreComputed';
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings';
import { behavior as computedBehavior } from 'miniprogram-computed';
import { store } from '../../../store/index';

// pages/user/chooseAvatar/index.ts
ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior, computedBehavior],

  storeBindings: {
    store,
    fields: ['userInfo'],
    actions: ['updateUserField']
  } as const,
  /**
   * 页面的初始数据
   */
  data: {
    /** 头像列表，包含头像URL和选中状态 */
    avatarList: [] as Array<{ url: string, selected: boolean }>,
    /** 当前选中的头像地址 */
    selectedAvatar: '',
    /** 原始头像地址，用于比较是否需要更新 */
    originalAvatar: ''
  },

  lifetimes: {
    attached() {
      // 从页面参数获取头像信息
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      this.onLoad(options as Record<string, string>);
    }
  },

  methods: {
    /**
     * 初始化页面数据
     * 获取传入的头像参数并初始化页面数据
     * @param options 页面参数，包含当前头像地址
     */
    onLoad(options: Record<string, string>) {
      // 获取传入的头像地址参数
      const currentAvatar = options.avatar ? decodeURIComponent(options.avatar) : '';
      // 保存原始头像地址
      this.setData({
        originalAvatar: currentAvatar
      });
      // 初始化页面数据
      this.init(currentAvatar);
    },

    /**
     * 初始化页面数据
     * 获取头像列表并设置当前选中状态
     * @param currentAvatar 当前头像地址，用于设置初始选中状态
     */
    async init(currentAvatar: string) {
      // 调用获取头像列表接口
      const { isSuccess, data } = await getAvatar();
      // 接口调用失败，直接返回
      if (!isSuccess) return;

      // 将字符串数组转换为对象数组，添加选中状态
      const avatarList = data.map((url: string) => ({
        url,
        selected: url === currentAvatar
      }));

      this.setData({
        avatarList,
        selectedAvatar: currentAvatar
      });
    },

    /**
     * 处理头像点击事件
     * 更新头像选中状态，重置其他头像的选中状态
     * @param e 事件对象，包含点击的头像索引
     */
    onAvatarClick(e: WechatMiniprogram.BaseEvent) {
      const { index } = e.currentTarget.dataset;
      const avatarList = this.data.avatarList;

      // 重置所有头像的选中状态
      avatarList.forEach((item, i) => {
        item.selected = i === index;
      });

      // 更新选中的头像
      const selectedAvatar = avatarList[index].url;

      this.setData({
        avatarList,
        selectedAvatar
      });
    },

    /**
     * 处理确认按钮点击事件
     * 验证头像选择并调用更新接口，如果头像相同则直接返回
     */
    async onSubmit() {
      // 检查是否选择了头像
      if (!this.data.selectedAvatar) {
        wx.displayToast({
          title: '请先选择头像',
          icon: 'none'
        });
        return;
      }

      // 检查选择的头像是否与原始头像一致
      if (this.data.selectedAvatar === this.data.originalAvatar) {
        // 头像地址一致，直接返回上一页
        wx.navigateBack();
        return;
      }

      try {
        // 调用更新用户信息接口
        const { isSuccess } = await updateUserInfo({
          avatar: this.data.selectedAvatar
        } as My.IUserInfoRes);

        // 隐藏加载提示
        wx.hidePrevLoading();
        if (isSuccess) {
          // 更新store中的头像
          this.updateUserField('avatar', this.data.selectedAvatar);

          // 不再需要事件传递，直接返回
          wx.navigateBack();
          return;
        }
        // 更新失败
        wx.displayToast({
          title: '头像更新失败',
          icon: 'none'
        });
      } catch (error) {
        // 隐藏加载提示
        wx.hidePrevLoading();
        console.error('更新头像失败:', error);
        wx.displayToast({
          title: '头像更新失败',
          icon: 'none'
        });
      }
    }
  }
})