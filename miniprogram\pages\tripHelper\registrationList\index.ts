import { getSignUpPageDetailApi } from '../../../api/tripHelper/registrationList'; 
import { formatDate } from '../../../utils/tool'

Page({
  /** 页面的初始数据 */
  data: {
    memberList: [] as Registration.ActivitySignUpCountDTO[]
  },
  /** 生命周期函数--监听页面加载 */
  async onLoad(options) {
    const { id } = options;

    if (!id) {
      wx.showToast({ title: '缺少活动id', icon: 'none' });
      return;
    }

    try {

      const { code,isSuccess,data ,message } = await getSignUpPageDetailApi({ id });
      if(code === 200 && isSuccess && data){
        const { activitySignUpCountDTOS } = data
        const processedData = activitySignUpCountDTOS.map((item) => {
          let signInNum:number = 0 , allNum:number = 0
          if(item.userDTOList?.length > 0){
            signInNum = item.userDTOList?.filter(user => {
              const isSigned = typeof user.isSignIn === 'string' 
                ? user.isSignIn === 'true' 
                : user.isSignIn;
              return isSigned;
            }).length;
            allNum = item.userDTOList.length
          }

          return {
            ...item,
            time:formatDate(item?.assemblyPlaceDTO?.startTime,'HH.mm'),
            singIndesc:'（' + signInNum + '/' + allNum + '）'
          };
        });
        this.setData({ memberList: processedData });
      }else{
        wx.showToast({ title: message, icon: 'none' });
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      wx.showToast({ title: '获取数据失败', icon: 'none' });
    }
  },
  // 拨打电话
  makePhoneCall(event:WechatMiniprogram.BaseEvent){
    const { mobile } = event.currentTarget.dataset;

    wx.makePhoneCall({
      phoneNumber: mobile,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  }
});