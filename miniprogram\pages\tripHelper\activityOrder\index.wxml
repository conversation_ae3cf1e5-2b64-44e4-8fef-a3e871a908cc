<block wx:if="{{!isLoading}}">
  <scroll-view scroll-y class="main">
    <!-- 活动价格信息 -->
    <view class="activity-info">
      <view class="title-box">
        <image class="pic" src="{{orderData.headImg}}" mode="" />
        <view class="content">
          <text>{{orderData.title}}</text>
          <view class="address-box">
            <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/location_icon.png" mode="" />
            <text>{{orderData.cityName}}</text>
          </view>
        </view>
      </view>
      <view class="activity-content">
        <!-- 时间 -->
        <view class="between-item">
          <text class="title">活动时间</text>
          <text class="txt blod">{{orderData.activitytime}}</text>
        </view>
        <!-- 地点 -->
        <view class="between-item">
          <text class="title">集合地点</text>
          <view class="address-list">
            <view 
              class="address-item flex-between"
              wx:for="{{orderData.assemblyPlaceList}}" 
              wx:key="index">
              <view class="flex-center">
                <image 
                  class="check-icon" 
                  src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/{{item.isChecked ? 'checked' : 'check'}}.png" 
                  mode="" 
                  data-index="{{index}}" 
                  bindtap="chooseAssemblyPlace"/>
                <text class="txt">{{item.startTime}}</text>
                <text class="txt blod">{{item.placeName}}</text>
              </view>
              <image 
                class="map-icon" 
                src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/map_icon.png" 
                mode=""
                data-lon="{{item.longitude}}" 
                data-lat="{{item.latitude}}"
                data-address="{{item.placeName}}" 
                data-cityName="{{item.cityName}}" 
                bindtap="openMap"/>
            </view>
          </view>
        </view>
        <!-- 购票 -->
        <view class="between-item">
          <text class="price-txt">
            <text class="title">购票数量</text>
            <text class="kc-txt" wx:if="{{orderData.stock < 5}}">仅剩{{orderData.stock}}张</text>
          </text>
          <view class="price-list">
            <view 
              class="price-item flex-center"
              wx:for="{{orderData.perList}}" 
              wx:key="index">
              <text class="txt blod">{{item.typename}}</text>
              <text class="txt blod">￥{{item.price}}</text>
              <view class="num-box flex-center">
                <image 
                  src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/sub{{item.num === 0 ? '_disable' : ''}}_icon.png" 
                  mode="" 
                  data-type="sub"
                  data-index="{{index}}"
                  bindtap="changeNum"/>
                <text>{{item.num}}</text>
                <image 
                  src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/add_icon.png" 
                  mode="" 
                  data-type="add"
                  data-index="{{index}}"
                  bindtap="changeNum"/>
              </view>
            </view>
          </view>
        </view>
        <!-- 金额 -->
        <view class="between-item">
          <text class="title">支付金额</text>
          <text class="txt blod">￥{{orderData.totalPrice}}</text>
        </view>
        <!-- 支付方式 -->
        <view class="between-item">
          <text class="title">支付方式</text>
          <view class="flex-center">
            <image class="wechat-icon" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/wechat_icon.png" mode="" />
            <text class="txt blod">微信支付</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 退款政策 -->
    <view class="activity-refund">
      <view class="title-box">
        <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/refund_icon.png" mode="" />
        <text class="txt">退款政策</text>
        <text class="desc">{{orderData.payTimeDesc}}</text>
      </view>
      <view class="refund-table">
        <!-- 表头 -->
        <view class="table-row head">
          <view class="table-cell">申请退款时间</view>
          <view class="table-cell">退款比例</view>
          <view class="table-cell">退款金额</view>
        </view>

        <!-- 表格内容 - 动态渲染 -->
        <view class="table-row" wx:for="{{orderData.orderRefundList}}" wx:key="index">
          <view class="table-cell">{{item.date}}</view>
          <view class="table-cell">{{item.refundProportion}}</view>
          <view class="table-cell">￥{{item.price}}</view>
        </view>
      </view>
      <text class="bottom-desc">{{orderData.refundRuleDesc}}</text>
    </view>
    <!-- 免责声明 -->
    <view class="activity-tips">
      <view class="title-box">
        <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrder/info_icon.png" mode="" />
        <text>免责声明</text>
      </view>
      <view wx:for="{{tips}}" wx:key="indx" class="tips-content-item">
        <view class="left"></view>
        <text class="right">{{item}}</text>
      </view>
    </view>
  </scroll-view>
  <!-- 底部按钮 -->
  <view class="bottom-bar">
    <view 
      class="bottom-bar-content {{isBtmDisabled ? 'disabled' : ''}}"
      bindtap="submit">
      <view>
        <text>确认支付<text wx:if="{{orderData.totalPrice > 0}}">￥{{orderData.totalPrice}}</text></text>
      </view>
    </view>
  </view>
</block>
<skeleton-screen wx:else></skeleton-screen>