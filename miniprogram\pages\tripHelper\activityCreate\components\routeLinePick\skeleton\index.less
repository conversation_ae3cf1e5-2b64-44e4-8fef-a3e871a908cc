.tabs-wrap {
	margin: 0 32rpx 32rpx;
	padding: 8rpx;
	border-radius: 32rpx;
	background: #fff;
	display: flex;
	.tab-item {
		flex: 1;
		height: 84rpx;
		background: @bg-gray-color;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		&:last-child {
			margin-left: 10rpx;
			background-color: #fff;
		}
		.icon {
			width: 40rpx;
			height: 40rpx;
			margin-right: 16rpx;
			background-color: @bg-gray-color;
		}
		.tab-title {
			width: 114rpx;
			height: 40rpx;
			background-color: @bg-gray-color;
		}
	}
}

.list {
	display: flex;
	align-items: center;
	overflow-x: auto;
	width: 100%;
	padding-left: 24rpx;
	box-sizing: border-box;
	.item {
		width: 216rpx;
		height: 320rpx;
		padding: 8rpx 8rpx 16rpx;
		box-sizing: border-box;
		background: #fff;
		border-radius: 32rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 16rpx;
		flex-shrink: 0;
		.image {
			background: @bg-gray-color;
			border-radius: 24rpx;
			width: 100%;
			height: 180rpx;
		}
		.label {
			margin: 8rpx 0 16rpx;
			width: 158rpx;
			height: 34rpx;
			background: @bg-gray-color;
		}
		.circle {
			background: #fff;
			border: 4rpx solid @bg-gray-color;
			width: 32rpx;
			height: 32rpx;
			border-radius: 50%;
		}
	}
}

.text-row {
	width: 180rpx;
	height: 34rpx;
	background: #fff;
	margin: 24rpx auto 0;
}