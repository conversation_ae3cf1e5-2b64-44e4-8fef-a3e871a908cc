import appConfig from "../../config/app.config";

Page({

  /**
   * 页面的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl,
    businessList: [
      {
        inactiveIcon: `${appConfig.staticBaseUrl}/home/<USER>
        activeIcon: `${appConfig.staticBaseUrl}/home/<USER>
        name: '酒店',
        type: 'hotel'
      },
      {
        inactiveIcon: `${appConfig.staticBaseUrl}/home/<USER>
        activeIcon: `${appConfig.staticBaseUrl}/home/<USER>
        name: '机票',
        type: 'flight'
      },
      {
        inactiveIcon: `${appConfig.staticBaseUrl}/home/<USER>
        activeIcon: `${appConfig.staticBaseUrl}/home/<USER>
        name: '火车票',
        type: 'train'
      },
      {
        inactiveIcon: `${appConfig.staticBaseUrl}/home/<USER>
        activeIcon: `${appConfig.staticBaseUrl}/home/<USER>
        name: '门票',
        type: 'ticket'
      },
    ] as Home.IBusinessItem[],
    businessType: 'hotel'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  handleHotelList() {
    wx.navigateTo({
      url: '/pages/hotel/hotelList/hotelList',
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  handleBusinessSwitch(e: WechatMiniprogram.TouchEvent<{}, {}, Pick<Home.IBusinessItem, 'type'>>) {
    const type = e.currentTarget.dataset.type;
    switch(type) {
      case "hotel":
        break;
      case "flight":
        // todo: 跳转webview
        break;
      case "train":
        // todo: 跳转webview
        break;
      case "ticket":
        // todo: 跳转webview
        break;
      default:
    }
    // this.setData({
    //   businessType: e.currentTarget.dataset.type
    // });
  }
});