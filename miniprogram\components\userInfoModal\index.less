/* 用户信息填写弹框样式 */
.user-info-content {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
}

/* 头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  margin-bottom: 80rpx;
  position: relative;

  .modal-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
  }

  .close-btn {
    position: absolute;
    right: 32rpx;
    top: 32rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .close-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

/* 头像上传 */
.avatar-section {
  margin-bottom: 80rpx;
  padding: 0 32rpx;

  .section-title {
    display: block;
    font-size: 28rpx;
    color: #66666E;
    margin-bottom: 24rpx;
  }

  .avatar-container {
    display: flex;
    justify-content: center;

    .avatar-display {
      position: relative;
      width: 144rpx;
      height: 144rpx;

      .avatar-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .avatar-delete {
        position: absolute;
        top: 0rpx;
        right: 0rpx;
        width: 40rpx;
        height: 40rpx;
        background: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        .delete-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }

    .avatar-upload {
      width: 144rpx;
      height: 144rpx;
      border-radius: 50%;
      background: #F7F7F7;
      border: 2rpx dashed #DBDBDB;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      &::after {
        border: none;
      }

      .avatar-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .upload-icon {
          width: 48rpx;
          height: 48rpx;
          margin-bottom: 8rpx;
        }

        .upload-text {
          font-size: 24rpx;
          color: #99999E;
        }
      }
    }
  }
}

/* 昵称输入 */
.nickname-section {
  margin-bottom: 80rpx;
  padding: 0 32rpx;

  .section-title {
    display: block;
    font-size: 28rpx;
    color: #66666E;
    margin-bottom: 24rpx;
  }

  .nickname-input {
    width: 100%;
    height: 96rpx;
    background: #F7F7F7;
    border-radius: 16rpx;
    padding: 0 24rpx;
    font-size: 32rpx;
    color: #33333E;
    box-sizing: border-box;

    &::placeholder {
      color: #99999E;
    }
  }
}

/* 性别选择 */
.gender-section {
  margin-bottom: 80rpx;
  padding: 0 32rpx;

  .section-title {
    display: block;
    font-size: 28rpx;
    color: #66666E;
    margin-bottom: 24rpx;
  }

  .gender-options {
    display: flex;
    gap: 32rpx;
    justify-content: center;

    .gender-option {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx;
      border-radius: 64rpx;
      background: #F7F7F7;
      min-width: 268rpx;
      transition: all 0.3s ease;

      &.selected {
        background: linear-gradient( 138deg, #0198FF 0%, #0DC0FE 100%);
      }

      .gender-icon {
        width: 64rpx;
        height: 64rpx;
        margin-right: 24rpx;
      }

      .gender-text {
        font-size: 28rpx;
        color: #33333E;
      }

      &.selected .gender-text {
        color: #fff;
      }
    }
  }
}

/* 提交按钮 */
.submit-section {
  padding: 0 32rpx 48rpx;

  .submit-btn {
    background: #0198FF;
    width: 100%;
    height: 108rpx;
    border-radius: 120rpx;
    font-size: 32rpx;
    font-weight: 600;
  }
}
