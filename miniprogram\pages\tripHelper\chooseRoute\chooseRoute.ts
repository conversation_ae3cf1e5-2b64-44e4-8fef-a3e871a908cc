// pages/tripHelper/chooseRoute/chooseRoute.ts
import appConfig from "../../../config/app.config";
import { getRegionSelectApi, getRouteListApi } from "../../../api/tripHelper/routeLine";
import { emitter } from "../../../utils/mitt";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl,
    searchValue: "",
    expandPopupVisible: false,
    filterType: "",
    // Filter组件的显示状态
    filterData: {
      cityLabel: { key: '', value: '' },
      daysLabel: '',
      currentDays: '' // 当前选中的天数
    },
    // 路线列表加载状态
    routeLoading: false,
    routeLoadingMore: false,
    routeHasMore: true,
    routePage: 1,
    routePageSize: 10,
    // 路线列表数据
    routeList: [] as any[],
    // 选中的路线ID
    selectedRouteId: '',
    // 是否显示确定按钮
    showConfirmBtn: false,
    // 城市选择数据
    hotCities: [] as RouteLine.HotCity[],
    regionList: [] as RouteLine.RegionList[],
    hasNext: false,
    routeData:{},
    project:''// 项目类型
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    this.setData({
      project: options.projectId
    });
    this.getRegionSelect();
    this.loadRouteList(true); // 初始加载
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  onInput(e: any) {
    this.setData({
      searchValue: e.detail.value,
    })
  },

  onIconTap() {
    this.setData({
      searchValue: "",
    })
    // 清除内容后刷新列表
    this.loadRouteList(true);
  },

  onSearch() {
    // 处理搜索确认事件
    this.loadRouteList(true);
  },

  onExpandPopupClose() {
    this.setData({
      expandPopupVisible: false,
    });
    // 重置Filter组件的展开状态
    const filterComponent = this.selectComponent('#filter-component');
    if (filterComponent) {
      filterComponent.resetExpandedStates();
    }
  },

  // Filter组件事件处理
  onCityClick(e: any) {
    const { expanded, type } = e.detail;
    this.setData({
      expandPopupVisible: expanded,
      filterType: expanded ? type : ''
    });
  },

  onDaysClick(e: any) {
    const { expanded, type } = e.detail;
    this.setData({
      expandPopupVisible: expanded,
      filterType: expanded ? type : ''
    });
  },

  // Days组件事件处理
  onDaySelect(e: any) {
    const { label } = e.detail;
    console.log('选择天数:', label);
    // 暂时不更新显示，等用户确认后再更新
  },

  onDayConfirm(e: any) {
    const { label, value } = e.detail;
    this.setData({
      'filterData.daysLabel': label,
      'filterData.currentDays': value,
      expandPopupVisible: false
    });
    // 重置Filter组件的展开状态
    const filterComponent = this.selectComponent('#filter-component');
    if (filterComponent) {
      filterComponent.resetExpandedStates();
    }
    // 筛选条件变化，重新加载路线列表
    this.loadRouteList(true);
  },

  onDayCancel() {
    this.setData({
      'filterData.daysLabel': '', // 清空天数显示
      'filterData.currentDays': '', // 重置选中天数
      expandPopupVisible: false
    });
    // 重置Filter组件的展开状态
    const filterComponent = this.selectComponent('#filter-component');
    if (filterComponent) {
      filterComponent.resetExpandedStates();
    }
  },

  onCitySelect(e: any) {
    const { key, value,expandPopupVisible=false } = e.detail;
    this.setData({
      'filterData.cityLabel': { key, value },
      expandPopupVisible,
    });
    // 重置Filter组件的展开状态
    const filterComponent = this.selectComponent('#filter-component');
    if (filterComponent) {
      filterComponent.resetExpandedStates();
    }
    // 筛选条件变化，重新加载路线列表
    this.loadRouteList(true);
  },

  // 路线选择事件处理
  onSelectRoute(e: any) {
    const { routeId, selected ,routeData} = e.detail;
    console.log('routeId', e.detail);
    if (selected) {
      // 选中路线，单选逻辑
      this.setData({
        selectedRouteId: routeId,
        showConfirmBtn: true,
        routeData
      });
    } else {
      // 取消选中
      this.setData({
        selectedRouteId: '',
        showConfirmBtn: false,
        routeData:{}
      });
    }
  },



  // 确定按钮点击事件
  onConfirmRoute() {
    if (this.data.selectedRouteId) {
      // 这里可以添加路线确认的逻辑
      emitter.emit('routeLineSelect', { 
        value: this.data.routeData
      })
       wx.navigateBack()
    }
  },

  /**
   * scroll-view触底事件处理函数
   */
  onScrollToLower() {
    // 检查是否正在加载或没有更多数据
    if (this.data.routeLoadingMore || !this.data.routeHasMore) {
      return;
    }
    
    // 触发加载更多
    this.loadRouteList(false);
  },

  /**
   * 获取城市选择数据
   */
  async getRegionSelect() {
    try {
      const { data } = await getRegionSelectApi();
      this.setData({
        hotCities: data.hotCities,
        regionList: data.regionList
      });
    } catch (error) {
      console.error('获取城市选择数据失败:', error);
    }
  },

  /**
   * 加载路线列表
   * @param {boolean} refresh 是否刷新（重新加载第一页）
   */
  async loadRouteList(refresh = false) {
    try {
      // 设置加载状态
      if (refresh) {
        this.setData({
          routeLoading: true,
          routePage: 1,
          routeHasMore: true
        });
      } else {
        this.setData({
          routeLoadingMore: true
        });
      }
      let {filterData,routePage,routePageSize,searchValue,project} = this.data;
      // 准备请求参数
      const params = {
        pageIndex: refresh ? 1 : routePage + 1,
        pageSize: routePageSize,
        cityId: filterData.cityLabel.key || '',
        journeyDays: filterData.currentDays,
        project,
        name: searchValue || ''
      };

      // 调用实际的路线列表API
      const { data } = await getRouteListApi(params);
      // 处理API响应数据
      const newRoutes = data.records || [];
      const hasMore = data.hasNext || false;

      this.setData({
        routeList: refresh ? newRoutes : [...this.data.routeList, ...newRoutes],
        routePage: params.pageIndex,
        routeHasMore: hasMore,
        routeLoading: false,
        routeLoadingMore: false
      });
    } catch (error) {
      console.error('加载路线列表失败:', error);
      this.setData({
        routeLoading: false,
        routeLoadingMore: false
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },
    /**
   * 生命周期函数--监听页面隐藏
   */
    onHide() {

    },
  
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {
  
    },
  
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
  
    },
  
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
      // 页面级触底事件（当前由scroll-view处理，此处暂时保留）
    },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
})