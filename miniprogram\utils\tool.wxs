/**
 * 将日期格式化为月-日格式，并判断是否为今天、明天、后天
 * @param dateStr 日期字符串，格式为 YYYY-MM-DD
 * @returns 格式化后的日期字符串
 */
function formatDateToMonthDay(dateStr) {
  // 检查输入是否有效
  if (!dateStr || dateStr.length !== 10) {
    return dateStr;
  }

  // 解析传入的日期 - WXS中需要手动解析
  var parts = dateStr.split('-');
  if (parts.length !== 3) {
    return dateStr;
  }
  
  var year = parseInt(parts[0]);
  var month = parseInt(parts[1]);
  var day = parseInt(parts[2]);
  
  // 创建日期对象
  var inputDate = getDate(year, month - 1, day);

  // 获取当前日期
  var today = getDate();
  today.setHours(0, 0, 0, 0);

  // 计算明天和后天的日期
  var tomorrow = getDate(today.getTime() + 24 * 60 * 60 * 1000);
  var dayAfterTomorrow = getDate(today.getTime() + 2 * 24 * 60 * 60 * 1000);

  // 比较日期
  var inputTime = inputDate.getTime();
  var todayTime = today.getTime();
  var tomorrowTime = tomorrow.getTime();
  var dayAfterTomorrowTime = dayAfterTomorrow.getTime();

  if (inputTime >= todayTime && inputTime < todayTime + 24 * 60 * 60 * 1000) {
    return "今天";
  } else if (inputTime >= tomorrowTime && inputTime < tomorrowTime + 24 * 60 * 60 * 1000) {
    return "明天";
  } else if (inputTime >= dayAfterTomorrowTime && inputTime < dayAfterTomorrowTime + 24 * 60 * 60 * 1000) {
    return "后天";
  } else {
    // 格式化为 MM-DD
    var monthStr = month < 10 ? '0' + month : '' + month;
    var dayStr = day < 10 ? '0' + day : '' + day;
    return monthStr + '-' + dayStr;
  }
}

/**
 * 格式化日期时间字符串为自定义格式
 * @param dateStr 日期字符串，格式为YYYY-MM-DD或YYYY-MM-DD HH:mm:ss
 * @param format 格式化字符串，如'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的字符串
 */
function formatDateTime(dateStr, format) {
  if (!dateStr || !format) {
    return dateStr;
  }
  // 解析日期和时间
  var dateTimeParts = dateStr.split(' ');
  var dateParts = dateTimeParts[0].split('-');
  var timeParts = dateTimeParts.length > 1 ? dateTimeParts[1].split(':') : [];

  var year = parseInt(dateParts[0]) || 0;
  var month = parseInt(dateParts[1]) || 1;
  var day = parseInt(dateParts[2]) || 1;
  var hour = timeParts.length > 0 ? parseInt(timeParts[0]) : 0;
  var minute = timeParts.length > 1 ? parseInt(timeParts[1]) : 0;
  var second = timeParts.length > 2 ? parseInt(timeParts[2]) : 0;

  // 补零函数
  function pad(n) {
    return n < 10 ? '0' + n : '' + n;
  }

  // 字符串全局替换函数，兼容WXS
  function replaceAll(str, search, replacement) {
    var arr = str.split(search);
    return arr.join(replacement);
  }

  // 计算周几
  var weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  var jsDate = getDate(year, month - 1, day);
  var weekDayStr = weekDays[jsDate.getDay()];

  // 替换格式
  var result = format;
  result = replaceAll(result, 'YYYY', year);
  result = replaceAll(result, 'MM', pad(month));
  result = replaceAll(result, 'DD', pad(day));
  result = replaceAll(result, 'HH', pad(hour));
  result = replaceAll(result, 'mm', pad(minute));
  result = replaceAll(result, 'ss', pad(second));
  result = replaceAll(result, 'dd', weekDayStr);
  return result;
}

/**
 * 在list中查找第一个key等于value的项，并返回该项的targetField字段值
 * @param list 数组
 * @param key 查找的key
 * @param value 查找的value
 * @param targetField 需要返回的字段名
 * @returns 匹配项的targetField字段值，未找到返回undefined
 */
function findValueByKey(list, key, value, targetField) {
  if (!list || !key || typeof value === 'undefined' || !targetField) {
    return undefined;
  }
  for (var i = 0; i < list.length; i++) {
    if (list[i] && list[i][key] === value) {
      return list[i][targetField];
    }
  }
  return undefined;
}

module.exports = {
  formatDateToMonthDay: formatDateToMonthDay,
  formatDateTime: formatDateTime,
  findValueByKey: findValueByKey
};
