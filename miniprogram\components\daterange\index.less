.trigger() {
  width: 36rpx;
  height: 36rpx;
  background-image: url('@{static-base-url}/tripHelper/activity/right-arrow.png');
  background-size: 100%;
}

.daterange-wrap {
  padding-bottom: 60rpx;
  .daterange-header {
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: @text-title-color;
    font-size: 32rpx;
    line-height: 50rpx;
    &.is-start {
      .left-trigger {
        &::before {
          content: "取消";
        }
      }
      .right-trigger {
        display: flex;
        &::before {
          content: "";
          .trigger();
        }
      }
    }
    &.is-end {
      .left-trigger {
        display: flex;
        &::before {
          content: "";
          .trigger();
          transform: rotateY(180deg);
        }
      }
      .right-trigger {
        &::before {
          content: "确定";
          color: @primary-color;
        }
      }
    }
    .daterange-title {
      font-size: 36rpx;
      font-weight: bold;
    }
  }
}
