/* pages/hotel/hotelList/components/AreaDistance/AreaDistance.wxss */

.area-distance {
  display: flex;
  height: 100%;
  background-color: #fff;
}

/* 第一栏：一级分类列表样式 */
.first-column {
  width: 162rpx;
  background-color: #f5f5f5;
  // border-right: 1rpx solid #e0e0e0;
  overflow-y: auto;
  max-height: 600rpx;
}

.category-item {
  display: flex;
  align-items: center;
  height: 120rpx;
  padding: 0 24rpx;
  box-sizing: border-box;

  &:active {
    background-color: #e0e0e0;
  }
  
  &.active {
    background-color: #fff;
    // border-right: 4rpx solid #568DED;
    
    .category-name {
      color: #568DED;
      font-weight: 500;
    }
  }
}

.category-name {
  font-size: 28rpx;
  color: #33333e;
}

/* 第二栏：二级分类列表样式（三级结构时显示） */
.second-column {
  width: 200rpx;
  border-right: 1rpx solid #f0f0f0;
  overflow-y: auto;
  max-height: 600rpx;
}

.sub-category-item {
  display: flex;
  align-items: center;
  height: 120rpx;
  margin:0 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  &:active {
    background-color: #e8e8e8;
  }
  
  &.active {
    background-color: #fff;
    // border-right: 4rpx solid #568DED;
    
    .sub-category-name {
      color: #568DED;
      font-weight: 500;
    }
  }
}

.sub-category-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

/* 第三栏：最终选项列表样式 */
.third-column {
  flex: 1;
  overflow-y: auto;
  max-height: 600rpx;
  
  &.two-level {
    /* 二级结构时，第三栏占据更多空间 */
    flex: 1;
  }
  
  &.three-level {
    /* 三级结构时，第三栏与前两栏平衡 */
    flex: 1;
  }
}

.final-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120rpx;
  margin: 0 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.selectable:active {
    background-color: #f5f5f5;
  }
  
  &.selected {
    .final-item-name {
      color: #568DED;
      font-weight: 500;
    }
  }
  
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.final-item-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
  transition: color 0.2s ease;
  margin-bottom: 8rpx;
}

.final-item-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
  margin-top: 4rpx;
  flex: 1;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
}

/* 左侧内容区域样式 */
.final-item .final-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 适配不同屏幕尺寸 */
// @media (max-width: 375px) {
//   .first-column,
//   .second-column {
//     width: 180rpx;
//   }
  
//   .category-item,
//   .sub-category-item,
//   .final-item {
//     padding: 28rpx 20rpx;
//   }
  
//   .category-name,
//   .sub-category-name,
//   .final-item-name {
//     font-size: 26rpx;
//   }
  
//   .final-item-desc {
//     font-size: 22rpx;
//   }
// }

/* 滚动条样式优化 */
// .first-column::-webkit-scrollbar,
// .second-column::-webkit-scrollbar,
// .third-column::-webkit-scrollbar {
//   width: 6rpx;
// }

// .first-column::-webkit-scrollbar-thumb,
// .second-column::-webkit-scrollbar-thumb,
// .third-column::-webkit-scrollbar-thumb {
//   background-color: #c1c1c1;
//   border-radius: 6rpx;
// }

// /* 响应式布局：当空间不足时调整栏宽 */
// @media (max-width: 320px) {
//   .first-column,
//   .second-column {
//     width: 160rpx;
//   }
// }

/* 底部操作按钮样式 */
.bottom-actions {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24rpx;
  z-index: 1000;
}

.reset-btn {
  flex: 1;
  height: 104rpx;
  background: #F3F3F3;
  color: #11111E;
  font-size: 32rpx;
  border: none;
  border-radius: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::after {
    border: none;
  }
  
  &:active {
    background-color: #ebebeb;
  }
}

.confirm-btn {
  flex: 1;
  height: 104rpx;
  background-color: #568DED;
  color: #fff;
  font-size: 32rpx;
  border-radius: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::after {
    border: none;
  }
  
  &:active {
    background-color: #4a7bd9;
  }
}