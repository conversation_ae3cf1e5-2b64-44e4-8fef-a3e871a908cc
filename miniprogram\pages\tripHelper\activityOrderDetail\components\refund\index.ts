import { cancelOrderApi } from '../../../../../api/tripHelper/activityOrderDetail'

interface RefundInfo{
  orderNo:string | number;
  personList:person[];
  refundAmount:number;
  totalAmount:number;
}
interface person{
  type:string;
  price:number;
  num:number;
}
Component({
  properties: {
    // 控制弹窗开关
    visible: {
      type: Boolean,
      value: false
    },
    refundData:{
      type:Object,
      value:{} as RefundInfo
    }
  },

  data: {
    refundList: [
      { time: "5月24日 00:00前", detail: "100%",price:'180.00' },
      { time: "5月24日00:00~5月25日00:00前", detail: "30%" ,price:'60.00'},
      { time: "5月25日 00:00后", detail: "0%，自行协商",price:'0.00' }
    ],
  },

  methods: {
    closePopClick(){
      this.setData({
        visible:false
      })
    },
    async sumbtm(){
      const { orderNo } = this.data.refundData
      const { code , isSuccess , data , message } = await cancelOrderApi({orderNo})
      if(code === 200 && isSuccess && data){
        this.closePopClick()
        this.triggerEvent('refreshPage', { needLoading:true })
      }else{
        wx.displayToast({ title: message, icon: 'none' });

        // showToast默认1.5秒
        setTimeout(() => {
          this.closePopClick()
        }, 1500);
      }
    }
  }
}) 