<view class="weui-navigation-bar {{extClass}} {{borderBottom ? 'has-border' : ''}}">
  <view
    class="weui-navigation-bar__inner {{ios ? 'ios' : 'android'}}"
    style="color: {{computedColor}}; background: {{computedBackground}}; {{displayStyle}}; {{innerPaddingRight}}; {{safeAreaTop}}; {{borderBottom ? 'border-bottom: 1px solid ' + borderColor + ';' : ''}}"
  >

    <!-- 左侧按钮 -->
    <view class='weui-navigation-bar__left' style="{{leftWidth}}">
      <block wx:if="{{back || homeButton}}">
        <!-- 返回上一页 -->
        <block wx:if="{{back}}">
          <view class="weui-navigation-bar__buttons weui-navigation-bar__buttons_goback">
            <view
              bindtap="back"
              class="weui-navigation-bar__btn_goback_wrapper"
              hover-class="weui-active"
              hover-stay-time="100"
              aria-role="button"
              aria-label="返回"
            >
              <!-- 自定义返回图标 -->
              <block wx:if="{{backIcon}}">
                <image
                  src="{{backIcon}}"
                  class="weui-navigation-bar__custom_back_icon"
                  style="filter: {{computedBackIconColor === '#ffffff' ? 'brightness(0) invert(1)' : ''}};"
                />
              </block>
              <!-- 默认返回图标 -->
              <block wx:else>
                <view
                  class="weui-navigation-bar__button weui-navigation-bar__btn_goback"
                  style="background-color: {{computedBackIconColor}};"
                ></view>
              </block>
            </view>
          </view>
        </block>
        <!-- 返回首页 -->
        <block wx:if="{{homeButton}}">
          <view class="weui-navigation-bar__buttons weui-navigation-bar__buttons_home">
            <view
              bindtap="home"
              class="weui-navigation-bar__btn_home_wrapper"
              hover-class="weui-active"
              aria-role="button"
              aria-label="首页"
            >
              <view
                class="weui-navigation-bar__button weui-navigation-bar__btn_home"
                style="background-color: {{computedBackIconColor}};"
              ></view>
            </view>
          </view>
        </block>
      </block>
      <block wx:else>
        <slot name="left"></slot>
      </block>
    </view>

    <!-- 标题 -->
    <view
      class='weui-navigation-bar__center {{titleAlign === "left" ? "align-left" : ""}}'
      style="font-size: {{titleSize}}; font-weight: {{titleWeight}};"
    >
      <view wx:if="{{loading}}" class="weui-navigation-bar__loading" aria-role="alert">
        <view
          class="weui-loading"
          aria-role="img"
          aria-label="加载中"
        ></view>
      </view>
      <block wx:if="{{title}}">
        <text class="weui-navigation-bar__title">{{title}}</text>
      </block>
      <block wx:else>
        <slot name="center"></slot>
      </block>
    </view>

    <!-- 右侧留空 -->
    <view class='weui-navigation-bar__right'>
      <slot name="right"></slot>
    </view>
  </view>
</view>
