import { getImageUrl } from "../../../utils/images";
import { getUserInfo, sendLogoutCode, sendLogout } from "../../../api/my";
import { ESmsType } from "../../../enum/user";

// pages/user/accountCancellationVerify/index.ts
Page({
  data: {
    infoIcon: getImageUrl('user/info.png'),
    accountIcon: getImageUrl('user/account.png'),
    accountArrowIcon: getImageUrl('user/account_arrow.png'),
    code: '',
    focus: false,
    countdown: 59, // 倒计时秒数
    mobile: '', // 用户手机号
    maskedMobile: '', // 脱敏后的手机号
  },
  
  timer: null as number | null,

  /**
   * 手机号脱敏处理
   * @param mobile 原始手机号
   * @returns 脱敏后的手机号，格式：138****1234
   */
  maskMobile(mobile: string): string {
    if (!mobile || mobile.length !== 11) {
      return mobile;
    }
    return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },

  /**
   * 获取用户信息
   * 调用getUserInfo接口获取用户手机号并进行脱敏处理
   */
  async getUserInfoData() {
    try {
      const { isSuccess, data } = await getUserInfo();
      if (isSuccess && data && data.mobile) {
        const maskedMobile = this.maskMobile(data.mobile);
        this.setData({
          mobile: data.mobile,
          maskedMobile: maskedMobile
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 发送注销验证码
   * 静默发送，只在失败时显示错误提示
   */
  async sendVerificationCode() {
    try {
      const { isSuccess, message } = await sendLogoutCode();
      if (!isSuccess) {
        // 发送失败，显示错误信息
        wx.displayToast({
          title: message || '发送验证码失败',
          icon: 'none'
        });
      }
      // 发送成功时不显示任何提示
    } catch (error) {
      console.error('发送验证码失败:', error);
      wx.displayToast({
        title: '发送验证码失败，请重试',
        icon: 'none'
      });
    }
  },
  
  onLoad() {
    // 获取用户信息
    this.getUserInfoData();
    // 发送验证码
    this.sendVerificationCode();
    // 开始倒计时
    this.startCountdown();
  },
  
  onUnload() {
    // 页面卸载时清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  
  startCountdown() {
    // 清除之前的定时器
    if (this.timer) {
      clearInterval(this.timer);
    }

    // 重置倒计时为59秒
    this.setData({
      countdown: 59
    });

    this.timer = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({
          countdown: this.data.countdown - 1
        });
      } else {
        if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }
    }, 1000);
  },
  
  onInput(e: WechatMiniprogram.Input) {
    let value = e.detail.value.replace(/\D/g, '').slice(0, 6);
    this.setData({ code: value });
  },
  
  onFocus() {
    this.setData({ focus: true });
  },
  
  onBlur() {
    this.setData({ focus: false });
  },
  
  onTapCodeBox() {
    this.setData({ focus: true });
  },

  /**
   * 重新发送验证码
   * 只有在倒计时结束后才能点击
   */
  onResendCode() {
    // 只有倒计时结束才能重新发送
    if (this.data.countdown > 0) {
      return;
    }

    // 重新发送验证码
    this.sendVerificationCode();
    // 重新开始倒计时
    this.startCountdown();
  },
  
  async onSubmit() {
    if (this.data.code.length !== 6) {
      wx.displayToast({
        title: '请输入6位验证码',
        icon: 'none'
      });
      return;
    }

    // 二次确认
    wx.showModal({
      title: '确认注销',
      content: '注销后账号无法找回，确定要注销吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performLogout();
        }
      }
    });
  },

  /**
   * 执行账号注销
   */
  async performLogout() {

    try {
      // 调用注销接口
      const { isSuccess, message } = await sendLogout({
        smsCode: this.data.code,
        smsType: ESmsType.LOGOUT
      });

      if (isSuccess) {
        // 注销成功
        wx.showModal({
          title: '注销成功',
          content: '您的账号已成功注销，感谢您的使用！',
          showCancel: false,
          success: () => {
            // 跳转到登录页面
            this.navigateToLogin();
          }
        });
      } else {
        // 注销失败
        wx.showModal({
          title: '注销失败',
          content: message || '注销失败，请检查验证码是否正确',
          showCancel: false
        });
      }
    } catch (error) {
      // 隐藏加载提示
      wx.hidePrevLoading();
      console.error('注销失败:', error);
      wx.showModal({
        title: '注销失败',
        content: '网络异常，请稍后重试',
        showCancel: false
      });
    }
  },

  /**
   * 注销成功后的处理
   */
  navigateToLogin() {
    // 清除本地存储的用户信息和token
    wx.clearStorageSync();

    // 跳转到首页，重新启动应用
    wx.reLaunch({
      url: '/pages/login/index'
    });
  }
})
