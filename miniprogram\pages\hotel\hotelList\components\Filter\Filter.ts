// pages/hotel/hotelList/components/Filter/Filter.ts

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前排序显示文本
    currentSortLabel: {
      type: String,
      value: ''
    },
    // 价格/星级选中数量
    priceStarCount: {
      type: Number,
      value: 0
    },
    // 位置距离选中名称
    distanceLabel: {
      type: String,
      value: ''
    },
    // 筛选选中数量
    filterCount: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 各筛选项的展开状态
    sortExpanded: false,
    priceStarExpanded: false,
    distanceExpanded: false,
    filterExpanded: false
  },


  /**
   * 组件的方法列表
   */
  methods: {
    // 统一的展开状态管理方法
    setExpandState(targetType: string) {
      const currentState = this.data[`${targetType}Expanded` as keyof typeof this.data];
      const newExpandedState = !currentState;
      
      // 设置所有展开状态，确保互斥
      this.setData({
        sortExpanded: targetType === 'sort' ? newExpandedState : false,
        priceStarExpanded: targetType === 'priceStar' ? newExpandedState : false,
        distanceExpanded: targetType === 'distance' ? newExpandedState : false,
        filterExpanded: targetType === 'filter' ? newExpandedState : false
      });
      
      return newExpandedState;
    },

    // 点击智能排序
    onSortClick() {
      const expanded = this.setExpandState('sort');
      this.triggerEvent('sortClick', { expanded });
    },

    // 点击价格/星级
    onPriceStarClick() {
      const expanded = this.setExpandState('priceStar');
      this.triggerEvent('priceStarClick', { expanded });
    },

    // 点击位置距离
    onDistanceClick() {
      const expanded = this.setExpandState('distance');
      this.triggerEvent('distanceClick', { expanded });
    },

    // 点击筛选
    onFilterClick() {
      const expanded = this.setExpandState('filter');
      this.triggerEvent('filterClick', { expanded });
    },

    // 重置所有展开状态（供页面调用）
    resetExpandedStates() {
      this.setData({
        sortExpanded: false,
        priceStarExpanded: false,
        distanceExpanded: false,
        filterExpanded: false
      });
    }
  }
})