import { getImageUrl } from '../../../../utils/images'

// 提现结果类型
type WithdrawResultType = 'success' | 'fail' | 'processing'

Page({
  data: {
    // 提现结果类型
    resultType: 'processing' as WithdrawResultType,

    // 提现金额
    amount: 0,

    // 成功图标
    successIcon: getImageUrl('tripHelper/success.png'),
    // 提现中图标
    processingIcon: getImageUrl('tripHelper/my/wallet_ing.png'),

    // 失败图标
    failIcon: getImageUrl('tripHelper/fail.png'),

    // 提示文本
    tipText: '可在"微信支付-服务-钱包-账单"查看明细'
  },

  onLoad(options: { type?: string; amount?: string }) {
    // 从上一页获取提现结果和金额
    const { type, amount } = options

    this.setData({
      resultType: (type as WithdrawResultType) || 'processing',
      amount: parseFloat(amount || '0') || 0
    })
  },

  /**
   * 获取结果图标
   */
  getResultIcon(): string {
    switch (this.data.resultType) {
      case 'success':
        return this.data.successIcon
      case 'processing':
        return this.data.processingIcon
      case 'fail':
      default:
        return this.data.failIcon
    }
  },

  /**
   * 获取结果文本
   */
  getResultText(): string {
    switch (this.data.resultType) {
      case 'success':
        return '提现成功'
      case 'processing':
        return '提现中'
      case 'fail':
      default:
        return '提现失败'
    }
  },

  /**
   * 获取提示文本
   */
  getTipText(): string {
    switch (this.data.resultType) {
      case 'success':
        return this.data.tipText
      case 'processing':
        return '提现申请已提交，预计1-3个工作日到账'
      case 'fail':
      default:
        return '提现失败，请稍后重试或联系客服'
    }
  }
})
