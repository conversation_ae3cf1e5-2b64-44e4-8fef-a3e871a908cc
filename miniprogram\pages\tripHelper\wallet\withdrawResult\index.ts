import { getImageUrl } from '../../../../utils/images'

// 提现结果类型
type WithdrawResultType = 'success' | 'fail'

Page({
  data: {
    // 提现结果类型
    resultType: 'success' as WithdrawResultType,
    
    // 提现金额
    amount: 2000.00,
    
    // 成功图标
    successIcon: getImageUrl('tripHelper/success.png'),
    
    // 失败图标
    failIcon: getImageUrl('tripHelper/fail.png'),
    
    // 提示文本
    tipText: '可在"微信支付-服务-钱包-账单"查看明细'
  },

  onLoad(options: any) {
    // 从上一页获取提现结果和金额
    const { type, amount } = options
    
    this.setData({
      resultType: type || 'success',
      amount: parseFloat(amount) || 2000.00
    })
  },

  /**
   * 获取结果图标
   */
  getResultIcon(): string {
    return this.data.resultType === 'success' ? this.data.successIcon : this.data.failIcon
  },

  /**
   * 获取结果文本
   */
  getResultText(): string {
    return this.data.resultType === 'success' ? '提现成功' : '提现失败'
  },

  /**
   * 获取提示文本
   */
  getTipText(): string {
    if (this.data.resultType === 'success') {
      return this.data.tipText
    } else {
      return '提现失败，请稍后重试或联系客服'
    }
  }
})
