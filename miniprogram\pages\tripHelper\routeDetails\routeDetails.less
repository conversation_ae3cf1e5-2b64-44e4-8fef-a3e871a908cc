/* pages/tripHelper/routeDetails/routeDetails.wxss */
@import "./components/skeleton/routeDetails.skeleton.less";
.route-box{
  padding: 0 32rpx;
}

/* 线路信息卡片 */
.route-card {
  margin: 32rpx 0;
  border-radius: 32rpx;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 左侧：线路名称和类型 */
.route-left {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.route-name {
  font-size: 40rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.route-type {
  display: flex;
  align-items: center;
}

.route-type-box{
  background: #EEF8FF;
  border-radius: 16rpx;
  padding:8rpx 16rpx;
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.type-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}

.type-text {
  font-size: 24rpx;
  color: #0198FF;
  font-weight: 400;
}

/* 右侧：位置信息 */
.route-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  ;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.location-text-box{

  max-width: 140rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.location-text {
  display: inline-block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
/* 底部操作区域 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.action-button {
  width: 100%;
  height: 88rpx;
  background: #0198FF;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
}

.action-button:active {
  background: #0084D6;
  transform: scale(0.98);
}



