// pages/hotel/hotelList/components/ExpandPopup/ExpandPopup.ts

interface ExpandPopupProps {
  visible: boolean;           // 控制显示/隐藏
  maxHeight?: string;         // 最大高度
  maskClosable?: boolean;     // 点击遮罩是否关闭
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 控制弹窗显示/隐藏
    visible: {
      type: Boolean,
      value: false
    },
    // 内容区域最大高度
    maxHeight: {
      type: String,
      value: '70vh'
    },
    // 点击遮罩是否关闭弹窗
    maskClosable: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画状态
    animationClass: ''
  },

  /**
   * 组件生命周期
   */
  observers: {
    'visible'(newVal: boolean) {
      // 根据visible状态更新动画类
      if (newVal) {
        // 延迟添加show类，确保DOM渲染完成
        setTimeout(() => {
          this.setData({ animationClass: 'show' });
        }, 10);
      } else {
        this.setData({ animationClass: 'hide' });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击遮罩关闭弹窗
    onMaskClick() {
      if (this.properties.maskClosable) {
        this.triggerEvent('close');
      }
    },

    // 点击内容区域阻止事件冒泡
    onContentClick(e: any) {
      // 在微信小程序中，catch:tap已经阻止了事件冒泡，无需手动调用stopPropagation
      // 为了兼容性，添加安全检查
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
    },

    // 关闭弹窗
    close() {
      this.triggerEvent('close');
    }
  }
})