<!-- pages/hotel/hotelList/components/Filter/Filter.wxml -->
<view class="filter-container">
    <!-- 智能排序按钮 -->
    <view class="filter-item active" bind:tap="onSortClick">
        <text class="filter-text sort-label">{{currentSortLabel}}</text>
        <t-icon name="chevron-down" size="32rpx" class="filter-arrow {{sortExpanded ? 'rotated' : ''}}" />
    </view>
    <!-- 价格/星级按钮 -->
    <view class="filter-item {{priceStarCount > 0 || priceStarExpanded ? 'active' : ''}}" bind:tap="onPriceStarClick">
        <text class="filter-text">价格/星级</text>
        <view class="filter-content">
            <text wx:if="{{priceStarCount > 0}}" class="filter-count">{{priceStarCount}}</text>
            <t-icon name="chevron-down" size="32rpx" class="filter-arrow {{priceStarExpanded ? 'rotated' : ''}}" />
        </view>
    </view>
    <!-- 位置距离按钮 -->
    <view class="filter-item {{distanceLabel || distanceExpanded ? 'active' : ''}}" bind:tap="onDistanceClick">
        <text class="filter-text distance-label">{{distanceLabel || '位置距离'}}</text>
        <view class="filter-content">
            <t-icon name="chevron-down" size="32rpx" class="filter-arrow {{distanceExpanded ? 'rotated' : ''}}" />
        </view>
    </view>
    <!-- 筛选按钮 -->
    <view class="filter-item {{filterCount > 0 || filterExpanded ? 'active' : ''}}" bind:tap="onFilterClick">
        <text class="filter-text">筛选</text>
        <view class="filter-content">
            <text wx:if="{{filterCount > 0}}" class="filter-count">{{filterCount}}</text>
            <t-icon name="chevron-down" size="32rpx" class="filter-arrow {{filterExpanded ? 'rotated' : ''}}" />
        </view>
    </view>
</view>