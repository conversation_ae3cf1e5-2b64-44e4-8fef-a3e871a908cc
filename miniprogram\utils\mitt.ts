// 定义事件类型
export type Events = {
	/** 活动创建/更新 路线选择事件 */
	routeLineSelect: {
		value: RouteLine.Datum
	}
}

// 事件监听器类型
type EventHandler<T = any> = (event: T) => void

/**
 * Mitt - 轻量级事件发射器
 * 支持TypeScript类型安全
 */
export class Mitt<T extends Events = Events> {
	private events: Map<keyof T, EventHandler[]> = new Map()

	/**
	 * 注册事件监听器
	 * @param type 事件类型
	 * @param handler 事件处理函数
	 */
	on<K extends keyof T>(type: K, handler: EventHandler<T[K]>): void {
		if (!this.events.has(type)) {
			this.events.set(type, [])
		}
		this.events.get(type)!.push(handler as EventHandler)
	}

	/**
	 * 注册一次性事件监听器
	 * @param type 事件类型
	 * @param handler 事件处理函数
	 */
	once<K extends keyof T>(type: K, handler: EventHandler<T[K]>): void {
		const onceHandler = ((event: T[K]) => {
			handler(event)
			this.off(type, onceHandler as EventHandler<T[K]>)
		}) as EventHandler<T[K]>
		
		this.on(type, onceHandler)
	}

	/**
	 * 移除事件监听器
	 * @param type 事件类型
	 * @param handler 事件处理函数（可选，不传则移除该类型的所有监听器）
	 */
	off<K extends keyof T>(type: K, handler?: EventHandler<T[K]>): void {
		if (!this.events.has(type)) return

		if (!handler) {
			// 移除该类型的所有监听器
			this.events.delete(type)
		} else {
			// 移除指定的监听器
			const handlers = this.events.get(type)!
			const index = handlers.indexOf(handler as EventHandler)
			if (index > -1) {
				handlers.splice(index, 1)
			}
			// 如果没有监听器了，删除该类型
			if (handlers.length === 0) {
				this.events.delete(type)
			}
		}
	}

	/**
	 * 发射事件
	 * @param type 事件类型
	 * @param event 事件数据
	 */
	emit<K extends keyof T>(type: K, event: T[K]): void {
		if (!this.events.has(type)) return

		const handlers = this.events.get(type)!
		// 创建副本避免在回调中修改数组
		handlers.slice().forEach(handler => {
			try {
				handler(event)
			} catch (error) {
				console.error(`Error in event handler for ${String(type)}:`, error)
			}
		})
	}

	/**
	 * 获取指定类型的事件监听器数量
	 * @param type 事件类型
	 */
	listenerCount<K extends keyof T>(type: K): number {
		return this.events.get(type)?.length || 0
	}

	/**
	 * 获取所有事件监听器
	 */
	getListeners(): Map<keyof T, EventHandler[]> {
		return new Map(this.events)
	}

	/**
	 * 清空所有事件监听器
	 */
	clear(): void {
		this.events.clear()
	}
}

/**
 * 创建mitt实例的工厂函数
 * 保持与原有API兼容
 */
export function mitt<T extends Events = Events>(): Mitt<T> {
	return new Mitt<T>()
}

// 创建默认的emitter实例
export const emitter = mitt<Events>()

// 导出类型别名，保持向后兼容
export type Emitter<T extends Events = Events> = Mitt<T>