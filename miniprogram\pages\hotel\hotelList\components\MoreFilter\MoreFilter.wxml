<!-- pages/hotel/hotelList/components/MoreFilter/MoreFilter.wxml -->

<!-- 引入筛选类目模板 -->
<import src="./filter-category.wxml"/>

<view class="more-filter-container">
    <view class="filter-layout">
      <!-- 左侧一级分类导航 -->
      <view class="left-categories">
        <view 
          wx:for="{{filterOptions}}" 
          wx:key="index"
          class="category-nav-item {{currentCategoryIndex === index ? 'active' : ''}}"
          data-index="{{index}}"
          data-type-id="{{item.typeId}}"
          bindtap="onCategoryNavClick"
        >
        <!-- 选中状态指示器 -->
          <view 
            wx:if="{{utils.categoryHasSelected(item.typeId, internalSelectedFilters)}}"
            class="selected-indicator"
          ></view>{{item.name}}
        </view>
      </view>
      
      <!-- 右侧筛选内容区域 -->
      <scroll-view 
        class="right-content" 
        scroll-y="{{true}}"
        scroll-into-view="{{scrollIntoView}}"
        scroll-with-animation="{{true}}"
      >
        <!-- 遍历所有一级筛选类目 -->
        <view 
          wx:for="{{filterOptions}}" 
          wx:key="index"
          class="filter-category"
          id="category-{{item.typeId}}"
        >
          <!-- 使用template渲染筛选类目内容 -->
          <template is="filter-category" data="{{item, expandStates, internalSelectedFilters, utils}}" />
        </view>
      </scroll-view>
    </view>
  
  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="reset-btn" bindtap="onResetClick">重置</button>
    <button class="confirm-btn" bindtap="onConfirmClick">完成</button>
  </view>
</view>

<wxs module="utils">
  // 检查一级类目是否有选中项（根据parentTypeId判断）
  function categoryHasSelected(categoryTypeId, selectedFilters) {
    if (!selectedFilters || selectedFilters.length === 0) return false;
    
    for (var i = 0; i < selectedFilters.length; i++) {
      if (selectedFilters[i].parentTypeId === categoryTypeId) {
        return true;
      }
    }
    return false;
  }

  module.exports = {
    categoryHasSelected: categoryHasSelected
  };
</wxs>