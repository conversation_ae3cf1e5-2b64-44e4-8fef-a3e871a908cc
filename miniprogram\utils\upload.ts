import appConfig from '../config/app.config';
import { StorageKeyEnum } from '../enum/app';
import { EImageUploadType } from '../enum/my';

/**
 * 文件上传工具类
 * 专门处理微信小程序的文件上传请求
 */

/**
 * 上传文件的配置选项
 */
export interface UploadOptions {
  /** 接口地址 */
  url: string;
  /** 文件路径 */
  filePath: string;
  /** 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容 */
  name: string;
  /** HTTP 请求 Header */
  header?: Record<string, string>;
  /** HTTP 请求中其他额外的 form data */
  formData?: Record<string, any>;
  /** 超时时间，单位为毫秒 */
  timeout?: number;
  /** 基础URL */
  baseURL?: string;
  /** 是否忽略API错误提示 */
  ignoreApiError?: boolean;
}

/**
 * 上传响应结果
 */
export interface UploadResult<T = any> {
  /** 是否成功 */
  isSuccess: boolean;
  /** 状态码 */
  code: number;
  /** 响应数据 */
  data: T | null;
  /** 响应消息 */
  message: string;
}

/**
 * 文件上传函数
 *
 * @param options 上传配置选项
 * @returns Promise<UploadResult<T>>
 */
export const uploadFile = <T = any>(options: UploadOptions): Promise<UploadResult<T>> => {
  return new Promise((resolve) => {
    const {
      url,
      filePath,
      name,
      header = {},
      formData = {},
      timeout = 60000,
      baseURL = appConfig.baseUrl +'/api',
      ignoreApiError = false
    } = options;

    // 获取认证 Token
    const token = wx.getStorageSync(StorageKeyEnum.Token);

    // 准备请求头部
    const uploadHeaders: Record<string, string> = { ...header };

    // 添加认证 Token
    if (token) {
      uploadHeaders['HT-Token'] = token;
    }

    // 调用微信上传文件API
    wx.uploadFile({
      url: `${baseURL}${url}`,
      filePath,
      name,
      header: uploadHeaders,
      formData,
      timeout,
      success: (res) => {
        try {
          // 解析响应数据
          const responseData = JSON.parse(res.data);
          const isSuccess = responseData.code === 200;
          const result: UploadResult<T> = {
            isSuccess,
            code: responseData.code || res.statusCode,
            data: responseData.data || null,
            message: responseData.message || (isSuccess ? '上传成功' : '上传失败')
          };

          if (!result.isSuccess && !ignoreApiError) {
            wx.displayToast({
              title: result.message,
              icon: 'none'
            });
          }

          resolve(result);
        } catch (error) {
          // JSON解析失败
          const result: UploadResult<T> = {
            isSuccess: false,
            code: res.statusCode,
            data: null,
            message: '响应数据解析失败'
          };

          if (!ignoreApiError) {
            wx.displayToast({
              title: result.message,
              icon: 'none'
            });
          }

          resolve(result);
        }
      },
      fail: (error) => {
        const result: UploadResult<T> = {
          isSuccess: false,
          code: -1,
          data: null,
          message: error.errMsg || '上传失败'
        };

        if (!ignoreApiError) {
          wx.displayToast({
            title: result.message,
            icon: 'none'
          });
        }

        resolve(result);
      }
    });

    // 可以返回uploadTask用于取消上传等操作
    // return uploadTask;
  });
};

/**
 * 上传图片的配置选项
 */
export interface UploadImageOptions extends Partial<UploadOptions> {
  /** 图片上传类型 */
  type: EImageUploadType;
}

/**
 * 上传图片的便捷方法
 *
 * @param filePath 图片文件路径
 * @param options 上传选项，包含必需的type参数
 * @returns Promise<UploadResult<{url: string}>>
 */
export const uploadImage = (
  filePath: string,
  options: UploadImageOptions
): Promise<UploadResult<{ url: string }>> => {
  // 确保 url 参数存在
  if (!options.url) {
    throw new Error('url is required for uploadImage');
  }

  // 确保 type 参数存在
  if (!options.type) {
    throw new Error('type is required for uploadImage');
  }

  // 准备formData，包含type参数
  const formData = {
    ...options.formData,
    type: options.type
  };



  return uploadFile<{ url: string }>({
    filePath,
    name: options.name || 'files', // 使用传入的name参数，默认为'files'
    url: options.url,
    header: options.header,
    formData,
    timeout: options.timeout,
    baseURL: options.baseURL + '/api',
    ignoreApiError: options.ignoreApiError
  });
};

/**
 * 批量上传图片（逐个上传）
 *
 * @param filePaths 图片文件路径数组
 * @param options 上传选项，包含必需的type参数
 * @returns Promise<UploadResult<{url: string}>[]>
 */
export const uploadImages = async (
  filePaths: string[],
  options: UploadImageOptions
): Promise<UploadResult<{ url: string }>[]> => {
  const results: UploadResult<{ url: string }>[] = [];

  for (let i = 0; i < filePaths.length; i++) {
    const filePath = filePaths[i];

    try {
      const result = await uploadImage(filePath, options);
      results.push(result);

      // 如果上传失败，可以选择继续或停止
      if (!result.isSuccess) {
        console.warn(`第 ${i + 1} 张图片上传失败:`, result.message);
        // 这里可以选择 break 来停止后续上传
      }
    } catch (error) {
      console.error(`第 ${i + 1} 张图片上传异常:`, error);
      results.push({
        isSuccess: false,
        code: -1,
        data: null,
        message: '上传异常'
      });
    }
  }

  return results;
};

/**
 * 批量上传图片（并发上传，同时上传多张图片）
 *
 * @param filePaths 图片文件路径数组
 * @param options 上传选项，包含必需的type参数
 * @returns Promise<UploadResult<{url: string}>[]>
 */
export const uploadImagesConcurrent = async (
  filePaths: string[],
  options: UploadImageOptions
): Promise<UploadResult<{ url: string }>[]> => {
  // 创建所有上传任务
  const uploadTasks = filePaths.map(filePath =>
    uploadImage(filePath, options).catch(error => ({
      isSuccess: false,
      code: -1,
      data: null,
      message: error.message || '上传异常'
    } as UploadResult<{ url: string }>))
  );

  // 并发执行所有上传任务
  const results = await Promise.all(uploadTasks);

  return results;
};




/**
 * 字符串转ArrayBuffer（UTF-16转UTF-8再转ArrayBuffer）
 */
const stringToArrayBuffer = (str: string): ArrayBuffer => {
  const utf8Arr: number[] = [];

  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i);

    if (code >= 0x00 && code <= 0x7f) {
      utf8Arr.push(code);
    } else if (code >= 0x80 && code <= 0x7ff) {
      utf8Arr.push(192 | (31 & (code >> 6)));
      utf8Arr.push(128 | (63 & code));
    } else if (
      (code >= 0x800 && code <= 0xd7ff) ||
      (code >= 0xe000 && code <= 0xffff)
    ) {
      utf8Arr.push(224 | (15 & (code >> 12)));
      utf8Arr.push(128 | (63 & (code >> 6)));
      utf8Arr.push(128 | (63 & code));
    } else if (code >= 0x10000 && code <= 0x10ffff) {
      utf8Arr.push(240 | (7 & (code >> 18)));
      utf8Arr.push(128 | (63 & (code >> 12)));
      utf8Arr.push(128 | (63 & (code >> 6)));
      utf8Arr.push(128 | (63 & code));
    }
  }

  const arrayBuf = new ArrayBuffer(utf8Arr.length);
  const buf = new Uint8Array(arrayBuf);
  for (let i = 0; i < utf8Arr.length; i++) {
    buf[i] = utf8Arr[i];
  }
  return arrayBuf;
};

/**
 * 合并多个ArrayBuffer
 */
const mergeArrayBuffers = (buffers: ArrayBuffer[]): ArrayBuffer => {
  const totalLength = buffers.reduce((sum, buf) => sum + buf.byteLength, 0);
  const result = new ArrayBuffer(totalLength);
  const resultView = new Uint8Array(result);

  let offset = 0;
  for (const buffer of buffers) {
    const view = new Uint8Array(buffer);
    resultView.set(view, offset);
    offset += buffer.byteLength;
  }

  return result;
};

/**
 * 真正的批量上传多个图片（一次请求上传多个文件）
 * 使用wx.request配合ArrayBuffer实现multipart/form-data格式
 */
export const uploadMultipleImagesAtOnce = async (
  filePaths: string[],
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<string[]>> => {
  const {
    url,
    baseURL = appConfig.baseUrl + '/api',
    timeout = appConfig.requestTimeout,
    ignoreApiError = false,
    formData = {}
  } = options;

  if (!url) {
    throw new Error('url is required for uploadMultipleImagesAtOnce');
  }

  if (!filePaths || filePaths.length === 0) {
    return {
      isSuccess: true,
      code: 200,
      data: [],
      message: '没有文件需要上传'
    };
  }

  try {
    // 获取认证 Token
    const token = wx.getStorageSync(StorageKeyEnum.Token);

    // 显示上传进度
    wx.displayLoading({
      title: `批量上传${filePaths.length}张图片`
    });

    // 构造multipart/form-data格式的数据
    const boundary = '----WebKitFormBoundary' + Math.random().toString(36).substring(2);
    const CRLF = '\r\n';
    const buffers: ArrayBuffer[] = [];

    // 添加其他表单数据（如type参数）
    for (const [key, value] of Object.entries(formData)) {
      const fieldData = `--${boundary}${CRLF}Content-Disposition: form-data; name="${key}"${CRLF}${CRLF}${value}${CRLF}`;
      buffers.push(stringToArrayBuffer(fieldData));
    }

    // 读取所有文件并添加到buffers
    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];

      // 读取文件为ArrayBuffer
      const fileBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
        wx.getFileSystemManager().readFile({
          filePath,
          success: (res) => {
            resolve(res.data as ArrayBuffer);
          },
          fail: reject
        });
      });

      // 添加文件头部信息
      const fileHeader = `--${boundary}${CRLF}Content-Disposition: form-data; name="files"; filename="image_${i + 1}.jpg"${CRLF}Content-Type: image/jpeg${CRLF}${CRLF}`;
      buffers.push(stringToArrayBuffer(fileHeader));

      // 添加文件内容
      buffers.push(fileBuffer);

      // 添加换行
      buffers.push(stringToArrayBuffer(CRLF));
    }

    // 添加结束标记
    const endBoundary = `--${boundary}--${CRLF}`;
    buffers.push(stringToArrayBuffer(endBoundary));

    // 合并所有buffers
    const finalBuffer = mergeArrayBuffers(buffers);

    // 准备请求头部
    const headers: Record<string, string> = {
      'Content-Type': `multipart/form-data; boundary=${boundary}`
    };

    if (token) {
      headers['HT-Token'] = token;
    }

    // 发送请求
    const response = await new Promise<any>((resolve, reject) => {
      wx.request({
        url: `${baseURL}${url}`,
        method: 'POST',
        header: headers,
        data: finalBuffer,
        timeout,
        success: resolve,
        fail: reject
      });
    });

    wx.hidePrevLoading();

    // 处理响应
    let responseData: any = null;
    let isSuccess = false;

    // 检查HTTP状态码
    if (response.statusCode !== 200) {
      // HTTP状态码不是200，直接认为失败
      isSuccess = false;
    } else {
      // HTTP状态码是200，检查响应数据
      try {
        // 如果response.data是字符串，尝试解析JSON
        if (typeof response.data === 'string') {
          responseData = JSON.parse(response.data);
        } else {
          responseData = response.data;
        }
        isSuccess = responseData?.code === 200;
      } catch (error) {
        // JSON解析失败，认为是失败
        console.error('批量上传响应数据解析失败:', error, response.data);
        isSuccess = false;
      }
    }

    const result: UploadResult<string[]> = {
      isSuccess,
      code: responseData?.code || response.statusCode,
      data: responseData?.data || [],
      message: responseData?.message || (isSuccess ? '批量上传成功' : "批量上传失败")
    };

    // 显示结果提示
    if (!ignoreApiError) {
      wx.displayToast({
        title: result.message,
        icon: result.isSuccess ? 'success' : 'none'
      });
    }

    return result;

  } catch (error: any) {
    wx.hidePrevLoading();

    const result: UploadResult<string[]> = {
      isSuccess: false,
      code: -1,
      data: [],
      message: error.errMsg || error.message || '批量上传失败'
    };

    if (!ignoreApiError) {
      wx.displayToast({
        title: result.message,
        icon: 'none'
      });
    }

    return result;
  }
};


