// pages/order/list/components/orderCard/index.ts
import { getImageUrl } from '../../../../../utils/images';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    orderData: {
      type: Object,
      value: {} as Order.OrderItem
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    checkInDate: '',
    checkOutDate: '',
    hotelIcon: getImageUrl('user/hotel.png'),
    deleteIcon: getImageUrl('user/delete.png'),
    showDeleteButton: false
  },

  /**
   * 数据观察器
   */
  observers: {
    'orderData': function(orderData: Order.OrderItem) {
      if (orderData?.checkInDate && orderData?.checkOutDate) {
        const checkInDate = new Date(orderData.checkInDate);
        const checkOutDate = new Date(orderData.checkOutDate);

        this.setData({
          checkInDate: `${checkInDate.getFullYear()}-${(checkInDate.getMonth() + 1).toString().padStart(2, '0')}-${checkInDate.getDate().toString().padStart(2, '0')}`,
          checkOutDate: `${checkOutDate.getFullYear()}-${(checkOutDate.getMonth() + 1).toString().padStart(2, '0')}-${checkOutDate.getDate().toString().padStart(2, '0')}`
        });
      }

      // 计算是否显示删除按钮
      const showDeleteButton = this.shouldShowDeleteButton(orderData);
      console.log('订单卡片删除按钮显示计算:', {
        orderId: orderData?.orderId,
        status: orderData?.status,
        showDeleteButton
      });
      this.setData({ showDeleteButton });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 判断是否显示删除按钮
     */
    shouldShowDeleteButton(orderData: Order.OrderItem): boolean {
      console.log('shouldShowDeleteButton 被调用:', orderData);

      if (!orderData || !orderData.status) {
        console.log('订单数据为空或状态为空，不显示删除按钮');
        return false;
      }

      // 已完成、已取消、已退款状态显示删除按钮
      const shouldShow = ['3', '4', '6'].includes(orderData.status);
      console.log('删除按钮显示判断:', {
        status: orderData.status,
        shouldShow,
        allowedStatuses: ['3', '4', '6']
      });

      // 临时测试：总是显示删除按钮
      console.log('临时测试：强制显示删除按钮');
      return true;
    },
    /**
     * 卡片点击事件
     */
    onCardClick() {
      this.triggerEvent('cardclick', {
        orderData: this.properties.orderData
      });
    },

    /**
     * 支付按钮点击事件
     */
    onPayClick() {
      this.triggerEvent('actionclick', {
        action: 'pay',
        orderData: this.properties.orderData
      });
    },

    /**
     * 再次预订按钮点击事件
     */
    onRebookClick() {
      this.triggerEvent('actionclick', {
        action: 'rebook',
        orderData: this.properties.orderData
      });
    },

    /**
     * 删除按钮点击事件
     */
    onDeleteClick() {
      this.triggerEvent('actionclick', {
        action: 'delete',
        orderData: this.properties.orderData
      });
    }
  }
});
