import { getImageUrl } from "../../../utils/images";
// http://webapp.test.aitrip123.com/agreements/user
// http://webapp.test.aitrip123.com/agreements/protect
// http://webapp.test.aitrip123.com/agreements/sdk
// http://webapp.test.aitrip123.com/agreements/children
// http://webapp.test.aitrip123.com/agreements/bookNoteInfo
// http://webapp.test.aitrip123.com/agreements/business
// 对应的是用户服务协议、翎游平台个人信息保护政策、内嵌第三方SDK名单、翎游儿童个人信息保护规则及监护人须知、翎游酒店预订条款、营业执照
// pages/user/aboutus/index.ts
const H5BaseUrl = 'https://webapp.test.aitrip123.com/agreements'
Page({

  data: {
    /** 箭头图标，用于列表项右侧 */
    arrowIcon: getImageUrl('arrow.png'),
    // 关于我们菜单
    aboutUsMenus: [{
      title: '用户协议',
      path: 'user'
    }, {
      title: '隐私协议',
      path: 'protect'
    }, {
      title: '第三方信息收集清单',
      path: 'sdk'
    }]
  },

  onMenuClick(e: WechatMiniprogram.BaseEvent) {
    const { path, title } = e.currentTarget.dataset;
    
    // 构建完整的H5页面URL
    const fullUrl = `${H5BaseUrl}/${path}`;
    
    // 跳转到web-view页面，传递URL参数
    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(title || '')}`
    });
  }
})