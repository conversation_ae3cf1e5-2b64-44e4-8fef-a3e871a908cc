import { getProjectList } from '../../../api/tripHelper/activity';

Page({
  customInstanceProperty: {
    /** 更新活动项目的id 注意消费过后reset */
    __activeProjectId: null as Nullable<number>
  },

  /**
   * 页面的初始数据
   */
  data: {
    activeStep: 1,
    activityId: 0,
    steps: [
      {
        title: '选择项目',
        value: 1
      },
      {
        title: '完善信息',
        value: 2
      }
    ],
    projectList: [] as Activity.IProject[],
    activeProject: null as Nullable<Activity.IProject>
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: { activityId?: string }) {
    if (options.activityId) {
      this.setData({
        activityId: Number(options.activityId)
      })
    }
    getProjectList().then(res => {
      if (res.isSuccess) {
        const projectList = res.data ?? [];
        this.setData({
          projectList: projectList,
        }, () => {
          const activeProjectId = this.__activeProjectId;
          if (activeProjectId) {
            this.handleTask(activeProjectId);
            this.__activeProjectId = null;
          }
        });
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  handleStepChange(e: WechatMiniprogram.TouchEvent<{}, {}, { step: number }>) {
    if (this.data.activeStep === 2) {
      this.setData({
        activeStep: e.currentTarget.dataset.step
      })
    }
  },
  setActiveStep(val: number) {
    this.setData({
      activeStep: val
    })
  },
  /** 项目变更 */
  handleProjectChange(e: WechatMiniprogram.CustomEvent<Activity.IProject>) {
    this.setData({
      activeProject: e.detail
    });
  },
  /** 下一步 */
  handleNextStep() {
    this.setData({
      activeStep: 2
    });
  },
  handleTabChange(e: WechatMiniprogram.CustomEvent<{ value: number }>) {
    this.setData({
      activeStep: e.detail.value
    })
  },
  handleUpdateProject(e: WechatMiniprogram.CustomEvent<{ value: number }>) {
    if (this.data.projectList.length) {
      this.handleTask(e.detail.value);
    } else {
      this.__activeProjectId = e.detail.value;
    }
  },
  handleTask(value: number) {
    const target = this.data.projectList.find(item => item.id === value);
    if (target) {
      this.setData({
        activeProject: target
      })
    }
  }
})