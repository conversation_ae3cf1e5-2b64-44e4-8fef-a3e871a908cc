// components/imageUploader/index.ts
import { getImageUrl } from '../../utils/images';
import { uploadMultipleFeedbackImages } from '../../api/home';
import { UploadFunction } from './types';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示标题
    showTitle: {
      type: Boolean,
      value: true
    },
    // 标题文本
    titleText: {
      type: String,
      value: '上传图片'
    },
    // 最大数量
    maxCount: {
      type: Number,
      value: 9
    },
    // 上传按钮文本
    uploadText: {
      type: String,
      value: '上传图片'
    },
    // 上传中文本
    uploadingText: {
      type: String,
      value: '上传中...'
    },
    // 上传图标路径
    uploadIcon: {
      type: String,
      value: ''
    },
    // 删除图标路径
    deleteIcon: {
      type: String,
      value: ''
    },
    // 是否有自定义内容
    hasCustomContent: {
      type: Boolean,
      value: false
    },
    // 是否有自定义标题
    hasCustomTitle: {
      type: Boolean,
      value: false
    },
    // 自定义上传函数
    uploadFunction: {
      type: null,
      value: null
    },
    // 受控图片数据
    value: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 上传状态
    uploading: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 设置默认图标
      if (!this.properties.uploadIcon) {
        this.setData({
          uploadIcon: getImageUrl('login/pic.png')
        });
      }
      if (!this.properties.deleteIcon) {
        this.setData({
          deleteIcon: getImageUrl('user/feedback_close.png')
        });
      }
      // 移除初始图片逻辑
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击上传图片
     */
    onUploadImage() {
      const { value, uploading } = this.data;
      const { maxCount } = this.properties;

      // 检查是否正在上传
      if (uploading) {
        wx.showToast({
          title: '正在上传中，请稍候',
          icon: 'none'
        });
        return;
      }

      // 检查是否达到最大上传数量
      if ((value.length) >= (maxCount || 9)) {
        wx.showToast({
          title: `最多上传${maxCount || 9}张图片`,
          icon: 'none'
        });
        return;
      }

      // 调用微信选择图片API
      wx.chooseMedia({
        count: (maxCount || 9) - value.length,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 获取临时文件路径
          const tempFiles = res.tempFiles.map(file => file.tempFilePath);

          // 上传图片到服务器
          this.uploadImages(tempFiles);
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 批量上传图片
     */
    async uploadImages(filePaths: string[]) {
      this.setData({ uploading: true });

      try {
        // 使用自定义上传函数或默认上传函数
        const uploadFn: UploadFunction = (this.properties.uploadFunction as UploadFunction) || uploadMultipleFeedbackImages;
        const { isSuccess, data, message } = await uploadFn(filePaths);

        if (isSuccess && data && Array.isArray(data)) {
          // 批量上传成功，合并新旧图片
          const newValue = [...(this.data.value || []), ...data];

          this.updateValue(newValue);

          // 触发上传成功事件
          this.triggerEvent('uploadSuccess',{ value: newValue });

          console.log('批量上传成功:', {
            总数: filePaths.length,
            成功: data.length,
            上传的URLs: data
          });
        } else {
          console.error('批量上传失败:', message);
          // 触发上传失败事件
          this.triggerEvent('uploadError', {
            message: message || '上传失败'
          });
        }

      } catch (error) {
        console.error('批量上传异常:', error);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
        // 触发上传失败事件
        this.triggerEvent('uploadError', {
          message: '图片上传失败'
        });
      } finally {
        this.setData({ uploading: false });
      }
    },

    /**
     * 删除已上传图片
     */
    onDeleteImage(e: WechatMiniprogram.TouchEvent) {
      const index = parseInt(e.currentTarget.dataset.index as string);
      const { value } = this.data;

      if (index < 0 || index >= value.length) {
        return;
      }

      // 从数组中删除指定索引的图片
      const newValue = [...value];
      newValue.splice(index, 1);

      // 触发变化事件
      this.updateValue(newValue);
    },
    updateValue(data: string[]) {
      this.triggerEvent('change', {value: data});
    },

    /**
     * 预览图片
     */
    onPreviewImage(e: WechatMiniprogram.TouchEvent) {
      const index = parseInt(e.currentTarget.dataset.index as string);
      const { value } = this.data;

      if (index >= 0 && index < value.length) {
        wx.previewImage({
          current: value[index],
          urls: value
        });
      }
    },
  }
});
