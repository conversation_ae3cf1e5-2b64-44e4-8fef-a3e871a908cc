<!--pages/tripHelper/wallet/withdrawRecord/index.wxml-->
<view class="withdraw-record-page">
  <!-- 使用refreshLoadList组件实现上拉加载 -->
  <refreshLoadList
    enable-refresh="{{false}}"
    enable-load-more="{{withdrawRecords.length > 0}}"
    disabled="{{false}}"
    refreshing="{{refreshing}}"
    loading-more="{{loadingMore}}"
    has-more="{{hasMore}}"
    is-empty="{{!loading && withdrawRecords.length === 0}}"
    loading="{{loading}}"
    hasCustomEmpty="{{false}}"
    hasCustomLoading="{{true}}"
    statusDisplayType="data-empty"
    statusDisplayTitle="暂无提现记录"
    statusDisplayStyle="margin-top: 100rpx;"
    bind:refresh="onRefresh"
    bind:loadmore="onLoadMore"
  >
    <!-- 自定义加载状态插槽 - 骨架屏 -->
    <view slot="loading">
      <view class="skeleton-container">
        <view wx:for="{{[1, 2, 3, 4, 5]}}" wx:key="*this" class="skeleton-item">
          <view class="skeleton-left">
            <view class="skeleton-amount"></view>
            <view class="skeleton-date"></view>
          </view>
          <view class="skeleton-right">
            <view class="skeleton-status"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提现记录列表 -->
    <view class="record-list">
      <view
        class="record-item"
        wx:for="{{withdrawRecords}}"
        wx:key="withdrawalMoney"
        wx:for-index="index"
      >
        <!-- 左侧信息 -->
        <view class="record-left">
          <view class="amount">¥ {{item.withdrawalMoney}}</view>
          <view class="date">{{item.createTime}}</view>
        </view>

        <!-- 右侧状态 -->
        <view class="record-right">
          <view
            class="status status-{{item.batchStatus}}"
          >
            {{item.batchMessage}}
          </view>
        </view>
      </view>
    </view>
  </refreshLoadList>
</view>
