<wxs src="../../../utils/tool.wxs" module="tool" />
<view class="hotelListPage">
  <navbar cityName="{{cityName}}" cityId="{{cityId}}" />
  <search 
    arrivalDate="{{tool.formatDateToMonthDay(arrivalDate)}}" 
    departureDate="{{tool.formatDateToMonthDay(departureDate)}}" 
    queryText="{{queryText}}" 
    bind:search="handleSearch" 
  />
  <filter 
    id="filter-component" 
    current-sort-label="{{filterData.currentSortLabel}}" 
    price-star-count="{{filterData.priceStarCount}}" 
    distance-label="{{filterData.distanceLabel}}" 
    filter-count="{{filterData.filterCount}}" 
    bind:sortClick="onSortClick" 
    bind:priceStarClick="onPriceStarClick" 
    bind:distanceClick="onDistanceClick" 
    bind:filterClick="onFilterClick" 
  />
  <expand-popup visible="{{expandPopupVisible}}" bind:close="onExpandPopupClose">
    <!-- 智能排序组件 -->
    <rec-sort 
      wx:if="{{currentFilterType === 'sort'}}" 
      sortList="{{sortList}}" 
      currentSort="{{sort}}" 
      bind:sortSelect="onSortSelect" 
    />
    <!-- 价格/星级组件 -->
    <price-star 
      wx:if="{{currentFilterType === 'priceStar'}}" 
      priceRangeVOs="{{priceRangeVOs}}" 
      starRatingVOs="{{starRatingVOs}}" 
      bind:priceStarChange="onPriceStarChange" 
      bind:priceStarReset="onPriceStarReset" 
      bind:filterPopupClose="onFilterPopupClose" 
    />
    <!-- 位置距离组件 -->
    <area-distance 
      wx:if="{{currentFilterType === 'distance'}}" 
      areaDistance="{{areaDistance}}" 
      currentSelectedItem="{{currentSelectedDistance}}" 
      bind:distanceChange="onDistanceChange" 
      bind:distanceReset="onDistanceReset" 
      bind:filterPopupClose="onFilterPopupClose" 
    />
    <!-- 更多筛选组件 -->
    <more-filter 
      class="more-filter {{currentFilterType === 'filter' ? 'show' : 'hide'}}"
      filter-options="{{filterOptions}}" 
      selected-filters="{{filterFilters || []}}"
      bind:filterChange="onFilterChange" 
      bind:moreFilterReset="onMoreFilterReset" 
      bind:filterPopupClose="onFilterPopupClose" 
    />
  </expand-popup>
  <quick-filter />
  <scroll-view 
    class="hotelList-content" 
    scroll-y="{{true}}" 
    refresher-enabled="{{true}}" 
    refresher-triggered="{{refresherTriggered}}" 
    refresher-default-style="none" 
    refresher-background="#f5f5f5" 
    bindrefresherrefresh="onRefresherRefresh" 
    bindrefresherpulling="onRefresherPulling"
    bindrefresherrestore="onRefresherRestore"
    bindscrolltolower="onScrollToLower" 
    lower-threshold="{{50}}"
  >
    <!-- 自定义下拉刷新内容 -->
    <view slot="refresher" class="custom-refresher">
      <t-loading wx:if="{{refreshing}}" theme="circular" size="40rpx" text="正在刷新..." />
      <image 
        wx:else 
        class="plane-icon {{refreshing ? 'refreshing' : 'pulling'}}" 
        src="{{imgBaseUrl}}/hoteList/planeIcon.png"
        style="transform: scale({{pullDownScale}})"
      />
    </view>
    <!-- 酒店列表 -->
    <hotel-item 
      wx:for="{{hotels}}" 
      wx:key="hotelId" 
      hotelData="{{item}}" 
      bind:hotelClick="handleHotelClick"
    ></hotel-item>
    <!-- 上拉加载更多状态 -->
    <view class="load-more" wx:if="{{loadingMore}}">
      <t-loading theme="circular" size="40rpx" text="加载中..." />
    </view>
    <!-- 没有更多数据提示 -->
    <view class="load-more" wx:elif="{{!hasMore && hotels.length > 0}}">
      <text>没有内容了</text>
    </view>
  </scroll-view>
</view>