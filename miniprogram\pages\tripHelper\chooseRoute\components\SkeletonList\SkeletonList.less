/* 骨架屏列表样式 */
.skeleton-list {
  width: 100%;
  margin: 16rpx 24rpx;
  
  .skeleton-item {
    width: 702rpx;
    height: 376rpx;
    background-color: #ffffff;
    border-radius: 32rpx;
    margin-bottom: 16rpx;
    padding: 32rpx;
    box-sizing: border-box;
    
    .skeleton-card {
      width: 100%;
      height: 100%;
      
      .skeleton-content {
        width: 636rpx;
        height: 312rpx;
        display: flex;
        align-items: center;
        
        .skeleton-icon {
          width: 48rpx;
          height: 48rpx;
          background-color: #f3f3f3;
          border-radius: 8rpx;
          flex-shrink: 0;
          animation: skeleton-loading 1.5s infinite ease-in-out;
        }
        
        .skeleton-main {
          width: 564rpx;
          height: 312rpx;
          margin-left: 24rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          
          .skeleton-header {
            width: 564rpx;
            height: 44rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .skeleton-title {
              width: 228rpx;
              height: 44rpx;
              background-color: #f3f3f3;
              border-radius: 4rpx;
              animation: skeleton-loading 1.5s infinite ease-in-out;
            }
            
            .skeleton-favorite {
              width: 28rpx;
              height: 28rpx;
              background-color: #f3f3f3;
              border-radius: 4rpx;
              animation: skeleton-loading 1.5s infinite ease-in-out;
            }
          }
          
          .skeleton-images {
            width: 564rpx;
            height: 180rpx;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-top: 24rpx;
            
            .skeleton-image {
              width: 180rpx;
              height: 180rpx;
              background-color: #f3f3f3;
              border-radius: 24rpx;
              animation: skeleton-loading 1.5s infinite ease-in-out;
              
              &:not(:first-child) {
                margin-left: 12rpx;
              }
            }
          }
          
          .skeleton-footer {
            width: 440rpx;
            height: 40rpx;
            background-color: #f3f3f3;
            border-radius: 4rpx;
            margin-top: 24rpx;
            animation: skeleton-loading 1.5s infinite ease-in-out;
          }
        }
      }
    }
  }
  
  .skeleton-item-small {
    width: 702rpx;
    height: 24rpx;
    background-color: #ffffff;
    border-radius: 32rpx;
    padding: 32rpx;
    box-sizing: border-box;
    animation: skeleton-loading 1.5s infinite ease-in-out;
  }
}

/* 骨架屏加载动画 */
@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
} 