/* pages/hotel/hotelList/components/PriceStar/PriceStar.wxss */

.price-star-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
}

/* 为底部按钮留出空间 */
.content {
  padding-bottom: 140rpx; /* 底部按钮高度约120rpx + padding */
}

/* 底部操作按钮样式 */
.bottom-actions {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24rpx;
  z-index: 1000;
}

.reset-btn {
  flex: 1;
  height: 104rpx;
  background: #F3F3F3;
  color: #11111E;
  font-size: 32rpx;
  border: none;
  border-radius: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::after {
    border: none;
  }
  
  &:active {
    background-color: #ebebeb;
  }
}

.confirm-btn {
  flex: 1;
  height: 104rpx;
  background-color: #568DED;
  color: #fff;
  font-size: 32rpx;
  border-radius: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::after {
    border: none;
  }
  
  &:active {
    background-color: #4a7bd9;
  }
}