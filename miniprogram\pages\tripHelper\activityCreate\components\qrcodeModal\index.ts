// pages/tripHelper/activityCreate/components/linkRecognition/index.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    url: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imageUrl: ''
  },
  observers: {
    visible() {
      if (this.data.visible) {
        this.setData({
          imageUrl: this.data.url
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.triggerEvent('update-visible', e.detail);
    },
    handleSubmit() {
      if (!this.data.imageUrl) {
        wx.displayToast({
          title: '二维码图片不能为空',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      this.triggerEvent('update-visible', { visible: false });
      this.triggerEvent('change', {
        value: this.data.imageUrl
      });
    },
    handleClose() {
      this.triggerEvent('update-visible', { visible: false });
    },
    handleImageChange(e: WechatMiniprogram.CustomEvent<{value: string[]}>) {
      if (e.detail.value?.length) {
        this.setData({
          imageUrl: e.detail.value[0]
        })
      }
    }
  }
})