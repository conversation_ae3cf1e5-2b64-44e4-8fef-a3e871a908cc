// pages/login/index.ts
import { getImageUrl } from '../../utils/images';
import { LoginManager } from '../../utils/loginManager';

const H5BaseUrl = 'https://webapp.test.aitrip123.com/agreements';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 默认头像
    avatarDefaultIcon: getImageUrl('user/avatar_default.png'),
    // 协议是否勾选
    agreementChecked: false,
    // 当前微信code（用于手机号授权后的二次登录）
    currentWxCode: '',
    // 登录成功后要跳转的页面URL
    backUrl: '',
    // 是否是token过期导致的登录
    isTokenExpired: false,
    // 用户信息弹框显示状态
    showUserInfoModal: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    console.log('登录页面加载', options);

    // 获取backUrl参数
    if (options.backUrl) {
      this.setData({
        backUrl: decodeURIComponent(options.backUrl)
      });
    }

    // 获取tokenExpired参数
    if (options.tokenExpired === '1') {
      this.setData({
        isTokenExpired: true
      });
    }
  },

  /**
   * 协议勾选状态变化
   */
  onAgreementChange(e: WechatMiniprogram.CustomEvent<{ value: boolean }>) {
    const { value } = e.detail;
    this.setData({
      agreementChecked: value
    });
  },

  /**
   * 点击用户协议
   */
  onUserAgreementClick() {
    const fullUrl = `${H5BaseUrl}/user`;
    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent('用户服务协议')}`
    });
  },

  /**
   * 点击隐私协议
   */
  onPrivacyAgreementClick() {
    const fullUrl = `${H5BaseUrl}/protect`;
    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent('隐私协议')}`
    });
  },



  /**
   * 点击登录按钮（协议未勾选时的提示）
   */
  onLoginClick() {
    // 协议未勾选时的提示
    wx.showToast({
      title: '请先勾选用户协议和隐私协议',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 微信原生手机号授权回调
   */
  async onGetPhoneNumber(e: any) {
    console.log('手机号授权结果:', e.detail);

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      try {
        // 授权成功，同时获取微信code
        const wxCode = await LoginManager.getWxCode();

        // 保存微信code
        this.setData({
          currentWxCode: wxCode
        });

        // 调用登录接口，同时传入微信code和手机号code
        this.performLogin(e.detail.code);
      } catch (error) {
        console.error('获取微信code失败:', error);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } else {
      // 授权失败
      console.log('用户取消手机号授权');
      wx.showToast({
        title: '需要手机号授权才能登录',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 执行登录
   */
  async performLogin(phoneCode?: string) {
    try {
      let wxCode = this.data.currentWxCode;

      // 如果没有保存的微信code，或者是第一次调用，重新获取
      if (!wxCode || !phoneCode) {
        wxCode = await LoginManager.getWxCode();
        // 保存微信code，用于可能的二次调用
        this.setData({
          currentWxCode: wxCode
        });
      }

      // 调用登录接口
      const result = await LoginManager.login(wxCode, phoneCode);

      console.log('登录成功:', result);

      // 处理登录结果
      this.handleLoginResult(result);

    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 处理登录结果
   */
  handleLoginResult(result: Login.LoginResponse) {
    // 检查是否需要完善用户信息
    if (result.needProfileComplete) {
      // 需要完善用户信息，在登录页面直接显示用户信息弹框
      this.setData({
        showUserInfoModal: true
      });
    } else {
      // 直接跳转页面
      this.navigateAfterLogin();
    }
  },



  /**
   * 登录成功后的页面跳转
   */
  navigateAfterLogin() {
    const { backUrl } = this.data;
    const homeUrl = '/pages/home/<USER>/index';

    if (backUrl) {
      // 有backUrl，跳转到目标页面
      wx.redirectTo({
        url: backUrl,
        fail: () => {
          // 跳转失败，跳转到首页
          wx.redirectTo({ url: homeUrl });
        }
      });
    } else {
      // 没有backUrl，返回上一页
      wx.navigateBack({
        fail: () => {
          // 如果没有上一页，跳转到首页
          wx.redirectTo({ url: homeUrl });
        }
      });
    }
  },

  /**
   * 用户信息弹框关闭事件
   */
  onUserInfoModalClose() {
    this.setData({
      showUserInfoModal: false
    });

    // 用户取消填写信息，直接跳转页面
    this.navigateAfterLogin();
  },

  /**
   * 用户信息提交成功事件
   */
  onUserInfoModalSuccess(e: any) {
    const userInfo = e.detail;
    console.log('用户信息提交成功:', userInfo);

    // 关闭弹框
    this.setData({
      showUserInfoModal: false
    });

    wx.showToast({
      title: '个人信息保存成功',
      icon: 'success',
      duration: 2000
    });

    // 用户信息提交成功后跳转页面
    this.navigateAfterLogin();
  }
});
