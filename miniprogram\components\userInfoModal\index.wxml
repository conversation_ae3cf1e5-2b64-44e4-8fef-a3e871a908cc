<!-- 用户信息填写弹框 -->
<t-popup
  visible="{{visible}}"
  placement="bottom"
  bind:visible-change="onVisibleChange"
  bind:overlay-click="onClose"
  custom-style="border-radius: 0; "
>
  <view class="user-info-content">
    <!-- 头部 -->
    <view class="modal-header">
      <text class="modal-title">填写个人信息</text>
      <view class="close-btn" bind:tap="onClose">
        <image src="{{closeIcon}}" mode="aspectFit" class="close-icon"></image>
      </view>
    </view>

    <!-- 头像上传 -->
    <view class="avatar-section">
      <text class="section-title">上传一张帅气的头像</text>
      <view class="avatar-container">
        <!-- 有头像时显示头像和删除按钮 -->
        <view wx:if="{{avatarUrl}}" class="avatar-display">
          <image
            src="{{avatarUrl}}"
            mode="aspectFill"
            class="avatar-image"
          ></image>
          <!-- 删除头像按钮 -->
          <view class="avatar-delete" bind:tap="onDeleteAvatar">
            <image src="{{deleteIcon}}" mode="aspectFit" class="delete-icon"></image>
          </view>
        </view>
        <!-- 没有头像时显示上传按钮 -->
        <button
          wx:else
          class="avatar-upload"
          open-type="chooseAvatar"
          bind:chooseavatar="onChooseAvatar"
        >
          <view class="avatar-placeholder">
            <image
              src="{{avatarPicIcon}}"
              class="upload-icon"
            ></image>
            <text class="upload-text">上传图片</text>
          </view>
        </button>
      </view>
    </view>

    <!-- 昵称输入 -->
    <view class="nickname-section">
      <text class="section-title">取个响亮的名字</text>
      <input
        class="nickname-input"
        placeholder="填写昵称"
        value="{{nickname}}"
        bind:input="onNicknameInput"
        maxlength="20"
      />
    </view>

    <!-- 性别选择 -->
    <view class="gender-section">
      <text class="section-title">你的性别</text>
      <view class="gender-options">
        <view
          class="gender-option {{gender === '2' ? 'selected' : ''}}"
          bind:tap="onGenderSelect"
          data-gender="female"
        >
          <image
            src="{{gender === '2' ? femaleActiveIcon : femaleIcon}}"
            mode="aspectFit"
            class="gender-icon"
          ></image>
          <text class="gender-text">女生</text>
        </view>
        <view
          class="gender-option {{gender === '1' ? 'selected' : ''}}"
          bind:tap="onGenderSelect"
          data-gender="male"
        >
          <image
            src="{{gender === '1' ? maleActiveIcon : maleIcon}}"
            mode="aspectFit"
            class="gender-icon"
          ></image>
          <text class="gender-text">男生</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <t-button
        theme="primary"
        size="large"
        bind:tap="onSubmit"
        loading="{{submitting}}"
        class="submit-btn"
      >
        提交
      </t-button>
    </view>
  </view>
</t-popup>
