/**
 * 通用的下拉刷新和上拉加载列表组件
 * 支持自定义loading样式、状态文案、触发距离等
 */

// 组件属性接口
export interface IRefreshLoadListProps {
  // === 基础配置 ===
  /** 是否启用下拉刷新 */
  enableRefresh?: boolean;
  /** 是否启用上拉加载 */
  enableLoadMore?: boolean;
  /** 上拉加载触发距离(rpx) */
  loadMoreDistance?: number;
  /** 是否使用页面级滚动（整体滚动模式） */
  usePageScroll?: boolean;
  
  // === 状态控制 ===
  /** 是否正在刷新 */
  refreshing?: boolean;
  /** 是否正在加载更多 */
  loadingMore?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 是否为空状态 */
  isEmpty?: boolean;
  /** 是否初始加载中 */
  loading?: boolean;
  
  // === 文案配置 ===
  /** 下拉刷新文案 */
  refreshText?: string;
  /** 上拉加载文案 */
  loadMoreText?: string;
  /** 无更多数据文案 */
  noMoreText?: string;
  /** 上拉提示文案 */
  pullTipText?: string;
  /** 初始加载文案 */
  loadingText?: string;
  /** 空状态文案 */
  emptyText?: string;
  /** 空状态描述 */
  emptyDesc?: string;
  
  // === 样式配置 ===
  /** 自定义样式类名 */
  customClass?: string;
  /** loading图标大小 */
  loadingSize?: string;
  /** loading图标颜色 */
  loadingColor?: string;
  /** 空状态图标 */
  emptyIcon?: string;
  /** 空状态图标大小 */
  emptyIconSize?: string;
  /** 空状态图标颜色 */
  emptyIconColor?: string;
  /** 距离顶部距离(rpx)，用于适配固定顶部元素如tabs */
  topOffset?: string;

  // === status-display 相关配置 ===
  /** status-display 组件类型 */
  statusDisplayType?: string;
  /** status-display 组件标题 */
  statusDisplayTitle?: string;
  /** status-display 组件自定义样式 */
  statusDisplayStyle?: string;
  
  // === 高级配置 ===
  /** 是否禁用所有加载功能(如编辑模式) */
  disabled?: boolean;
  /** 刷新延迟时间(ms) */
  refreshDelay?: number;
  /** 防抖时间(ms) */
  debounceTime?: number;
  /** 是否使用自定义空状态 */
  hasCustomEmpty?: boolean;
  /** 是否使用自定义加载状态 */
  hasCustomLoading?: boolean;
}

// 组件事件接口
export interface IRefreshLoadListEvents {
  /** 下拉刷新事件 */
  onRefresh?: () => void | Promise<void>;
  /** 上拉加载事件 */
  onLoadMore?: () => void | Promise<void>;
}

Component({
  options: {
    multipleSlots: true,
    virtualHost: true
  },

  properties: {
    // === 基础配置 ===
    enableRefresh: {
      type: Boolean,
      value: true
    },
    enableLoadMore: {
      type: Boolean,
      value: true
    },
    loadMoreDistance: {
      type: Number,
      value: 50
    },
    usePageScroll: {
      type: Boolean,
      value: false
    },

    // === 状态控制 ===
    refreshing: {
      type: Boolean,
      value: false
    },
    loadingMore: {
      type: Boolean,
      value: false
    },
    hasMore: {
      type: Boolean,
      value: true
    },
    isEmpty: {
      type: Boolean,
      value: false
    },
    loading: {
      type: Boolean,
      value: false
    },

    // === 文案配置 ===
    refreshText: {
      type: String,
      value: '正在刷新...'
    },
    loadMoreText: {
      type: String,
      value: '正在加载更多...'
    },
    noMoreText: {
      type: String,
      value: ''
    },
    pullTipText: {
      type: String,
      value: '上拉加载更多'
    },
    loadingText: {
      type: String,
      value: '加载中...'
    },
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    emptyDesc: {
      type: String,
      value: '暂时没有相关数据'
    },

    // === 样式配置 ===
    customClass: {
      type: String,
      value: ''
    },
    loadingSize: {
      type: String,
      value: '40rpx'
    },
    loadingColor: {
      type: String,
      value: '#999'
    },
    emptyIcon: {
      type: String,
      value: 'info-circle'
    },
    emptyIconSize: {
      type: String,
      value: '80rpx'
    },
    emptyIconColor: {
      type: String,
      value: '#E0E0E0'
    },
    topOffset: {
      type: String,
      value: '0rpx'
    },

    // === 高级配置 ===
    disabled: {
      type: Boolean,
      value: false
    },
    refreshDelay: {
      type: Number,
      value: 500
    },
    debounceTime: {
      type: Number,
      value: 800
    },
    hasCustomEmpty: {
      type: Boolean,
      value: false
    },

    // === status-display 相关属性 ===
    statusDisplayType: {
      type: String,
      value: 'data-empty'
    },
    statusDisplayTitle: {
      type: String,
      value: '暂无数据'
    },
    statusDisplayStyle: {
      type: String,
      value: 'margin-top: 100rpx;'
    },
    hasCustomLoading: {
      type: Boolean,
      value: false
    }
  },

  data: {
    lastLoadMoreTime: 0, // 上次上拉加载时间，用于防抖
    // 下拉刷新相关状态
    pullDistance: 0, // 下拉距离
    refreshThreshold: 80, // 触发刷新的阈值(rpx)
    startY: 0, // 触摸开始Y坐标
    isPulling: false, // 是否正在下拉
    canPull: false, // 是否可以下拉（页面在顶部时才能下拉）
    // 滚动位置相关
    scrollTop: 0, // 当前滚动位置
    scrollHeight: 0, // 内容总高度
    viewHeight: 0, // 可视区域高度
    // 页面级滚动相关
    originalOnReachBottom: null as (() => void) | null, // 保存原始的onReachBottom方法

    // 图标的base64数据（避免依赖外部字体文件）
    arrowDownIconBase64: 'PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTMuNjY2N0wzLjMzMzMzIDlIOC42NjY2N0w4IDEzLjY2NjdaIiBmaWxsPSIjOTk5Ii8+Cjwvc3ZnPgo=',
    refreshIconBase64: 'PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMi4zMzMzM0wxMi42NjY3IDdINy4zMzMzM0w4IDIuMzMzMzNaIiBmaWxsPSIjOTk5Ii8+CjxwYXRoIGQ9Ik04IDEzLjY2NjdMMy4zMzMzMyA5SDguNjY2NjdMOCAxMy42NjY3WiIgZmlsbD0iIzk5OSIvPgo8L3N2Zz4K'
  },

  lifetimes: {
    attached() {
      // 如果使用页面级滚动，需要监听页面的onReachBottom事件
      if (this.data.usePageScroll) {
        this.setupPageScrollListener();
      }
    },

    detached() {
      // 清理页面级滚动监听
      if (this.data.usePageScroll) {
        this.cleanupPageScrollListener();
      }
    }
  },

  methods: {
    /**
     * 设置页面级滚动监听
     */
    setupPageScrollListener() {
      // 获取页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (currentPage) {
        // 保存原始的onReachBottom方法
        this.setData({
          originalOnReachBottom: currentPage.onReachBottom
        });

        // 重写页面的onReachBottom方法
        currentPage.onReachBottom = () => {
          // 先调用原始方法
          if (this.data.originalOnReachBottom) {
            this.data.originalOnReachBottom.call(currentPage);
          }

          // 然后处理组件的上拉加载
          this.handlePageReachBottom();
        };
      }
    },

    /**
     * 清理页面级滚动监听
     */
    cleanupPageScrollListener() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (currentPage && this.data.originalOnReachBottom) {
        // 恢复原始的onReachBottom方法
        currentPage.onReachBottom = this.data.originalOnReachBottom;
        this.setData({
          originalOnReachBottom: null
        });
      }
    },

    /**
     * 处理页面级的onReachBottom事件
     */
    handlePageReachBottom() {
      if (!this.data.enableLoadMore || this.data.loadingMore || !this.data.hasMore || this.data.disabled) {
        return;
      }

      // 触发上拉加载事件
      this.triggerEvent('loadmore');
    },
    /**
     * 获取页面滚动位置
     */
    getPageScrollTop() {
      return new Promise<number>((resolve) => {
        // 获取页面滚动位置
        wx.createSelectorQuery().selectViewport().scrollOffset((res) => {
          const scrollTop = res ? res.scrollTop : 0;
          console.log('Page ScrollTop:', scrollTop);
          resolve(scrollTop);
        }).exec();
      });
    },



    /**
     * 触摸开始
     */
    async onTouchStart(e: WechatMiniprogram.TouchEvent) {
      if (this.data.disabled || !this.data.enableRefresh || this.data.refreshing) {
        return;
      }

      const touch = e.touches[0];

      // 获取页面滚动位置
      const currentScrollTop = await this.getPageScrollTop();
      const canPull = currentScrollTop <= 10; // 允许10px的误差

      this.setData({
        startY: touch.clientY,
        isPulling: false,
        canPull,
        scrollTop: currentScrollTop
      });

      console.log('TouchStart:', {
        currentScrollTop,
        canPull
      });
    },

    /**
     * 触摸移动
     */
    async onTouchMove(e: WechatMiniprogram.TouchEvent) {
      if (this.data.disabled || !this.data.enableRefresh || this.data.refreshing || !this.data.canPull) {
        return;
      }

      const touch = e.touches[0];
      const deltaY = touch.clientY - this.data.startY;

      // 向下拉动时，实时检测滚动位置
      if (deltaY > 30) {
        const currentScrollTop = await this.getPageScrollTop();

        console.log('TouchMove:', {
          deltaY,
          currentScrollTop,
          canPull: this.data.canPull
        });

        // 只有在页面顶部时才处理
        if (currentScrollTop <= 10) {
          this.setData({
            isPulling: true,
            pullDistance: Math.min((deltaY - 30) * 0.4, 100),
            scrollTop: currentScrollTop
          });
        } else {
          // 不在顶部时重置状态
          this.resetPullState();
        }
      } else if (deltaY <= 0) {
        // 向上滑动时重置状态
        this.resetPullState();
      }
    },

    /**
     * 触摸结束
     */
    onTouchEnd() {
      if (this.data.disabled || !this.data.enableRefresh) {
        this.resetPullState();
        return;
      }

      const { pullDistance, refreshThreshold, isPulling, canPull } = this.data;

      // 只有在允许下拉且正在下拉且达到阈值时才触发刷新
      if (canPull && isPulling && pullDistance >= refreshThreshold) {
        // 触发刷新
        this.setData({
          refreshing: true,
          pullDistance: refreshThreshold
        });
        console.log('RefreshLoadList: 下拉刷新触发');
        this.triggerEvent('refresh');
      } else {
        // 回弹
        this.resetPullState();
      }
    },

    /**
     * 重置下拉状态
     */
    resetPullState() {
      this.setData({
        pullDistance: 0,
        isPulling: false,
        canPull: false
      });
    },

    /**
     * 下拉刷新触发（保留原有方法以兼容）
     */
    onPullDownRefresh() {
      if (this.data.disabled || !this.data.enableRefresh) {
        wx.stopPullDownRefresh();
        return;
      }

      console.log('RefreshLoadList: 系统下拉刷新触发');
      this.triggerEvent('refresh');
    },



    /**
     * 停止下拉刷新
     */
    stopRefresh() {
      if (this.data.refreshDelay > 0) {
        setTimeout(() => {
          wx.stopPullDownRefresh();
        }, this.data.refreshDelay);
      } else {
        wx.stopPullDownRefresh();
      }
    },



    /**
     * 滚动到底部触发加载更多
     */
    onScrollToLower() {
      if (this.data.disabled || !this.data.enableLoadMore || this.data.loadingMore || !this.data.hasMore) {
        return;
      }

      this.triggerLoadMore();
    },

    /**
     * 触发加载更多
     */
    triggerLoadMore() {
      const now = Date.now();

      // 防抖处理
      if (now - this.data.lastLoadMoreTime < this.data.debounceTime) {
        return;
      }

      this.setData({
        lastLoadMoreTime: now
      });

      console.log('RefreshLoadList: 上拉加载更多触发');
      this.triggerEvent('loadmore');
    },

    /**
     * 滚动事件处理
     */
    onScroll(e: WechatMiniprogram.CustomEvent<{ scrollTop: number; scrollLeft: number; scrollHeight: number; scrollWidth: number; deltaX: number; deltaY: number }>) {
      // 将滚动事件传递给父组件
      this.triggerEvent('scroll', e.detail);
    },

    /**
     * status-display 组件按钮点击事件
     */
    onStatusDisplayButtonClick(e: WechatMiniprogram.CustomEvent) {
      console.log('RefreshLoadList: status-display 按钮点击', e.detail);
      this.triggerEvent('statusbuttonclick', e.detail);
    }
  },

  observers: {
    'refreshing': function(refreshing: boolean) {
      if (!refreshing && this.data.enableRefresh) {
        this.stopRefresh();
        // 重置下拉状态
        setTimeout(() => {
          this.resetPullState();
        }, 300); // 延迟重置，让动画更自然
      }
    }
  }
});
