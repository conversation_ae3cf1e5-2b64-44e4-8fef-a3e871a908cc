import appConfig from '../../../../../config/app.config';
import dayjs from 'dayjs';
import { AgeGroupDisplay, StorageKeyEnum, AgeGroupEnum } from '../../../../../enum/index';
import { getPolicyList, createActivity, updateActivity } from '../../../../../api/tripHelper/activity';
import { getDetailApi } from '../../../../../api/tripHelper/activityDetail';

const getDefaultData: () => Activity.ICreateUpdateActivityPayload = () => ({
  title: '',
  description: '',
  coverImages: [],
  startTime: '',
  endTime: '',
  activityType: null,
  routeId: null,
  cancelPolicyId: null,
  allowWaiting: 0,
  wxImage: '',
  assemblyPlace: [],
  perCost: [],
  peopleCount: null,
  deadlineTime: '',
  grouponCount: null,
})

interface IStorageInfo {
  /** 数据有效日期 */
  dateTime: string;
  formData: Activity.ICreateUpdateActivityPayload,
  activeTab: number
}

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    projectId: {
      // @ts-ignore-next-line
      type: Number,
      value: 0
    },
    activeStep: {
      type: Number,
      value: 1
    },
    activityId: {
      type: Number,
      value: 0
    }
  },
  observers: {
    projectId() {
      const projectId = this.data.projectId;
      if (projectId && this.data.formModel.activityType !== this.data.projectId) {
        this.setData({
          'formModel.activityType': projectId,
          'formModel.title': '',
          'formModel.description': '',
          'formModel.routeId': null,
          activeTab: 1
        });
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: false,
    activeTab: 1,
    AgeGroupDisplay,
    nowTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    staticBaseUrl: appConfig.staticBaseUrl,
    showNumberPeoplePicker: false,
    formModel: getDefaultData(),
    showDeadlineTime: false,
    showPriceModal: false,
    showQrcodeModal: false,
    showDateRange: false,
    showCancelPolicyModal: false,
    policyList: [] as Activity.IPolicy[]
  },

  lifetimes: {
    ready() {
      this.handleCacheData();
      getPolicyList().then(res => {
        if (res.isSuccess) {
          this.setData({
            policyList: res.data ?? []
          })  
        }
      });
      
      getDetailApi({ id: this.data.activityId }).then(res => {
        if (res.isSuccess) {
          console.log(res);
          
        }
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /** 处理缓存数据 */
    handleCacheData() {
      const data = wx.getStorageSync<Nullable<IStorageInfo>>(StorageKeyEnum.ActivityData);
      const today = dayjs().format('YYYY-MM-DD');
      if (!data) return;

      if (data.formData && today === data.dateTime) {
        this.triggerEvent('update-project', {
          value: data.formData.activityType
        });
        this.setData({
          formModel: data.formData,
          activeTab: data.activeTab
        });
        this.triggerEvent('tab-change', {
          value: 2
        });
      } else {
        wx.removeStorageSync(StorageKeyEnum.ActivityData);
      }
    },
    /** 更新activeTab */
    handleUpdateActiveTab(e: WechatMiniprogram.CustomEvent<{value: number}>) {
      this.setData({
        activeTab: e.detail.value
      })
    },
    /** 跳转集合地点页 */
    handleJumpMeetingPoint() {
      const { startTime, endTime } = this.data.formModel;
      if (!startTime) {
        wx.showToast({
          title: '请先选择活动日期',
          icon: 'none',
          duration: 3000
        })
        return;
      }
      wx.navigateTo({
        url: '/pages/tripHelper/mettingPoint/index',
        events: {
          // 定义监听的事件
          'updatePOIList': (data: { value: Activity.IPlace[] }) => {
            this.setData({
              'formModel.assemblyPlace': data.value
            })
          }
        },
        success: (res) => {
          // 通过事件通道发送数据
          res.eventChannel.emit('getPOIList', {
            value: this.data.formModel.assemblyPlace,
            minDate: startTime,
            maxDate: endTime
          });
        }
      })
    },
    handlePlaceRemove(e: WechatMiniprogram.TouchEvent<{}, {}, { index: number }>) {
      const list = this.data.formModel.assemblyPlace;
      list.splice(e.currentTarget.dataset.index, 1);
      this.setData({
        'formModel.assemblyPlace': list
      })
    },
    /** 打开人数选择 */
    handleOpenNumberPeoplePicker() {
      this.setData({
        showNumberPeoplePicker: true
      })
    },
    handleNumberPickerChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.setData({
        showNumberPeoplePicker: e.detail.visible
      })
    },
    /** 截止报名时间变更 */
    handleDeadlineTimeChange(e: WechatMiniprogram.CustomEvent<{ value: string }>) {
      this.setData({
        'formModel.deadlineTime': e.detail.value
      })
    },
    handleOpenDeadlineTimePicker() {
      this.setData({
        showDeadlineTime: true
      })
    },
    handleCloseDeadlineTimePicker() {
      this.setData({
        showDeadlineTime: false
      })
    },
    handlePriceModalChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.setData({
        showPriceModal: e.detail.visible
      })
    },
    handleOpenPriceModal() {
      this.setData({
        showPriceModal: true
      })
    },
    handleQrcodeModalChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.setData({
        showQrcodeModal: e.detail.visible
      })
    },
    handleOpenQrcodeModal() {
      this.setData({
        showQrcodeModal: true
      })
    },
    handleAllowWaitingChange(e: WechatMiniprogram.CustomEvent<{ value: number }>) {
      this.setData({
        'formModel.allowWaiting': e.detail.value
      })
    },
    /** 活动人数变更 */
    handleNumberPeopleChange(e: WechatMiniprogram.CustomEvent<{ min: number, max: number }>) {
      this.setData({
        'formModel.peopleCount': e.detail.max,
        'formModel.grouponCount': e.detail.min
      })
    },
    /** 路线数据更新 */
    handleRouteLineChange(e: WechatMiniprogram.CustomEvent<{ [key in keyof Activity.ICreateUpdateActivityPayload]: Activity.ICreateUpdateActivityPayload[key] }>) {
      const formModel = this.data.formModel;
      this.setData({
        formModel: {
          ...formModel,
          ...e.detail
        }
      })
    },
    /** 二维码数据更新 */
    handleQrcodeValueChange(e: WechatMiniprogram.CustomEvent<{ value: string }>) {
      this.setData({
        'formModel.wxImage': e.detail.value
      })
    },
    /** 价格数据更新 */
    handlePriceChange(e: WechatMiniprogram.CustomEvent<{ value: Activity.ICreateUpdateActivityPayload['perCost'] }>) {
      this.setData({
        'formModel.perCost': e.detail.value ?? []
      })
    },
    /** 打开活动日期选择 */
    onDateRangeOpen() {
      this.setData({
        showDateRange: true
      })
    },
    /** 活动日期弹窗切换 */
    onDateRangeVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.setData({
        showDateRange: e.detail.visible
      })
    },
    /** 更新活动时间 */
    onDateRangeChange(e: WechatMiniprogram.CustomEvent<{ start: string; end: string; }>) {
      const { start, end } = e.detail;
      const { deadlineTime, assemblyPlace } = this.data.formModel;
      // check集合地点日期&报名截止时间是否符合新的活动日期
      const list = assemblyPlace.filter(item => item.startTime >= `${start} 00:00:00` && item.startTime <= `${end} 23:59:59`);
      this.setData({
        'formModel.startTime': e.detail.start,
        'formModel.endTime': e.detail.end,
        'formModel.deadlineTime': deadlineTime > `${start} 00:00:00` ? '' : deadlineTime,
        'formModel.assemblyPlace': list
      });
    },
    /** 取消政策弹窗切换 */
    handleCancelPolicyModalToggle(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.setData({
        showCancelPolicyModal: e.detail.visible
      })
    },
    handleOpenCancelPolicyModal() {
      this.setData({
        showCancelPolicyModal: true
      })
    },
    /** 取消政策变更 */
    handleCancelPolicyChange(e: WechatMiniprogram.CustomEvent<{ value: number }>) {
      this.setData({
        'formModel.cancelPolicyId': e.detail.value
      })
    },
    /** 保存草稿 */
    handleSaveDraft() {
      wx.setStorage<IStorageInfo>({
        key: StorageKeyEnum.ActivityData,
        data: {
          dateTime: dayjs().format('YYYY-MM-DD'),
          activeTab: this.data.activeTab,
          formData: {
            ...this.data.formModel
          }
        }
      });
      wx.showToast({
        title: '保存草稿成功',
        icon: 'none',
        duration: 3000
      });
    },
    validate() {
      const { activeTab } = this.data;
      const { routeId, coverImages, title, description, startTime, endTime, assemblyPlace, peopleCount, perCost, cancelPolicyId, deadlineTime } = this.data.formModel;
      if (activeTab === 1 && !routeId) return '线路不能为空，请选择线路';

      if (activeTab === 2) {
        if (!coverImages.length) return '活动图片不能为空';
        if (!title) return '活动标题不能为空';
        if (!description) return '活动介绍不能为空';
      }
      if (!startTime || !endTime) return '活动时间不能为空';
      if (!assemblyPlace.length) return '集合地点不能为空';
      if (!peopleCount) return '活动人数不能为空';
      if (!perCost.find(item => item.type === AgeGroupEnum.ADULT)) return '活动价格不能为空';
      if (!cancelPolicyId) return '取消政策不能为空';
      if (!deadlineTime) return '报名截止时间不能为空';
      return null;
    },
    /** 提交 */
    async handleSubmit() {
      const errorMessage = this.validate();
      if (errorMessage) {
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
        return;
      }
      this.setData({
        loading: true
      });
      const formData = {...this.data.formModel};
      if (this.data.activeTab === 2) {
        formData.routeId = null
      } else {
        Object.assign(formData, {
          title: '',
          description: '',
          coverImages: []
        })
      }
      let fn = createActivity
      if (this.data.activityId) {
        Object.assign(formData, {
          id: this.data.activityId
        })
        fn = updateActivity
      }
      const res = await fn(formData);
      this.setData({
        loading: false
      });
      if (res.isSuccess) {
        wx.navigateTo({
          url: `/pages/tripHelper/activityDetail/index?id=${this.data.activityId || res.data}&isShowShareCard=1`
        });
      }
    }
  }
})