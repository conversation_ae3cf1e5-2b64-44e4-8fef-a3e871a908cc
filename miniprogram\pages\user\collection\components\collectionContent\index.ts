/**
 * 收藏内容展示组件
 * 用于展示收藏列表内容，支持编辑模式和滑动删除
 */
import { behavior as computedBehavior } from "miniprogram-computed";

/**
 * 组件属性接口定义
 */
interface CollectionContentProps extends WechatMiniprogram.Component.PropertyOption {
  /** 当前列表数据 */
  currentList: WechatMiniprogram.Component.FullProperty<ArrayConstructor>;
  /** 是否为编辑模式 */
  isEditMode: WechatMiniprogram.Component.FullProperty<BooleanConstructor>;
  /** 当前滑动项索引 */
  currentSwipeIndex: WechatMiniprogram.Component.FullProperty<NumberConstructor>;
  /** 未选中图标 */
  circleIcon: WechatMiniprogram.Component.FullProperty<StringConstructor>;
  /** 选中图标 */
  circleActIcon: WechatMiniprogram.Component.FullProperty<StringConstructor>;
  /** 删除图标 */
  deleteIcon: WechatMiniprogram.Component.FullProperty<StringConstructor>;
}

/**
 * 组件数据接口定义
 */
interface CollectionContentData {
  /** 触摸开始位置 */
  touchStartX: number;
  /** 触摸开始时间 */
  touchStartTime: number;
}

/**
 * 组件方法接口定义
 */
interface CollectionContentMethods {
  /** 触摸开始事件 */
  onTouchStart(e: WechatMiniprogram.TouchEvent): void;
  /** 触摸移动事件 */
  onTouchMove(): void;
  /** 触摸结束事件 */
  onTouchEnd(e: WechatMiniprogram.TouchEvent): void;
  /** 选择项目事件 */
  onSelectItem(e: WechatMiniprogram.BaseEvent): void;
  /** 删除项目事件 */
  onDeleteItem(e: WechatMiniprogram.BaseEvent): void;
  /** 点击项目事件 */
  onItemClick(e: WechatMiniprogram.BaseEvent): void;
  /** 酒店点击事件 */
  onHotelClick(e: WechatMiniprogram.BaseEvent): void;
  /** 酒店卡片点击事件 */
  onHotelCardClick(e: WechatMiniprogram.BaseEvent): void;
  /** 字符串索引签名，满足MethodOption约束 */
  [key: string]: Function;
}

Component<
  CollectionContentData,
  CollectionContentProps,
  CollectionContentMethods,
  WechatMiniprogram.Component.BehaviorOption,
  {},
  false
>({
  behaviors: [computedBehavior],
  /**
   * 组件的属性列表
   */
  properties: {
    /** 当前列表数据 */
    currentList: {
      type: Array,
      value: []
    },
    /** 是否为编辑模式 */
    isEditMode: {
      type: Boolean,
      value: false
    },
    /** 当前滑动项索引 */
    currentSwipeIndex: {
      type: Number,
      value: -1
    },
    /** 未选中图标 */
    circleIcon: {
      type: String,
      value: ''
    },
    /** 选中图标 */
    circleActIcon: {
      type: String,
      value: ''
    },
    /** 删除图标 */
    deleteIcon: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    /** 触摸开始位置 */
    touchStartX: 0,
    /** 触摸开始时间 */
    touchStartTime: 0
  },

  /**
   * 计算属性
   */
  computed: {
    // 将收藏数据转换为HotelItem组件期望的格式
    convertedList(data: Record<string, unknown>) {
      const typedData = data as unknown as { currentList: Collection.List };
      const { currentList } = typedData;

      return currentList.map((item: Collection.Item) => {
        // 类型守卫：检查是否为酒店收藏项
        const isHotelItem = (item: Collection.Item): item is Collection.HotelItem => {
          return 'hotelName' in item && 'address' in item && 'price' in item && 'rating' in item;
        };

        if (isHotelItem(item)) {
          // 转换为HotelItem组件期望的格式
          return {
            hotelId: item.id,
            lowRate: item.price.toString(),
            currencySymbol: '¥',
            hotel: {
              hotelName: item.hotelName,
              thumbNailUrl: item.image || '',
              review: {
                score: item.rating,
                starRate: Math.floor(item.rating) // 将评分转换为星级
              },
              districtName: item.address,
              businessZoneName: '',
              features: [] as string[]
            },
            // 保留原始数据用于其他操作
            originalData: item
          };
        }

        // 如果不是酒店项，返回基础格式
        return {
          hotelId: item.id,
          lowRate: '0',
          currencySymbol: '¥',
          hotel: {
            hotelName: item.title,
            thumbNailUrl: item.image || '',
            review: {
              score: 0,
              starRate: 0
            },
            districtName: '',
            businessZoneName: '',
            features: [] as string[]
          },
          originalData: item
        };
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 触摸开始事件
     */
    onTouchStart(e: WechatMiniprogram.TouchEvent) {
       if ((this.properties as any).isEditMode) return;

       this.setData({
         touchStartX: e.touches[0].clientX,
         touchStartTime: Date.now()
       });
    },

    /**
     * 触摸移动事件
     */
    onTouchMove() {
       if ((this.properties as any).isEditMode) return;
       // 触摸移动逻辑由父组件处理
    },

    /**
     * 触摸结束事件
     */
    onTouchEnd(e: WechatMiniprogram.TouchEvent) {
       if ((this.properties as any).isEditMode) return;

       const touchEndX = e.changedTouches[0].clientX;
       const touchEndTime = Date.now();
       const deltaX = this.data.touchStartX - touchEndX;
       const deltaTime = touchEndTime - this.data.touchStartTime;

       // 触发父组件的触摸结束事件
       this.triggerEvent('touchend', {
         index: e.currentTarget.dataset.index,
         deltaX,
         deltaTime
       });
    },

    /**
     * 选择项目事件
     */
    onSelectItem(e: WechatMiniprogram.BaseEvent) {
      const itemId = e.currentTarget.dataset.id;
      this.triggerEvent('selectitem', { itemId });
    },

    /**
     * 删除项目事件
     */
    onDeleteItem(e: WechatMiniprogram.BaseEvent) {
      const itemId = e.currentTarget.dataset.id;
      this.triggerEvent('deleteitem', { itemId });
    },

    /**
     * 点击项目事件
     */
    onItemClick(e: WechatMiniprogram.BaseEvent) {
      if ((this.properties as any).isEditMode) {
        this.onSelectItem(e);
      } else {
        const itemId = e.currentTarget.dataset.id;
        this.triggerEvent('itemclick', { itemId });
      }
    },

    /**
     * 酒店点击事件（保留兼容性）
     */
    onHotelClick(e: WechatMiniprogram.BaseEvent) {
      if ((this.properties as any).isEditMode) {
        return; // 编辑模式下不处理酒店点击
      }

      // 获取酒店数据并触发事件
      const hotelData = e.detail.hotelData;
      this.triggerEvent('itemclick', {
        hotelData: hotelData,
        originalData: hotelData.originalData
      });
    },

    /**
     * 酒店卡片点击事件（新的处理方式）
     */
    onHotelCardClick(e: WechatMiniprogram.BaseEvent) {
      if ((this.properties as any).isEditMode) {
        return; // 编辑模式下不处理酒店点击
      }

      // 阻止事件冒泡到父容器的触摸事件
      e.stopPropagation();

      // 获取酒店数据并触发事件
      const hotelData = e.currentTarget.dataset.hotelData;
      this.triggerEvent('itemclick', {
        hotelData: hotelData,
        originalData: hotelData.originalData
      });
    }
  }
});
