/* 自定义导航栏样式 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  transition: background-color 0.3s ease;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  position: relative;
}

/* 返回按钮样式 */
.nav-back {
  display: flex;
  align-items: center;
  justify-content: center;
  
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-icon {
  width: 22rpx;
  height: 22rpx;
  border-left: 2rpx solid;
  border-bottom: 2rpx solid;
  border-color: inherit;
  transform: rotate(45deg);
  transition: border-color 0.3s ease;
}

/* 标题样式 */
.nav-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease, transform 0.3s ease;
  
  &.hidden {
    transform: translate(-50%, -50%) translateY(-10px);
  }
  
  &.visible {
    transform: translate(-50%, -50%) translateY(0);
  }
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

/* 右侧占位 */
.nav-right {
  width: 44px;
  height: 44px;
}