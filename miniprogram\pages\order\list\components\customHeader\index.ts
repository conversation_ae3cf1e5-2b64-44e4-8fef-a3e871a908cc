// pages/order/list/components/customHeader/index.ts
import { getImageUrl } from '../../../../../utils/images';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    currentKeyword: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    searchIcon: getImageUrl('user/search.png'),
    selectIcon: getImageUrl('user/select.png'),
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 获取状态栏高度
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight || 44
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 返回按钮点击事件
     */
    onBackClick() {
      this.triggerEvent('back');
    },

    /**
     * 搜索框点击事件
     */
    onSearchClick() {
      console.log('头部组件搜索框被点击');
      this.triggerEvent('searchclick');
      console.log('已触发searchclick事件');
    },

    /**
     * 筛选按钮点击事件
     */
    onFilterClick() {
      this.triggerEvent('filterclick');
    }
  }
});
