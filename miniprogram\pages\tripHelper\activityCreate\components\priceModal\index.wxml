<t-popup placement="bottom" visible="{{visible}}" bind:visible-change="handleVisibleChange">
	<view class="popup-body">
		<view class="popup-title">
			<text catch:tap="handleClose">取消</text>
			<text class="main-title">活动价格</text>
			<text class="confirm-trigger" catch:tap="handleSubmit">确定</text>
		</view>
		<view class="popup-content">
			成人*
			<input
				value="{{price}}"
				type="number"
				placeholder="填写活动价格"
				placeholder-style="color: #ccccce;font-weight: normal;"
				bindinput="handleValueChange"
				data-field="price"
			/>
			<text>元/人</text>
		</view>
		<view class="popup-content">
			儿童
			<input
				value="{{childPrice}}"
				type="number"
				placeholder="填写活动价格"
				placeholder-style="color: #ccccce;font-weight: normal;"
				bindinput="handleValueChange"
				data-field="childPrice"
			/>
			<text>元/人</text>
		</view>
		<view class="popup-content">
			老人
			<input
				value="{{elderlyPrice}}"
				type="number"
				placeholder="填写活动价格"
				placeholder-style="color: #ccccce;font-weight: normal;"
				bindinput="handleValueChange"
				data-field="elderlyPrice"
			/>
			<text>元/人</text>
		</view>
	</view>
</t-popup>