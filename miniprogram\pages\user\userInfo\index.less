/* 个人资料页面样式 - 用户信息编辑和管理页面样式 */
.userInfo-my-page {
  /* 用户信息编辑区域样式 - 圆角卡片设计 */
  .userInfo {
    margin: 16rpx;
    border-radius: 40rpx;
    background-color: #fff;

    /* 信息项样式 - 水平布局，左右对齐 */
    .userInfo-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 32rpx;
      margin-bottom: 8rpx;
      height: 104rpx;

      /* 头像项特殊样式 - 增加高度以容纳头像 */
      &.avatar {
        height: 182rpx;

        /* 头像图片样式 - 圆形头像 */
        .userInfo-avatar {
          width: 118rpx;
          height: 118rpx;
        }
      }

      /* 信息内容区域样式 - 右对齐布局 */
      .userInfo-content {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

      /* 信息标题样式 */
      .userInfo-title {
        font-size: 28rpx;
        color: #000;
      }

      /* 信息文本样式 */
      .userInfo-text {
        font-size: 28rpx;
        color: #000;

        /* 空值状态样式 - 灰色文字 */
        &.userInfo-text-empty {
          color: #ccccce;
        }
      }

      /* 箭头图标样式 - 右侧指示箭头 */
      .arrow-icon {
        margin-left: 16rpx;
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  /* 退出登录按钮样式 - 圆角按钮设计 */
  .login-out {
    margin: 0 16rpx;
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    background: #ffffff;
    border-radius: 120rpx;
  }
}


