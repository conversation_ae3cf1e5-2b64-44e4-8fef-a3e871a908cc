Page({
  data: {
    url: '',
    title: ''
  },

  onLoad(options: any) {
    // 获取传递过来的URL和标题参数
    if (options.url) {
      this.setData({
        url: decodeURIComponent(options.url),
        title: options.title ? decodeURIComponent(options.title) : '页面'
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: this.data.title
      });
    }
  },

  // web-view加载完成事件
  onWebviewLoad(e: any) {
    console.log('web-view加载完成', e);
  },

  // web-view加载失败事件
  onWebviewError(e: any) {
    console.error('web-view加载失败', e);
    wx.displayToast({
      title: '页面加载失败',
      icon: 'none'
    });
  }
}); 