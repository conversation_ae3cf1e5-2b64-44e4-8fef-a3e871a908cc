// pages/hotel/hotelList/components/PriceStar/PriceStar.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 重置按钮点击
    onResetClick() {
      // 重置价格/星级选择，后续开发
      console.log('重置价格/星级选择');
      // 触发重置事件
      this.triggerEvent('priceStarReset');
    },

    // 完成按钮点击
    onConfirmClick() {
      // 统一的关闭弹窗事件
      console.log('完成价格/星级选择');
      this.triggerEvent('filterPopupClose', {
        filterType: 'priceStar'
      });
    }
  }
})