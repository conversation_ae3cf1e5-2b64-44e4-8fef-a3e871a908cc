nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}
.main{
  background-color: #F7F7F7;
  height: 100vh;
  width: 100vw;
}
.activity-banner{
  width: 100%;
  position: relative;
  background-color: #FFFFFF;
  /* 轮播图样式 */
  .banner-swiper {
    width: 100%;
    height: 574rpx;
    position: relative;

    .swiper-image {
      width: 100%;
      height: 552rpx;;
    }
    /* 指示点容器 */
    .wx-swiper-dots {
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
    /* 单个指示点 */
    .wx-swiper-dot {
      width: 6rpx;
      height: 6rpx;
      border-radius: 0;
      margin: 0 4rpx;
      background: #D9D9D9;
      transition: all 0.3s ease;
    }
    /* 当前激活的指示点 */
    .wx-swiper-dot-active {
      width: 32rpx;
      height: 6rpx;
      border-radius: 0;
      background-color: #0198FF;
    }
  }
  .location-box{
    background: rgba(0,0,0,0.3);
    border-radius: 32rpx;
    padding: 8rpx 16rpx;
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 50rpx;
    left: 20rpx;
    image{
      width: 28rpx;
      height: 28rpx;
      margin-right: 8rpx;
    }
    text{
      font-size: 24rpx;
      color: #FFFFFF;
    }
  }
}
.activity-title{
  width: 100%;
  background-color: #FFFFFF;
  padding: 34rpx 32rpx 24rpx 32rpx;
  position: relative;

  .title{
    font-weight: 500;
    font-size: 40rpx;
    color: #33333E;
    max-width: 470rpx;        /* 必须设置宽度 */
    white-space: nowrap;      /* 强制不换行 */
    overflow: hidden;         /* 隐藏溢出内容 */
    text-overflow: ellipsis;  /* 显示省略号 */
    display: block;           /* 或 inline-block */
  }
  .tips-list{
    margin-top: 8rpx;
    display: flex;
    align-items: center;

    .tips-item{
      display: flex;
      align-items: center;
      background: #EEF8FF;
      border-radius: 16rpx;
      padding: 8rpx 12rpx;
      margin-right: 14rpx;
      .p1{
        width: 24rpx;
        height: 24rpx;
        margin-right: 8rpx;
      }
      .p2{
        width: 16rpx;
        height: 16rpx;
        margin-left: 4rpx;
      }
      text{
        font-size: 20rpx;
        color: #33333E;
      }
    }
  }
  .price-box{
    width: 232rpx;
    height: 206rpx;
    background-image: url("https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/price_bg.png");
    background-size: 100% 100%;
    position: absolute;
    left: 486rpx;
    bottom: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    .content{
      display: flex;
      align-items: center;
      transform: rotate(-5deg);
      .left{
        font-weight: 500;
        font-size: 64rpx;
        color: #33333E;
        letter-spacing: 3rpx;
        font-style: italic;
      }
      .right{
        margin-left: 8rpx;
        display: flex;
        flex-direction: column;
        .t1{
          font-size: 24rpx;
          color: #33333E;
          font-style: italic;
        }
        .t2{
          line-height: 1;
          font-size: 20rpx;
          color: #33333E;
        }
      }
    }
  }
}
.activity-message{
  margin-top: 24rpx;
  width: 100%;
  background-color: #FFFFFF;
  padding-top: 32rpx;
  padding-bottom: 24rpx;
  

  .title-box{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-content{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 32rpx;

      image{
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
      text{
        font-size: 28rpx;
        color: #66666E;
      }
      .txt-num{
        font-size: 24rpx;
        color: #99999E;
        margin-left: 16rpx;
      }
    }
    .time-txt{
      font-weight: 500;
      font-size: 28rpx;
      color: #33333E;
      margin-right: 32rpx;
    }
    .right-content{
      display: flex;
      align-items: center;
      margin-right: 32rpx;
      text{
        font-size: 24rpx;
        color: #66666E;
        margin-right: 8rpx;
      }
      image{
        width: 20rpx;
        height: 20rpx;
      }
    }
  }

  .address{
    margin-top: 40rpx;
    padding: 0 32rpx;
    .title-left{
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;
      image{
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
      text{
        font-size: 28rpx;
        color: #66666E;
      }
    }
    .address-item{
      margin-top: 8rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      background: #F9F9F9;
      border-radius: 24rpx;
      text{
        font-size: 28rpx;
        color: #33333E;
      }
      .blod{
        font-weight: 500;
        margin-left: 24rpx;
      }
      image{
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .member{
    margin-top: 40rpx;
    .head-list{
      margin-top: 24rpx;
      margin-left: 48rpx;
      overflow-x: scroll;
      display: flex;

      .head-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 24rpx;
        .head-pic-box{
          position: relative;
          .head-pic {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            overflow: hidden;
          }
          .icon-box{
            width: 24rpx;
            height: 24rpx;
            border-radius: 48rpx 48rpx 48rpx 48rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            right: 0;
            bottom: 0;
          }
          .girl{
            background: #FF4EAA;
          }
          .boy{
            background: #0198FF;
          }
          .icon{
            width: 14.4rpx;
            height: 14.4rpx;
          }
        }
        

        text {
          margin-top: 8rpx;
          font-size: 20rpx;
          color: #99999E;
        }
      }
    }
  }
}
.activity-describe{
  margin-top: 24rpx;
  width: 100%;
  background-color: #FFFFFF;
  padding-top: 24rpx;
  
  .title{
    margin-left: 32rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #33333E;
  }
  .content{
    padding: 24rpx 32rpx;
    .address-box{
      display: flex;
      align-items: center;
      text{
        font-weight: 500;
        font-size: 32rpx;
        color: #33333E;
      }
      view{
        margin-left: 8rpx;
        padding: 4rpx 8rpx;
        background: #EEF8FF;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 20rpx;
        color: #0198FF;
      }
    }
    .describe-txt{
      font-size: 24rpx;
      color: #66666E;
      margin-top: 16rpx;
    }
  }
  .img{
    width: 100%;
    margin-top: 12rpx;
  }
  .bottom{
    padding-top: 44rpx;
    padding-bottom: 300rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    .line{
      width: 24rpx;
      height: 2rpx;
      background-color: #CCCCCE;
    }
    text{
      font-size: 28rpx;
      color: #CCCCCE;
      margin: 0 22rpx;
    }
  }
}
.bottom-bar{
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 100;
  background-color: #FFFFFF;
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容iOS >= 11.2 */
  .bottom-bar-content{
    padding: 16rpx 32rpx 16rpx 56rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 2rpx solid #F7F7F7;

    .left{
      display: flex;
      flex-direction: column;
      align-items: center;
      image{
        width: 48rpx;
        height: 48rpx;
        margin-bottom: 4rpx;
      }
      text{
        font-size: 20rpx;
        color: #99999E;
      }
    }

    .right{
      height: 96rpx;
      background: #0198FF;
      border-radius: 120rpx 120rpx 120rpx 120rpx;
      text-align: center;
      line-height: 96rpx;
      color: #FFFFFF;
      text{
        font-weight: 500;
        font-size: 32rpx;
      }
    }
    .disabled{
      background: #F3F3F3;
      color: #CCCCCE;
    }
    .short{
      width: 392rpx;
    }
    .long{
      width: 510rpx;
    }
  }
}