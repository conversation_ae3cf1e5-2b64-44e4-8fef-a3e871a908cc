// pages/tripHelper/chooseRoute/components/Days/Days.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的天数值（如3表示3天）
    currentDays: {
      type: Number,
      value: 3 // 默认选中3天
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 天数选项数组
    daysArray: [
      1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
    ],
    // picker-view的value，表示选中的索引
    pickerValue: [2] // 默认选中第3项（3天）
  },

  /**
   * 组件生命周期
   */
  ready() {
    // 根据传入的currentDays设置初始选中项
    const selectedIndex = this.properties.currentDays > 0 ? this.properties.currentDays - 1 : 2;
    this.setData({
      pickerValue: [selectedIndex]
    });
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // picker-view滚动选择事件
    onPickerChange(e: any) {
      const selectedIndex = e.detail.value[0];
      const selectedDay = this.data.daysArray[selectedIndex];
      
      this.setData({
        pickerValue: e.detail.value
      });

      // 向父组件传递选中的天数
      this.triggerEvent('daySelect', {
        index: selectedIndex,
        label: `${selectedDay}天`,
        value: selectedDay // 实际天数值
      });
    },

    // 确认选择（供父组件调用）
    confirmSelect() {
      const selectedIndex = this.data.pickerValue[0];
      const selectedDay = this.data.daysArray[selectedIndex];
      
      // 触发确认事件
      this.triggerEvent('dayConfirm', {
        index: selectedIndex,
        label: `${selectedDay}天`,
        value: selectedDay
      });
    },

    // 点击取消按钮
    onCancel() {
      this.triggerEvent('dayCancel');
      // 触发确认事件
      this.triggerEvent('dayConfirm', {
        index: 0,
        label: '',
        value: ''
      });
    },

    // 点击确定按钮
    onConfirm() {
      const selectedIndex = this.data.pickerValue[0];
      const selectedDay = this.data.daysArray[selectedIndex];
      
      // 触发确认事件
      this.triggerEvent('dayConfirm', {
        index: selectedIndex,
        label: `${selectedDay}天`,
        value: selectedDay
      });
    }
  }
})