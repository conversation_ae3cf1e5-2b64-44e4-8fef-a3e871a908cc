// pages/hotelList/hotelList.ts
import { hotelListApi, sortTypeApi, priceStarApi, areaApi, filterOptionsApi } from '../../../api/hotel/hotelList';
import { StorageKeyEnum } from '../../../enum/index';
import dayjs from 'dayjs'

// 添加完整的TypeScript接口定义
interface FilterData {
  currentSortLabel: string;
  priceStarCount: number;
  distanceLabel: string;
  filterCount: number;
}

interface SelectedFilter {
  typeId: number;
  filterId: string;
  parentTypeId: number;
  category: string; // 筛选类型：'sort' | 'distance' | 'filter' | 'priceStar'
}

interface SortSelectEvent {
  detail: {
    sortOption: {
      key: string;
      desc: string;
    };
  };
}

interface FilterChangeEvent {
  detail: {
    selectedFilters: SelectedFilter[];
    selectedCount: number;
  };
}

interface PriceStarChangeEvent {
  detail: {
    selectedCount: number;
    selectedOptions: any[];
  };
}

interface DistanceChangeEvent {
  detail: {
    selectedItem: Hotel.IFilter;
    filters: SelectedFilter;
  };
}

interface FilterPopupCloseEvent {
  detail: {
    selectedItem?: Hotel.IFilter;
    filterType?: string;
  };
}

interface HotelClickEvent {
  detail: {
    hotelData: Hotel.IHotelList;
  };
}

interface LoadOptions {
  cityName?: string;
  cityId?: string;
  arrivalDate?: string;
  departureDate?: string;
  queryText?: string;
}

/**
 * 筛选状态管理器
 * 
 * 职责：
 * - 统一管理所有类型的筛选状态
 * - 提供筛选项的增删改操作
 * - 自动处理UI状态同步和数据重载
 */
class FilterStateManager {
  private data: any;
  private setData: Function;

  constructor(data: any, setData: Function) {
    this.data = data;
    this.setData = setData;
  }

  /**
   * 更新筛选数据并重新加载列表
   * @param newFilters 新的筛选项数组
   * @param shouldReload 是否重新加载数据
   */
  updateFilters(newFilters: SelectedFilter[], shouldReload = true) {
    const filterCount = newFilters.filter(f => f.category === 'filter').length;
    
    this.setData({
      filters: newFilters,
      'filterData.filterCount': filterCount,
      pageIndex: shouldReload ? 1 : this.data.pageIndex
    });
  }

  /**
   * 重置特定类型的筛选项
   * @param category 筛选类型
   * @param shouldReload 是否重新加载数据
   */
  resetFiltersByCategory(category: string, shouldReload = true) {
    const filteredItems = this.data.filters.filter((filter: SelectedFilter) => filter.category !== category);
    this.updateFilters(filteredItems, shouldReload);
    
    // 根据category类型重置对应的UI状态
    const updates: any = {};
    switch (category) {
      case 'filter':
        updates['filterData.filterCount'] = 0;
        break;
      case 'distance':
        updates['filterData.distanceLabel'] = '';
        updates.currentSelectedDistance = null;
        break;
      case 'priceStar':
        updates['filterData.priceStarCount'] = 0;
        break;
    }
    
    if (Object.keys(updates).length > 0) {
      this.setData(updates);
    }
  }

  /**
   * 添加筛选项
   * @param filter 要添加的筛选项
   * @param shouldReload 是否重新加载数据
   */
  addFilter(filter: SelectedFilter, shouldReload = true) {
    const newFilters = [...this.data.filters, filter];
    this.updateFilters(newFilters, shouldReload);
  }

  /**
   * 替换特定类型的筛选项
   * @param category 筛选类型
   * @param newFilters 新的筛选项数组
   * @param shouldReload 是否重新加载数据
   */
  replaceFiltersByCategory(category: string, newFilters: SelectedFilter[], shouldReload = true) {
    const otherFilters = this.data.filters.filter((filter: SelectedFilter) => filter.category !== category);
    const allFilters = [...otherFilters, ...newFilters];
    this.updateFilters(allFilters, shouldReload);
  }
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgBaseUrl: getApp().globalData.imgBaseUrl,
    cityName: '杭州市',
    cityId: '1201',
    /** 入住时间 */
    arrivalDate: dayjs().format('YYYY-MM-DD'),
    /** 离店时间 */
    departureDate: dayjs().add(1, 'day').format('YYYY-MM-DD'),
    queryText: '',
    pageIndex: 1,
    pageSize: 10,
    hotels: [] as Hotel.IHotelList[],
    hasMore: true,
    /** 初始加载状态 */
    loading: false,
    /** 下拉刷新状态 */
    refreshing: false,
    /** 上拉加载更多状态 */
    loadingMore: false,
    /** scroll-view下拉刷新状态 */
    refresherTriggered: false,
    /** 排序列表 */
    sortList: [] as Hotel.ISortType[],
    sort: null as string | null,

    /** 排序类型 */
    typeId: null as number | null,
    /** 区域 */
    areaDistance: [] as Hotel.IFilter[],
    /** 筛选选项 */
    filterOptions: [] as Hotel.IFilter[],
    filters: [] as SelectedFilter[],
    /** 仅包含filter类型的筛选项 */
    filterFilters: [] as SelectedFilter[],

    // Filter组件数据
    filterData: {
      currentSortLabel: '',
      priceStarCount: 0,
      distanceLabel: '',
      filterCount: 0
    } as FilterData,
    // 弹窗控制
    expandPopupVisible: false,
    currentFilterType: '', // 当前活跃的筛选类型: 'sort' | 'priceStar' | 'distance' | 'filter'
    // 当前选中的位置距离项
    currentSelectedDistance: null as Hotel.IFilter | null,
    // 价格星级数据
    priceRangeVOs: [] as any[], // 待完善类型定义
    starRatingVOs: [] as any[],  // 待完善类型定义
    
    // 下拉刷新动画状态
    pullDownScale: 0.8, // 下拉时plane-icon的缩放比例
    maxPullDistance: 80, // 最大下拉距离(rpx)
  },

  // 筛选状态管理器
  filterStateManager: null as FilterStateManager | null,

  /**
   * 数据监听器 - 简化逻辑
   */
  observers: {
    'filters'(newFilters: SelectedFilter[]) {
      // 简化observers逻辑，只同步filterFilters
      const filterOnlyItems = newFilters.filter(filter => filter.category === 'filter');
      this.setData({
        filterFilters: filterOnlyItems
      });
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(option: LoadOptions) {
    // 初始化筛选状态管理器
    this.filterStateManager = new FilterStateManager(this.data, this.setData.bind(this));
    
    Object.keys(option).forEach((key) => {
      this.setData({
        [key]: option[key as keyof LoadOptions]
      })
    });
    // 可以从缓存或全局数据中获取城市信息
    wx.setStorageSync(StorageKeyEnum.Token, 'QWdNA11JB4fNjjsct7OlH2CGNe6UQCVJ6yqrvPgnllPjDsrLHFwDFjI9UNeQlADBuQk5uhs5BbySUXHDeVNAy3q4rAQVomMjrb7gAiObhdDh8dT9rXAkpCd8eqSES6Zd');
    this.getPriceStar();
    this.getAreaDistance();
    this.getFilterOptions();
    await this.getSortType();
    this.getHotelList();
  },

  /**
   * 统一错误处理机制
   */
  handleError(error: any, operation: string) {
    console.error(`${operation} error:`, error);
    
    // 重置所有加载状态
    this.setData({
      loading: false,
      refreshing: false,
      loadingMore: false,
      refresherTriggered: false
    });

    // 显示用户友好的错误提示
    let errorMessage = '操作失败，请重试';
    
    switch (operation) {
      case 'getHotelList':
        errorMessage = '加载酒店列表失败，请重试';
        break;
      case 'getSortType':
        errorMessage = '获取排序选项失败';
        break;
      case 'getPriceStar':
        errorMessage = '获取价格星级信息失败';
        break;
      case 'getAreaDistance':
        errorMessage = '获取区域信息失败';
        break;
      case 'getFilterOptions':
        errorMessage = '获取筛选选项失败';
        break;
    }

    wx.displayToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 统一的loading状态管理
   */
  setLoadingState(type: 'initial' | 'refresh' | 'loadMore' | 'none', state: boolean) {
    const updates: any = {};
    
    switch (type) {
      case 'initial':
        updates.loading = state;
        break;
      case 'refresh':
        updates.refreshing = state;
        updates.refresherTriggered = state;
        break;
      case 'loadMore':
        updates.loadingMore = state;
        break;
      case 'none':
        updates.loading = false;
        updates.refreshing = false;
        updates.loadingMore = false;
        updates.refresherTriggered = false;
        break;
    }
    
    this.setData(updates);
  },

  async getHotelList(isLoadMore = false) {
    const { queryText, cityId, arrivalDate, departureDate, pageIndex, pageSize, filters, sort } = this.data;
    
    // 将SelectedFilter转换为API期望的IFilter格式
    const apiFilters: Hotel.IFilter[] = filters.map(filter => ({
      typeId: filter.typeId,
      filterId: filter.filterId,
      name: '', // API可能不需要这些字段，但为了类型匹配保留
      nameEn: null,
      describe: null,
      multi: 0,
      parentTypeId: filter.parentTypeId,
      subFilters: [],
      grey: null
    }));
    
    const params = {
      queryText,
      cityId,
      arrivalDate,
      departureDate,
      sort,
      pageIndex,
      pageSize,
      filters: apiFilters
    }

    try {
      // 设置对应的加载状态
      if (isLoadMore) {
        this.setLoadingState('loadMore', true);
      } else if (pageIndex === 1) {
        this.setLoadingState('initial', true);
      }

      const { code, data: { hasMore, hotels } } = await hotelListApi(params);

      if (code === 200) {
        const newData: any = { hasMore };

        if (isLoadMore) {
          // 上拉加载更多，追加数据
          newData.hotels = [...this.data.hotels, ...hotels];
        } else {
          // 初始加载或下拉刷新，替换数据
          newData.hotels = hotels;
        }

        this.setData(newData);
        this.setLoadingState('none', false);
      } else {
        throw new Error(`API returned code: ${code}`);
      }
    } catch (error) {
      this.handleError(error, 'getHotelList');
    }
  },

  /**
   * 处理酒店卡片点击事件
   */
  handleHotelClick(e: HotelClickEvent) {
    const { hotelData } = e.detail;
    // 跳转到酒店详情页面
    wx.navigateTo({
      url: `/pages/hotel/detail/detail?hotelId=${hotelData.hotelId}`
    });
  },

  /**
   * 处理搜索事件
   */
  handleSearch(e: any) {
    // 重置分页状态，重新加载数据
    this.setData({
      pageIndex: 1
    });
    this.getHotelList(false);
  },

  /**
   * 处理展开弹窗关闭事件
   */
  onExpandPopupClose() {
    this.setData({
      expandPopupVisible: false,
      currentFilterType: ''
    });

    // 重置Filter组件的展开状态，确保箭头方向正确
    const filterComponent = this.selectComponent('#filter-component');
    if (filterComponent && filterComponent.resetExpandedStates) {
      filterComponent.resetExpandedStates();
    }
  },

  /**
   * 通用筛选处理方法
   */
  handleFilterChange(type: string, data: any, shouldClosePopup = false) {
    if (!this.filterStateManager) return;

    switch (type) {
      case 'sort':
        this.setData({
          sort: data.sortOption.key,
          'filterData.currentSortLabel': data.sortOption.desc,
          pageIndex: 1
        });
        if (shouldClosePopup) this.onExpandPopupClose();
        this.getHotelList(false);
        break;

      case 'distance':
        const distanceFilter: SelectedFilter = {
          typeId: data.selectedItem.typeId,
          filterId: data.selectedItem.filterId,
          parentTypeId: data.selectedItem.parentTypeId || data.selectedItem.typeId,
          category: 'distance'
        };
        
        this.filterStateManager.replaceFiltersByCategory('distance', [distanceFilter]);
        this.setData({
          'filterData.distanceLabel': data.selectedItem.name,
          currentSelectedDistance: data.selectedItem
        });
        this.getHotelList(false);
        break;

      case 'filter':
        this.filterStateManager.replaceFiltersByCategory('filter', data.selectedFilters);
        this.getHotelList(false);
        break;

      case 'priceStar':
        this.setData({
          'filterData.priceStarCount': data.selectedCount
        });
        break;
    }
  },

  /**
   * 通用重置处理方法
   */
  handleFilterReset(type: string) {
    if (!this.filterStateManager) return;

    switch (type) {
      case 'distance':
        this.filterStateManager.resetFiltersByCategory('distance');
        this.getHotelList(false);
        break;

      case 'filter':
        this.filterStateManager.resetFiltersByCategory('filter');
        this.getHotelList(false);
        break;

      case 'priceStar':
        this.setData({
          'filterData.priceStarCount': 0,
          pageIndex: 1
        });
        this.getHotelList(false);
        break;
    }
  },

  /**
   * 筛选组件事件处理 ===================================================================
   */

  // 统一的筛选类型切换逻辑
  toggleFilterType(filterType: string) {
    if (this.data.currentFilterType === filterType && this.data.expandPopupVisible) {
      // 当前已展开该类型，点击收起
      this.setData({
        expandPopupVisible: false,
        currentFilterType: ''
      });
    } else {
      // 展开或切换到该类型
      this.setData({
        currentFilterType: filterType,
        expandPopupVisible: true
      });
    }
  },

  // 处理排序点击事件
  onSortClick(e: any) {
    this.toggleFilterType('sort');
  },

  // 处理价格/星级点击事件  
  onPriceStarClick(e: any) {
    this.toggleFilterType('priceStar');
  },

  // 处理位置距离点击事件
  onDistanceClick(e: any) {
    this.toggleFilterType('distance');
  },

  // 处理筛选点击事件
  onFilterClick(e: any) {
    this.toggleFilterType('filter');
  },

  // 处理排序选择 - 使用通用方法
  onSortSelect(e: SortSelectEvent) {
    this.handleFilterChange('sort', e.detail, true);
  },

  // 处理价格/星级变更 - 使用通用方法
  onPriceStarChange(e: PriceStarChangeEvent) {
    this.handleFilterChange('priceStar', e.detail);
  },

  // 处理位置距离变更 - 使用通用方法
  onDistanceChange(e: DistanceChangeEvent) {
    this.handleFilterChange('distance', e.detail);
  },

  // 处理位置距离重置事件 - 使用通用方法
  onDistanceReset(e: any) {
    this.handleFilterReset('distance');
  },

  // 统一的筛选弹窗关闭事件
  onFilterPopupClose(e: FilterPopupCloseEvent) {
    // 处理位置距离的状态同步
    if (e.detail && e.detail.filterType === 'distance') {
      const { selectedItem } = e.detail;

      if (selectedItem) {
        // 有选中项，更新Filter高亮状态
        this.setData({
          'filterData.distanceLabel': selectedItem.name,
          currentSelectedDistance: selectedItem
        });
      } else {
        // 没有选中项，清除Filter高亮状态
        this.setData({
          'filterData.distanceLabel': '',
          currentSelectedDistance: null
        });
      }
    }

    this.onExpandPopupClose();
  },

  // 处理价格/星级重置事件 - 使用通用方法
  onPriceStarReset(e: any) {
    this.handleFilterReset('priceStar');
  },

  // 处理更多筛选重置事件 - 使用通用方法
  onMoreFilterReset(e: any) {
    this.handleFilterReset('filter');
  },

  // 处理更多筛选变更 - 使用通用方法
  onFilterChange(e: FilterChangeEvent) {
    this.handleFilterChange('filter', e.detail);
  },

  /**
   * scroll-view下拉刷新事件处理
   */
  async onRefresherRefresh() {
    this.setData({
      refreshing: true,
      refresherTriggered: true,
      pageIndex: 1
    });

    await this.getHotelList(false);
  },

  /**
   * 下拉过程中的动画处理
   */
  onRefresherPulling(e: any) {
    const { dy } = e.detail; // 下拉距离，单位px
    const { maxPullDistance } = this.data;
    
    // 边界处理：只在有效下拉距离时处理
    if (dy <= 0) return;
    
    // 将px转换为rpx (假设设备像素比为2)
    const pullDistanceRpx = dy * 2;
    
    // 计算缩放比例：从0.8到1.5，使用缓动函数
    const minScale = 0.8;
    const maxScale = 1.5;
    const progress = Math.min(pullDistanceRpx / maxPullDistance, 1);
    
    // 使用easeOut缓动函数使动画更自然
    const easeOutProgress = 1 - Math.pow(1 - progress, 3);
    const scale = Math.max(minScale, minScale + (maxScale - minScale) * easeOutProgress);
    
    // 防抖：只在缩放值有明显变化时更新
    const currentScale = this.data.pullDownScale;
    if (Math.abs(scale - currentScale) > 0.05) {
      this.setData({
        pullDownScale: Number(scale.toFixed(2))
      });
    }
  },

  /**
   * 下拉刷新恢复时重置动画状态
   */
  onRefresherRestore() {
    this.setData({
      pullDownScale: 0.8
    });
  },

  /**
   * scroll-view滚动到底部事件处理
   */
  async onScrollToLower() {
    const { hasMore, loadingMore } = this.data;
    // 如果没有更多数据或正在加载中，则不执行
    if (!hasMore || loadingMore) {
      return;
    }

    // 增加页码
    const newPageIndex = this.data.pageIndex + 1;
    this.setData({ pageIndex: newPageIndex });

    // 加载更多数据
    await this.getHotelList(true);
  },

  // 获取排序方式
  async getSortType() {
    try {
      const { code, data } = await sortTypeApi();
      if (code === 200) {
        this.setData({
          sortList: data.sort,
          sort: data.sort[0].key,
          "filterData.currentSortLabel": data.sort[0].desc,
        });
      } else {
        throw new Error(`API returned code: ${code}`);
      }
    } catch (error) {
      this.handleError(error, 'getSortType');
    }
  },

  // 获取价格星级
  async getPriceStar() {
    try {
      const { code, data } = await priceStarApi();
      if (code === 200) {
        this.setData({
          priceRangeVOs: data.priceRangeVOs,
          starRatingVOs: data.starRatingVOs
        });
      } else {
        throw new Error(`API returned code: ${code}`);
      }
    } catch (error) {
      this.handleError(error, 'getPriceStar');
    }
  },

  // 获取区域
  async getAreaDistance() {
    try {
      const params = {
        cityId: this.data.cityId,
        typeId: this.data.typeId
      }
      const { code, data } = await areaApi(params);
      if (code === 200) {
        this.setData({ areaDistance: data });
      } else {
        throw new Error(`API returned code: ${code}`);
      }
    } catch (error) {
      this.handleError(error, 'getAreaDistance');
    }
  },

  // 获取筛选选项
  async getFilterOptions() {
    try {
      const { code, data } = await filterOptionsApi(this.data.cityId);
      if (code === 200) {
        this.setData({ filterOptions: data });
      } else {
        throw new Error(`API returned code: ${code}`);
      }
    } catch (error) {
      this.handleError(error, 'getFilterOptions');
    }
  },

})