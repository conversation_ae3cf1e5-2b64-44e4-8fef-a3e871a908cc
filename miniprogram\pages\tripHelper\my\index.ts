// pages/tripHelper/my/index.ts
import { getImageUrl } from '../../../utils/images';
import { ComponentWithComputedStore } from '../../../core/componentWithStoreComputed';
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings';
import { behavior as computedBehavior } from 'miniprogram-computed';
import { store } from '../../../store/index';
import { GenderEnum } from '../../../enum/my';
import { getUserInfo, activitySignIn, getActivityList } from '../../../api/my';
import { StatusDisplayType } from '../../../components/statusDisplay/types';
import { hasToken } from '../../../utils/userInfo';
import { LoginManager } from '../../../utils/loginManager';

ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior, computedBehavior],

  storeBindings: {
    store,
    fields: ['userInfo'],
    actions: ['setUserDetail', 'updateUserField']
  } as const,



  /**
   * 页面的初始数据
   */
  data: {
    currentTabIndex: 2, // 当前在我的页面，对应tabbar的第三个tab

    // 登录状态
    isLoggedIn: false,

    // 上拉刷新相关状态
    refreshing: false,
    loadingMore: false,



    // 背景图片
    bgImage: getImageUrl('tripHelper/my/bg.png'),

    // 性别标识图标
    genderMaleIcon: getImageUrl('tripHelper/my/men.png'),
    genderFemaleIcon: getImageUrl('tripHelper/my/women.png'),

    // 功能图标（已登录状态）
    messageIcon: getImageUrl('tripHelper/my/msg.png'),
    orderIcon: getImageUrl('tripHelper/my/order.png'),
    walletIcon: getImageUrl('tripHelper/my/wallet.png'),

    // 功能图标（未登录状态）
    messageDisIcon: getImageUrl('tripHelper/my/msg_dis.png'),
    orderDisIcon: getImageUrl('tripHelper/my/order_dis.png'),
    walletDisIcon: getImageUrl('tripHelper/my/wallet_dis.png'),

    // 消息数量（模拟数据，实际应从接口获取）
    messageCount: 1,

    // 消息红点背景图
    msgNumBg: getImageUrl('tripHelper/my/msg_num.png'),
    msgNumBg2: getImageUrl('tripHelper/my/msg_num2.png'),

    // 加载状态（初始为false，登录后再设置为true）
    isLoadingActivityOrders: false,

    // 活动订单列表
    activityOrders: [] as My.IActivityDTO[],

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: false,

    // 空态相关状态（初始显示空态，登录后再根据数据决定）
    showEmptyState: true,
    emptyStateType: StatusDisplayType.DataEmpty,

    // 时间和地址图标
    timeIcon: getImageUrl('tripHelper/my/time.png'),
    addressIcon: getImageUrl('tripHelper/my/address.png'),

    // 状态背景图（统一使用status1.png，后续可根据状态替换）
    statusBgIcon: getImageUrl('tripHelper/my/status1.png'),

    // 导航栏状态
    navbarVisible: false,
    navbarOpacity: 0,


  },

  computed: {
    // 显示的用户昵称
    displayNickname(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; isLoggedIn: boolean };
      if (!typedData.isLoggedIn) {
        return '畅旅用户';
      }
      return typedData.userInfo?.nickname || '翎游用户9876';
    },
    // 显示的用户头像（小图标）
    displayAvatar(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; isLoggedIn: boolean };
      if (!typedData.isLoggedIn) {
        return getImageUrl('user/avatar_default.png');
      }
      return typedData.userInfo?.avatar || getImageUrl('user/avatar_default.png');
    },
    // 性别标识图标
    genderIcon(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; genderMaleIcon: string; genderFemaleIcon: string; isLoggedIn: boolean };
      if (!typedData.isLoggedIn) {
        return ''; // 未登录时不显示性别图标
      }
      const gender = typedData.userInfo?.gender;
      if (gender === GenderEnum.Male) {
        return typedData.genderMaleIcon;
      } else if (gender === GenderEnum.Female) {
        return typedData.genderFemaleIcon;
      }
      return ''; // 未设置性别时不显示图标
    },
    // 是否显示性别图标
    showGenderIcon(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; isLoggedIn: boolean };
      if (!typedData.isLoggedIn) {
        return false; // 未登录时不显示性别图标
      }
      const gender = typedData.userInfo?.gender;
      return gender === GenderEnum.Male || gender === GenderEnum.Female;
    },
    // 性别图标背景色
    genderIconBgColor(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes };
      const gender = typedData.userInfo?.gender;
      if (gender === GenderEnum.Male) {
        return '#0198FF'; // 男生蓝色
      } else if (gender === GenderEnum.Female) {
        return '#FF4EAA'; // 女生粉色
      }
      return '#0198FF'; // 默认蓝色
    },
    // 是否为默认头像
    isDefaultAvatar(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes };
      const avatar = typedData.userInfo?.avatar;
      return !avatar || avatar.includes('avatar_default.png');
    },
    // 昵称文字颜色
    nicknameTextColor(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; isLoggedIn: boolean };
      if (!typedData.isLoggedIn) {
        return '#33333E'; // 未登录时用深色
      }
      const avatar = typedData.userInfo?.avatar;
      const isDefault = !avatar || avatar.includes('avatar_default.png');
      return isDefault ? '#33333E' : '#fff'; // 默认头像用深色，自定义头像用白色
    },
    // 消息红点背景图
    messageBadgeBg(data: Record<string, unknown>) {
      const typedData = data as { messageCount: number; msgNumBg: string; msgNumBg2: string };
      return typedData.messageCount >= 10 ? typedData.msgNumBg2 : typedData.msgNumBg;
    },
    // 当前显示的消息图标
    currentMessageIcon(data: Record<string, unknown>) {
      const typedData = data as { isLoggedIn: boolean; messageIcon: string; messageDisIcon: string };
      return typedData.isLoggedIn ? typedData.messageIcon : typedData.messageDisIcon;
    },
    // 当前显示的订单图标
    currentOrderIcon(data: Record<string, unknown>) {
      const typedData = data as { isLoggedIn: boolean; orderIcon: string; orderDisIcon: string };
      return typedData.isLoggedIn ? typedData.orderIcon : typedData.orderDisIcon;
    },
    // 当前显示的钱包图标
    currentWalletIcon(data: Record<string, unknown>) {
      const typedData = data as { isLoggedIn: boolean; walletIcon: string; walletDisIcon: string };
      return typedData.isLoggedIn ? typedData.walletIcon : typedData.walletDisIcon;
    },
    // 菜单文字颜色
    menuTextColor(data: Record<string, unknown>) {
      const typedData = data as { isLoggedIn: boolean };
      return typedData.isLoggedIn ? '#33333E' : '#99999E';
    },
    // 导航栏标题（显示用户昵称，兜底显示"我的"）
    navbarTitle(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; isLoggedIn: boolean };
      if (!typedData.isLoggedIn) {
        return '我的';
      }
      return typedData.userInfo?.nickname || '我的';
    }
  },

  lifetimes: {
    attached() {
      // 检查登录状态
      const isLoggedIn = hasToken();
      console.log('页面初始化，登录状态:', isLoggedIn);
      this.setData({
        isLoggedIn
      });

      // 初始化数据（包含登录状态判断）
      this.init();
    }
  },

  pageLifetimes: {
    show() {
      // 页面显示时的逻辑
      const isLoggedIn = hasToken();
      const wasLoggedIn = this.data.isLoggedIn;

      this.setData({
        isLoggedIn
      });

      // 如果从未登录变为已登录，重新加载数据
      if (!wasLoggedIn && isLoggedIn) {
        this.init();
      }
    }
  },





  methods: {
    /**
     * 页面滚动事件
     */
    onPageScroll(e: WechatMiniprogram.Page.IPageScrollOption) {
      const { scrollTop } = e;
      const threshold = 80; // 滚动阈值

      if (scrollTop > threshold) {
        // 滚动超过阈值，显示导航栏（淡入）
        if (!this.data.navbarVisible) {
          this.setData({
            navbarVisible: true,
            navbarOpacity: 1
          });
        }
      } else {
        // 滚动未超过阈值，隐藏导航栏（淡出）
        if (this.data.navbarVisible) {
          this.setData({
            navbarVisible: false,
            navbarOpacity: 0
          });
        }
      }
    },

    /**
     * 初始化页面数据
     */
    async init() {
      try {
        // 只有登录状态才加载用户信息和订单数据
        if (this.data.isLoggedIn) {
          // 获取用户信息
          await this.loadUserInfo();

          // 加载活动订单列表
          await this.loadActivityOrders();
        } else {
          // 未登录状态直接显示空态
          await this.loadActivityOrders();
        }
      } catch (error) {
        console.error('初始化页面数据失败:', error);
      }
    },

    /**
     * 加载活动订单列表
     */
    async loadActivityOrders() {
      // 未登录状态直接显示空态，不显示骨架屏
      if (!this.data.isLoggedIn) {
        this.setData({
          isLoadingActivityOrders: false,
          activityOrders: [],
          showEmptyState: true,
          emptyStateType: StatusDisplayType.DataEmpty
        });
        return;
      }

      // 已登录状态显示骨架屏后加载数据
      await this.loadActivityOrdersWithType(false);
    },

    /**
     * 上拉刷新事件
     */
    async onRefresh() {
      if (!this.data.isLoggedIn) {
        this.setData({ refreshing: false });
        return;
      }

      this.setData({ refreshing: true });

      try {
        // 重新加载用户信息和活动订单
        await this.loadUserInfo();
        await this.loadActivityOrders();
      } catch (error) {
        console.error('刷新数据失败:', error);
      } finally {
        this.setData({ refreshing: false });
      }
    },

    /**
     * 上拉加载更多事件
     */
    async onLoadMore() {
      console.log('onLoadMore 被触发了！', {
        isLoggedIn: this.data.isLoggedIn,
        loadingMore: this.data.loadingMore,
        hasMore: this.data.hasMore
      });

      if (!this.data.isLoggedIn || this.data.loadingMore || !this.data.hasMore) {
        console.log('onLoadMore 被阻止执行');
        return;
      }

      console.log('开始加载更多数据...');
      await this.loadActivityOrdersWithType(true);
    },

    /**
     * 刷新活动订单列表（用于测试骨架屏）
     */
    refreshActivityOrders() {
      this.loadActivityOrders();
    },

    /**
     * 加载用户信息
     */
    async loadUserInfo() {
      try {
        const result = await getUserInfo();
        if (result.isSuccess && result.data) {
          // 更新store中的用户信息
          this.setUserDetail(result.data);
        } else {
          console.error('获取用户信息失败:', result.message);
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
      }
    },



    /**
     * 点击头像
     */
    onAvatarClick() {
      if (!this.data.isLoggedIn) {
        this.navigateToLogin();
      }
    },

    /**
     * 点击消息中心
     */
    onMessageClick() {
      if (!this.data.isLoggedIn) {
        this.navigateToLogin();
        return;
      }
      wx.navigateTo({
        url: '/pages/tripHelper/message/index'
      });
    },

    /**
     * 点击报名订单
     */
    onOrderClick() {
      if (!this.data.isLoggedIn) {
        this.navigateToLogin();
        return;
      }
      wx.navigateTo({
        url: '/pages/tripHelper/order/index'
      });
    },

    /**
     * 点击我的钱包
     */
    onWalletClick() {
      if (!this.data.isLoggedIn) {
        this.navigateToLogin();
        return;
      }
      wx.navigateTo({
        url: '/pages/tripHelper/wallet/index'
      });
    },

    /**
     * 点击查看名单
     */
    onViewListClick(e: WechatMiniprogram.TouchEvent) {
      const { id } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/pages/tripHelper/registrationList/index?id=${id}`
      });
    },

    /**
     * 点击打卡按钮
     */
    onClockInClick(e: WechatMiniprogram.TouchEvent) {
      const { id, orderId } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/pages/tripHelper/clockIn/index?activityId=${id}&orderId=${orderId}`
      });
    },

    /**
     * 点击签到按钮
     */
    async onSignInClick(e: WechatMiniprogram.TouchEvent) {
      const { id, orderId } = e.currentTarget.dataset;
      const activityId = parseInt(id);
      const orderIdNum = parseInt(orderId);

      // 检查是否已经签到过
      const currentActivity = this.data.activityOrders.find(item => item.id === activityId);
      if (currentActivity?.ifSignIn) {
        wx.showToast({
          title: '已签到',
          icon: 'success'
        });
        return;
      }

      try {
        // 调用签到接口
        const response = await activitySignIn({
          activityId: activityId,
          orderId: orderIdNum
        });

        if (response.code === 0) {
          // 签到成功
          wx.showToast({
            title: '签到成功',
            icon: 'success'
          });

          // 更新活动的签到状态
          const updatedOrders = this.data.activityOrders.map(item =>
            item.id === activityId ? { ...item, ifSignIn: true } : item
          );
          this.setData({
            activityOrders: updatedOrders
          });
        } else {
          // 签到失败
          wx.showToast({
            title: response.message || '签到失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('签到异常:', error);
        wx.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      }
    },

    /**
     * 点击管理按钮
     */
    onManageClick(e: WechatMiniprogram.TouchEvent) {
      const { id } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/pages/tripHelper/activityDetail/index?isShowManagementModal=1&id=${id}`
      });
    },

    /**
     * 点击编辑按钮/立即登录按钮
     */
    onEditClick() {
      if (!this.data.isLoggedIn) {
        this.navigateToLogin();
        return;
      }
      wx.navigateTo({
        url: '/pages/tripHelper/my/edit/index'
      });
    },

    /**
     * 跳转到登录页面
     */
    navigateToLogin() {
      LoginManager.navigateToLogin();
    },



    /**
     * 状态显示组件按钮点击事件
     */
    onStatusDisplayButtonClick(e: WechatMiniprogram.CustomEvent) {
      const { type } = e.detail;
      console.log('状态显示组件按钮点击:', type);

      if (type === StatusDisplayType.NetworkError || type === 'network-error') {
        // 网络异常时重新加载数据
        this.loadActivityOrders();
      } else if (type === StatusDisplayType.DataEmpty || type === 'data-empty') {
        // 空态时可以跳转到创建活动页面或其他操作
        wx.showToast({
          title: '暂无活动订单',
          icon: 'none'
        });
      }
    },



    /**
     * 加载活动订单数据
     */
    async loadActivityOrdersWithType(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.setData({
            isLoadingActivityOrders: true,
            showEmptyState: false
          });
        } else {
          this.setData({ loadingMore: true });
        }

        // 调用真实API
        const response = await getActivityList({
          page: isLoadMore ? this.data.pageNum : 1,
          size: this.data.pageSize
        });

        if (response.code === 200 && response.data) {
          const { records, hasNext } = response.data;

          let activityOrders: My.IActivityDTO[];
          if (isLoadMore) {
            // 上拉加载更多
            activityOrders = [...this.data.activityOrders, ...records];
          } else {
            // 首次加载
            activityOrders = records;
          }

          const isEmpty = activityOrders.length === 0;

          this.setData({
            isLoadingActivityOrders: false,
            loadingMore: false,
            activityOrders,
            hasMore: hasNext,
            pageNum: isLoadMore ? this.data.pageNum + 1 : 2,
            showEmptyState: isEmpty,
            emptyStateType: StatusDisplayType.DataEmpty
          });
        } else {
          throw new Error(response.message || '获取活动列表失败');
        }
      } catch (error) {
        console.error('加载活动订单失败:', error);
        this.setData({
          isLoadingActivityOrders: false,
          loadingMore: false,
          activityOrders: isLoadMore ? this.data.activityOrders : [],
          showEmptyState: !isLoadMore,
          emptyStateType: StatusDisplayType.NetworkError
        });
      }
    },

    /**
     * 处理tabbar切换事件
     */
    onTabChange(e: WechatMiniprogram.CustomEvent) {
      const { index } = e.detail;
      // 这里可以处理tabbar切换逻辑
      console.log('切换到tab:', index);
    },


  }
})
