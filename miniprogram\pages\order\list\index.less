/* pages/order/list/index.less */
.order-list-page {
  width: 100vw;
  min-height: 100vh;
  background-color: #f5f5f5;

  /* Tab切换 */
  .tab-container {
    position: fixed;
    top: 152rpx; /* 默认状态栏高度44px + 自定义头部高度108rpx */
    left: 0;
    right: 0;
    z-index: 100;
    background-color: #fff;

    .tab-list {
      display: flex;
      padding: 0 32rpx;

      .tab-item {
        flex: 1;
        position: relative;
        height: 112rpx;
        line-height: 112rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s ease;

        .tab-text {
          font-size: 28rpx;
          color: #33333E;
          transition: color 0.3s;
        }

        .tab-indicator {
          position: absolute;
          bottom: 22rpx;
          width: 48rpx;
          height: 2px;
          background-color: #568DED;
          border-radius: 3rpx;
        }

        &.active .tab-text {
          color: #568DED;
          font-weight: 600;
        }

        &:active {
          background-color: #f5f5f5;
        }
      }
    }
  }

  /* 订单列表 */
  .order-list {
    margin: 16rpx;

    /* 为每个订单卡片添加间距 */
    order-card {
      margin-bottom: 24rpx;
    }

    /* 最后一个卡片不需要底部间距 */
    order-card:last-child {
      margin-bottom: 0;
    }
  }
}
