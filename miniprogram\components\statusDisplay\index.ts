// components/statusDisplay/index.ts
import { getImageUrl } from '../../utils/images';

Component({
  properties: {
    type: {
      type: String,
      value: 'data-empty',
      observer: 'updateDisplay'
    },
    title: {
      type: String,
      value: '',
      observer: 'updateDisplay'
    },
    visible: {
      type: Boolean,
      value: true
    },
    /** 自定义样式类名 */
    customClass: {
      type: String,
      value: ''
    },
    /** 自定义内联样式 */
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    displayTitle: '',
    displayImage: '',
    displayDescription: '',
    shouldShowButton: false,
    displayButtonText: ''
  },

  lifetimes: {
    attached() {
      this.updateDisplay();
    }
  },

  methods: {
    updateDisplay() {
      const { type, title } = this.properties;

      let displayTitle = title;
      let displayDescription = '';
      let shouldShowButton = false;
      let displayButtonText = '';

      if (!displayTitle) {
        switch (type) {
          case 'data-empty':
          case 'empty':
            displayTitle = '没有内容';
            break;
          case 'network-error':
          case 'error':
            displayTitle = '网络好像出了问题';
            shouldShowButton = true;
            displayButtonText = '重试';
            break;
          default:
            displayTitle = '没有内容';
        }
      }

      this.setData({
        displayTitle,
        displayImage: getImageUrl('tripHelper/illustration1.png'),
        displayDescription,
        shouldShowButton,
        displayButtonText
      });
    },

    onButtonClick() {
      this.triggerEvent('buttonclick', {
        type: this.properties.type
      });
    }
  }
});