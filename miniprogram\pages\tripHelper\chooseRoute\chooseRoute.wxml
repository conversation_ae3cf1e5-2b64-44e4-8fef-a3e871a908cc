<view class="chooseRoutePage">
  <view class="search-box">
    <view class="search-box-content">
      <image class="search-icon" src="{{staticBaseUrl}}/tripHelper/chooseRoute/searchIcon.png" mode="widthFix">
      </image>
      <input class="search-input" value="{{searchValue}}" placeholder="查询路线" bind:input="onInput" bind:confirm="onSearch" />
      <t-icon wx:if="{{searchValue}}" name="close-circle" size="30rpx" bind:click="onIconTap" />
    </view>
  </view>
  <filter id="filter-component" city-label="{{filterData.cityLabel}}" days-label="{{filterData.daysLabel}}" bind:cityClick="onCityClick" bind:daysClick="onDaysClick" />
  
  <!-- 页面级弹窗容器 -->
  <view class="page-popup-container" wx:if="{{expandPopupVisible}}">
    <!-- 遮罩层 -->
    <view class="popup-mask" bind:tap="onExpandPopupClose"></view>
    <!-- 内容区 -->
    <view class="popup-content-wrapper">
      <view class="popup-content">
        <citys wx:if="{{filterType === 'city'}}" bind:citySelect="onCitySelect" hotCities="{{hotCities}}" regionList="{{regionList}}" selectedCity="{{filterData.cityLabel}}" />
        <days wx:if="{{filterType === 'days'}}" current-days="{{filterData.currentDays}}" bind:daySelect="onDaySelect" bind:dayConfirm="onDayConfirm" bind:dayCancel="onDayCancel" />
      </view>
    </view>
  </view>

  <!-- 路线列表 -->
  <scroll-view class="route-list" scroll-y="{{true}}" bindscrolltolower="onScrollToLower" lower-threshold="100">
    <view class="route-list-content">
      <!-- 骨架屏 -->
      <skeleton-list wx:if="{{routeLoading}}" />
      <!-- 路线列表 -->
      <block wx:else>
        <list-item wx:for="{{routeList}}" wx:key="routeId" route-data="{{item}}" selected="{{item.routeId === selectedRouteId}}" bind:selectRoute="onSelectRoute" />
        <!-- 加载更多状态 -->
        <view class="load-more-status" wx:if="{{routeList.length > 0}}">
          <view wx:if="{{routeLoadingMore}}" class="loading-more">
            <text>加载中...</text>
          </view>
          <view wx:elif="{{!routeHasMore}}" class="no-more">
            <text>没有更多数据了</text>     
          </view>
        </view>
        <!-- 空状态 -->
        <view wx:if="{{routeList.length === 0 && !routeLoading}}" class="empty-state">
          <text>暂无路线数据</text>
        </view>
      </block>
    </view>
  </scroll-view>
  <!-- 确定按钮 -->
  <view class="confirm-btn-container" wx:if="{{showConfirmBtn}}">
    <view class="confirm-btn" bind:tap="onConfirmRoute">确定</view>
  </view>
</view>