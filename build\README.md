# 微信小程序 CI/CD 构建系统

本目录包含了微信小程序的自动化构建和部署脚本。

## 文件结构

```
build/
├── ci.ts          # TypeScript CI构建脚本
├── deploy.sh      # 部署入口脚本
├── private.key    # 小程序上传密钥（需要手动添加）
└── README.md      # 本说明文档
```

## 前置要求

1. **Node.js环境**: 确保已安装 Node.js 和 npm
2. **项目依赖**: 在项目根目录运行 `npm install`
3. **上传密钥**: 将小程序的上传密钥文件放置在 `build/private.key`
4. **AppID配置**: 确保 `project.config.json` 中的 `appid` 字段正确

## 使用方法

### 方法一：使用 Shell 脚本（推荐）

```bash
# 基本部署（使用默认版本和描述）
./build/deploy.sh

# 指定版本号
./build/deploy.sh 1.2.3

# 指定版本号和描述
./build/deploy.sh 1.2.3 "修复bug并优化性能"

# 查看帮助
./build/deploy.sh --help
```

### 方法二：使用 TypeScript 脚本

```bash
# 安装 ts-node（如果未安装）
npm install -g ts-node

# 运行构建
ts-node build/ci.ts --appid wxappid --private-key build/private.key

# 指定版本和描述
ts-node build/ci.ts \
  --appid wxappid \
  --private-key build/private.key \
  --version 1.2.3 \
  --desc "修复bug"
```

## 构建流程

1. **环境检查**: 验证必要文件是否存在
2. **依赖安装**: 自动检查并安装项目依赖
3. **NPM构建**: 构建小程序所需的 npm 包
4. **代码上传**: 将构建后的代码上传到微信小程序后台

## 配置说明

### private.key 文件

这是微信小程序的上传密钥文件，需要从微信公众平台获取：

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入小程序管理后台
3. 开发 → 开发管理 → 开发设置
4. 在"代码上传"部分下载密钥文件
5. 将密钥文件重命名为 `private.key` 并放置在 `build/` 目录下

### project.config.json

确保项目配置文件中的 `appid` 字段正确：

```json
{
  "appid": "wxappid",
  // ... 其他配置
}
```

## 错误处理

### 常见错误及解决方案

1. **私钥文件不存在**
   ```
   [ERROR] 私钥文件不存在: build/private.key
   ```
   解决方案：确保 `build/private.key` 文件存在且有效

2. **AppID 获取失败**
   ```
   [ERROR] 无法从project.config.json中获取appid
   ```
   解决方案：检查 `project.config.json` 中的 `appid` 字段

3. **依赖安装失败**
   ```
   [ERROR] npm install 失败
   ```
   解决方案：检查网络连接和 npm 配置

4. **代码上传失败**
   ```
   [ERROR] 代码上传失败
   ```
   解决方案：
   - 检查私钥文件是否正确
   - 确认 AppID 是否有效
   - 检查小程序代码是否有语法错误

## 高级用法

### 自定义构建配置

可以通过修改 `ci.ts` 文件来自定义构建配置：

```typescript
const config: BuildConfig = {
  appid: 'your-appid',
  privateKeyPath: 'path/to/private.key',
  projectPath: './',
  version: '1.0.0',
  desc: '自定义描述',
  robot: 1,
  enableEs7: true,
  verbose: true
};
```

### CI/CD 集成

可以在 CI/CD 平台（如 GitHub Actions、GitLab CI）中使用：

```yaml
# GitHub Actions 示例
- name: Deploy Mini Program
  run: |
    chmod +x build/deploy.sh
    ./build/deploy.sh ${{ github.run_number }} "CI自动部署"
```

## 注意事项

1. **安全性**: 私钥文件包含敏感信息，不要提交到版本控制系统
2. **版本管理**: 建议使用语义化版本号
3. **备份**: 定期备份私钥文件
4. **权限**: 确保脚本有执行权限

## 技术支持

如果遇到问题，请检查：

1. Node.js 版本是否兼容
2. 项目依赖是否正确安装
3. 网络连接是否正常
4. 微信小程序后台配置是否正确 