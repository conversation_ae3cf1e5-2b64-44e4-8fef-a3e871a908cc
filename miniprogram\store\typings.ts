/** 当前定位信息 */
export type GeoInfo = {
  /** 纬度 */
  lat: number;
  /** 经度 */
  lng: number;
  /** 最后更新时间（毫秒时间戳） */
  lastUpdatedTime?: number
};

/** 扩展的用户信息类型，包含基础认证信息和详细资料 */
export type UserInfo = {
  /** 基础认证信息 */
  openid: string;
  unionid: string;
  /** 详细用户资料 */
  avatar?: string;
  nickname?: string;
  gender?: string;
  birthday?: string;
  email?: string;
  mobile?: string;
  userId?: number;
  /** 是否已加载详细信息 */
  isDetailLoaded?: boolean;
};

export type State = {
  geoInfo: GeoInfo;
  cityInfo: {
    cityId: string;
    cityName: string;
  };
  userInfo: UserInfo;
  token: string;
}

export type Actions = {
  setUserInfo: (payload: Partial<UserInfo>) => void;
  setUserDetail: (payload: My.IUserInfoRes) => void;
  updateUserField: (field: keyof My.IUserInfoRes, value: string | number) => void;
  setGeoInfo: (payload: State['geoInfo']) => void;
  setToken: (token: string) => void;
}

export type Store = State & Actions;