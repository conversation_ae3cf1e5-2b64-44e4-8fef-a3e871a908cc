declare namespace RouteLine {
    /**
     * 线路列表请求参数
     */
	 interface RouteListParams {
        /**
         * 渠道
         */
        channel?: number;
        /**
         * 城市id
         */
        cityId?: string;
        /**
         * 行程天数
         */
        journeyDays?: string;
        /**
         * 线路名称
         */
        name?: string;
        /**
         * 页数
         */
        pageIndex: number;
        /**
         * 每页条数
         */
        pageSize: number;
        /**
         * 项目
         */
        project?: number | string;
        [property: string]: any;
    }
    
    /**
     * 线路列表响应
     */
     interface RouteListResponse {
        records: Datum[];
        /**
         * 是否还有数据
         */
        hasNext: boolean;
        [property: string]: any;
    }
    
     interface Datum {
        /**
         * 本人是否打卡过
         */
        ifCheckIn?: boolean;
        /**
         * 线路名称
         */
        name?: string;
        /**
         * 线路图片urls
         */
        picUrls?: string[];
        /**
         * 线路id
         */
        routeId?: number | string;
        /**
         * 多少人打过卡
         */
        totalCheckIn?: number;
        /**
         * 用户头像
         */
        userAvatars?: string;
        [property: string]: any;
    }

    /**
     * 线路详情响应
     */
     interface RouteDetailResponse {
        /**
         * 渠道id
         */
        channel: number[];
        /**
         * 渠道名称
         */
        channelName: string[];
        /**
         * 城市ids
         */
        cities: string[];
        /**
         * 城市名称
         */
        cityNames: string[];
        /**
         * 线路描述
         */
        description: string;
        /**
         * 行程内容
         */
        journeyInfos: JourneyInfo[];
        /**
         * 线路名称
         */
        name: string;
        /**
         * 线路图片urls
         */
        picUrls: string[];
        /**
         * 项目id
         */
        project: number[];
        projectInfos: ProjectInfo[];
        /**
         * 项目名称
         */
        projectName: string;
        /**
         * 线路id
         */
        routeId: number;
        [property: string]: any;
    }
    
     interface JourneyInfo {
        /**
         * 第N天
         */
        dayNo: number;
        locations: LocationElement[];
        [property: string]: any;
    }
    
     interface LocationElement {
        description: string;
        location: LocationLocation;
        /**
         * 地点id
         */
        locationId: number;
        locationNameInput: string;
        [property: string]: any;
    }
    
     interface LocationLocation {
        address?: string;
        /**
         * 地点分类
         */
        categoryName?: string;
        /**
         * 地点描述
         */
        description?: string;
        /**
         * 地点id
         */
        locationId?: number;
        /**
         * 地点名称
         */
        name: string;
        /**
         * 地点图片urls
         */
        picUrls?: string[];
        /**
         * 地点类型
         */
        typeName?: string;
        [property: string]: any;
    }
    
     interface ProjectInfo {
        projectIcon?: string;
        projectId?: number;
        projectName?: string;
        [property: string]: any;
    }
    
    /**
     * 区域选择响应
     */
     interface RegionSelectResponse{
        hotCities: HotCity[];
        regionList: RegionList[];
        [property: string]: any;
    }
    
     interface HotCity {
        key: string;
        type: number;
        value: string;
        [property: string]: any;
    }
    
     interface RegionList {
        letter: string;
        regionInfos: RegionInfo[];
        [property: string]: any;
    }
    
     interface RegionInfo {
        key: string;
        type: number;
        value: string;
        [property: string]: any;
    }

    /**
     * ip定位响应
     */
     interface IpLocateResponse {
        locationCode: string;
        locationName: string;
        [property: string]: any;
    }

}