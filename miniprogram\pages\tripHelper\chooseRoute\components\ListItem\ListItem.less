/* pages/tripHelper/chooseRoute/components/ListItem/ListItem.less */

.list-item {
  display: flex;
  background-color: #fff;
  border-radius: 32rpx;
  margin: 16rpx 24rpx;
  padding: 32rpx;
  position: relative;
}

.left-section {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  .select-icon {
    width: 48rpx;
    height: 48rpx;
  }
}

.right-section {
  flex: 1;
  position: relative;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  
  .route-name {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
  }
}

.route-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
  
  .route-img {
    width: 180rpx;
    height: 180rpx;
    border-radius: 8rpx;
  }
}

.friends-section {
  display: flex;
  align-items: center;
  
  .friends-avatars {
    display: flex;
    margin-right: 16rpx;
    
    .friend-avatar {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      border: 2rpx solid #fff;
      margin-left: -16rpx;
      &:first-child {
        margin-left: 0;
      }
    }
  }
  
  .friends-count {
    font-size: 24rpx;
    color: #99999E;
  }
}

  
  
  .punched-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 152rpx;
    height: 152rpx;
  }