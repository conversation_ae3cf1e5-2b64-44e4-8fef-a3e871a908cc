import { initOrderApi,createTrailOrderApi,wechatPayApi } from '../../../api/tripHelper/activityOrder'
import { formatDate,getDayOfWeek } from '../../../utils/tool'
import { AgeGroupDisplay } from '../../../enum/index'
interface PageOptions {
  id?: string | number;
}

Page({
  /** 页面的初始数据 */
  data: {
    isLoading:true,
    pageOptions:{} as PageOptions,
    orderData: null as null | ActivityOrder.OrderPageData,
    isBtmDisabled:true,   


    



    tips:[
      '活动均为主理人自行发布并组织的活动，请您注意，粗门仅为活动提供平台技术支持，活动后续事项有主理人负责组织，费用均为主理人收取。如您因活动发起、组织、撤销、下线、退款等引起的纠纷须由主理人自行解决并承担后果。',
      '请您务必确认活动的举办时间，尤其是在粗门报名展示的活动时间。若该时间与俱乐部的详情描述、活动图片内容或实际举办时间不符，请您务必自行予以充分关注，并与俱乐部主理人核实确认。粗门活动票夹显示的结束时间后24小时内作为活动争议处理反馈的异议期，逾期未收到退款争议投诉的活动，活动费用会支付给主理人，敬请留意。',
      '报名并参与活动，请您谨慎甄别主理人发起活动的身份及信息，注意主理人提示的内容，参加活动前妥善评估活动安全性及自身身体状况适合参加活动安排，确保多与线下活动时的人身安全与财产安全，若因隐瞒身体疾病或伤情故意参加活动导致的意外伤害需自行承担责任。'
    ]
  },

  /** 生命周期函数--监听页面加载 */
  onLoad(options:PageOptions) {
    const { id } = options

    // 处理id不存在的情况
    if (!id) {
      wx.displayToast({ title: '缺少活动id', icon: 'none' });
      return;
    }

    this.setData({
      pageOptions:options
    },() => {
      this.initData()
    })
  },
  // 页面初始化数据
  async initData(){
    try{
      const { pageOptions } = this.data
      const { code , isSuccess, data ,message } = await initOrderApi({id:pageOptions.id!})
      if(code === 200 && isSuccess && data && data.activityDTO){
      
        let orderData = {} as ActivityOrder.OrderPageData
        const { 
          coverImages,
          title,
          cityName,
          startTime,
          assemblyPlaceDTOS,
          allowWaiting,
          leftTicketCount,
          maxTicketCount,
          policyDTO,
          perCost,
          id } = data.activityDTO

        // 头图（取第一张）
        orderData.headImg = coverImages.length > 0 ? coverImages[0] : '';

        // 活动名称所在城市
        orderData.title = title;
        orderData.cityName = cityName;

        // 活动时间 MM.DD + (weekend)
        orderData.activitytime = formatDate(startTime,'MM.DD') + '（' + getDayOfWeek(startTime) + '）';

        // 活动地点
        orderData.assemblyPlaceList = assemblyPlaceDTOS?.map(item => ({
          ...item,
          startTime: formatDate(item.startTime, 'MM.DD'),
          isChecked:false
        })) || [];

        // 库存 （leftTicketCount）
        // allowWaiting -1未知、1允许候补、2不允许候补
        // 允许候补 -- 无库存限制 不展示
        // 不允许候补 -- 库存<5 展示
        orderData.isWaiting = allowWaiting === 1
        orderData.stock = allowWaiting === 1 ? leftTicketCount : -1;

        // 人群 价格 数量
        orderData.perList = perCost?.map(item => ({
          type: item.type,
          num: 0,
          price: item.perCost?.toFixed(2),
          typename:AgeGroupDisplay[item.type as keyof typeof AgeGroupDisplay],
        }));

        // 支付金额
        orderData.totalPrice = '0.00'

        // 退款政策
        orderData.payTimeDesc = policyDTO.payTimeDesc
        orderData.refundRuleDesc = policyDTO.refundRuleDesc
        orderData.orderRefundList = policyDTO.policyDetails?.map(item => ({
          date: item.dateDesc,
          refundProportion:item.refundProportionDesc,
          amount:item.refundAmount
        }))

        orderData.activityId = id

        this.setData({
          orderData:orderData,
          isLoading:false
        })
      }else{
        wx.displayToast({ title: message , icon: 'none' });
      }

    }catch(error){
      wx.displayToast({ title: '页面加载失败，请稍后重试', icon: 'none' });
    }
  },
  // 选择集合地点
  chooseAssemblyPlace(event: WechatMiniprogram.BaseEvent){
    const { index } = event.currentTarget.dataset;
    const { orderData } = this.data;

    if (!orderData) return;

    // 重置所有 isChecked 为 false
    const newAssemblyPlaceList = orderData.assemblyPlaceList.map((place, i) => ({
      ...place,
      isChecked: i === index // 当前点击的设为 true，其他为 false
    }));
    orderData.assemblyPlaceList = newAssemblyPlaceList;

    this.setData({
      orderData
    },() => {
      this.checkForm()
    });
  },
  // 唤起腾讯地图
  openMap(event: WechatMiniprogram.BaseEvent) {
    const { lon,lat,cityName,address } = event.currentTarget.dataset
    
    wx.openLocation({
      latitude: lat,        // 纬度，范围为-90~90，负数表示南纬
      longitude: lon,       // 经度，范围为-180~180，负数表示西经
      name: cityName,       // 位置名
      address,              // 地址的详细说明
      scale: 18,            // 地图缩放级别，整形值，范围从1~28
      success: (res) => {
        console.log('打开成功', res);
      },
      fail: (err) => {
        console.error('打开失败', err);
      }
    });
  },
  // 加减按钮
  changeNum(event: WechatMiniprogram.BaseEvent) {
    const { type, index } = event.currentTarget.dataset;
    const { orderData } = this.data;
  
    if (!orderData || !Array.isArray(orderData.perList) || typeof index !== 'number') return;
  
    const perList = [...orderData.perList];
    const currentItem = perList[index];
    const totalNum = perList.reduce((sum, item) => sum + item.num, 0);
  
    if (type === 'sub') {
      if (currentItem.num > 0) {
        currentItem.num--;
      }
    } else if (type === 'add') {
      if (totalNum >= orderData.stock) {
        wx.displayToast({ title: `最多可购买 ${orderData.stock} 张`, icon: 'none' });
        return;
      }
      currentItem.num++;
    }
  
    perList[index] = currentItem;
    this.setData({
      orderData: {
        ...orderData,
        perList
      }
    }, () => {
      this.calculateTotalprice();
      this.checkForm();
    });
  },
  // 计算总价
  calculateTotalprice(){
    const { orderData } = this.data;
    if(orderData){
      const { perList } = orderData
      const totalAmount = perList.reduce((sum, item) => {
        return sum + item.num * Number(item.price);
      }, 0);
      orderData.totalPrice = totalAmount.toFixed(2)

      this.setData({ orderData })
    }
  },
  // 校验表单
  checkForm(showToast?:boolean){
    const { orderData } = this.data
    
    if(!orderData){
      return false
    }

    const { assemblyPlaceList,perList } = orderData
    let flag:boolean = true;
    let errortxt:string = '';

    if(assemblyPlaceList?.length > 0 && !assemblyPlaceList.some(place => place.isChecked)){
      flag = false;
      errortxt = '请选择集合地点';
    }else if(perList.every(item => item.num === 0)){
      flag = false;
      errortxt = '请添加购票数量';
    }

    if(flag){
      this.setData({isBtmDisabled:false})
    }else{
      if(showToast){
        wx.displayToast({ title: errortxt, icon: 'none' });
      }
      this.setData({isBtmDisabled:true})
    }
    
    return flag
  },
  // 表单参数
  getsubmitParams(){
    let submitParams = {} as ActivityOrder.OrderSubmitParams
    const { orderData } = this.data

    const { activityId,assemblyPlaceList,totalPrice,isWaiting,perList } = orderData!

    submitParams.activityId = activityId
    submitParams.paymentAmount = Number(totalPrice)
    submitParams.paymentType = 0     // 支付方式 0-微信 1-支付宝
    submitParams.isWaiting = isWaiting ? 1 : 0

    const checkedPlace = assemblyPlaceList.find(item => item.isChecked);
    if (checkedPlace) {
      submitParams.pointId = checkedPlace.id;
      submitParams.pointName = checkedPlace.placeName;
    }

    perList.forEach(item => {
      if(item.type === 0){
        submitParams.oldPersonPrice = Number(item.price)
        submitParams.oldPersonCount = item.num
      }else if(item.type === 1){
        submitParams.adultPrice = Number(item.price)
        submitParams.adultCount = item.num
      }else if(item.type === 2){
        submitParams.childrenPrice = Number(item.price)
        submitParams.childrenCount = item.num
      }
    })

    return submitParams;

  },
  // 提交订单
  async submit(){
    
    if(!this.checkForm(true)){
      return
    }

    try{
      const param = this.getsubmitParams()

      const { code , isSuccess, data ,message } = await createTrailOrderApi(param)
      if(code === 200 && isSuccess && data){
        const { orderNo,paymentAmount } = data
        this.goPay(orderNo,paymentAmount)
      }else{
        wx.displayToast({ title: message, icon: 'none' });
      }

    }catch(error){
      wx.displayToast({ title: '下单失败', icon: 'none' });
    }
  },
  // 支付
  async goPay(orderNo:number|string,paymentAmount:number){
    try{
      const { activityId,title  } = this.data.orderData!
      const param = {
        orderNo,
        activityId,
        orderPayMoney:paymentAmount.toFixed(2),
        goodsName:title
      }
      const { code , isSuccess, data ,message } = await wechatPayApi(param)

      if(code === 200 && isSuccess && data){
        const { timeStamp,nonceStr,prepayId,signType,paySign } = data
        wx.requestPayment({
          timeStamp: timeStamp,
          nonceStr: nonceStr,
          package: prepayId, 
          signType: signType,
          paySign: paySign,
          success : (res) => {
            console.log('支付成功:', res);
            this.goOrderDetail(orderNo)
          },
          fail : (err) => {
            wx.displayToast({ title: err?.errMsg, icon: 'none' });
            this.goOrderDetail(orderNo)
          }
        });
      }else{
        wx.displayToast({ title: message, icon: 'none' });
      }

    }catch(error){
      wx.displayToast({ title: '请稍后重试～', icon: 'none' });
    }
  },
  // 跳转订单详情页
  goOrderDetail(orderNo:number|string){
    const { activityId  } = this.data.orderData!

    wx.navigateTo({ 
      url : `/pages/tripHelper/activityOrderDetail/index?orderNo=${orderNo}&activityId=${activityId}`
    })
  }

})