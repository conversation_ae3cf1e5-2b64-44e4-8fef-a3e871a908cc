// pages/tripHelper/order/index.ts
import { getTrailOrderList } from '../../../api/my';
import { StatusDisplayType } from '../../../components/statusDisplay/types';



Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 订单列表数据
    orderList: [] as My.ITrailOrderItem[],

    // 分页相关
    pageIndex: 1,
    pageSize: 10,
    hasMore: false,

    // 加载状态
    loading: false,
    loadingMore: false,
    isEmpty: false,

    // 错误状态
    hasError: false,
    showEmptyState: false,
    emptyStateType: 'data-empty' as StatusDisplayType
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadOrderList(true);
  },

  /**
   * 加载订单列表
   * @param isRefresh 是否为刷新操作
   */
  async loadOrderList(isRefresh = false) {
    try {
      // 如果是重新加载，重置分页和状态
      if (isRefresh) {
        this.setData({
          pageIndex: 1,
          hasMore: true,
          loading: true,
          isEmpty: false,
          hasError: false,
          showEmptyState: false
        });
      } else {
        this.setData({ loadingMore: true });
      }

      const res = await getTrailOrderList({
        pageIndex: this.data.pageIndex,
        pageSize: this.data.pageSize
      });
      if (res.code === 200 && res.data) {
        // 直接使用接口返回的数据结构
        const { trailOrderItemList } = res.data;

        let orderList: My.ITrailOrderItem[];
        if (isRefresh) {
          orderList = trailOrderItemList;
        } else {
          orderList = [...this.data.orderList, ...trailOrderItemList];
        }

        // 根据分页信息判断是否还有更多数据
        const { currentPage, totalPage } = res.data;
        const hasMore = currentPage < totalPage;
        const isEmpty = orderList.length === 0;

        this.setData({
          orderList,
          // 只有在有数据且确实有更多数据时才设置hasMore为true
          // 如果没有更多数据，不设置hasMore，让组件根据enable-load-more自动处理
          ...(isEmpty ? {} : hasMore ? { hasMore: true } : {}),
          isEmpty,
          pageIndex: this.data.pageIndex + 1,
          loading: false,
          loadingMore: false,
          hasError: false,
          showEmptyState: isEmpty,
          emptyStateType: StatusDisplayType.DataEmpty
        });
      } else {
        // API返回错误
        if (isRefresh || this.data.orderList.length === 0) {
          this.setData({
            hasError: true,
            showEmptyState: true,
            emptyStateType: StatusDisplayType.NetworkError,
            loading: false,
            loadingMore: false
          });
        } else {
          wx.displayToast({ title: res.message || '获取订单列表失败' });
          this.setData({
            loading: false,
            loadingMore: false
          });
        }
      }
    } catch (error) {
      console.error('获取订单列表失败:', error);

      // 网络错误处理
      if (isRefresh || this.data.orderList.length === 0) {
        this.setData({
          hasError: true,
          showEmptyState: true,
          emptyStateType: StatusDisplayType.NetworkError,
          loading: false,
          loadingMore: false
        });
      } else {
        wx.displayToast({ title: '网络错误，请重试' });
        this.setData({
          loading: false,
          loadingMore: false
        });
      }
    }
  },





  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }
    this.loadOrderList(false);
  },

  /**
   * 去支付按钮点击事件
   */
  onGoPay(e: WechatMiniprogram.TouchEvent) {
    const { activityId, orderNo } = e.currentTarget.dataset;
    console.log('去支付，活动ID:', activityId, '订单号:', orderNo);

    // 跳转到订单详情页面
    wx.navigateTo({
      url: `/pages/tripHelper/activityOrderDetail/index?orderNo=${orderNo}&activityId=${activityId}`
    });
  },

  /**
   * 申请退款按钮点击事件
   */
  onApplyRefund(e: WechatMiniprogram.TouchEvent) {
    const { activityId, orderNo } = e.currentTarget.dataset;
    console.log('申请退款，活动ID:', activityId, '订单号:', orderNo);

    // 跳转到订单详情页面
    wx.navigateTo({
      url: `/pages/tripHelper/activityOrderDetail/index?orderNo=${orderNo}&activityId=${activityId}`
    });
  },

  /**
   * 状态显示组件按钮点击事件
   */
  onStatusDisplayButtonClick(e: WechatMiniprogram.CustomEvent) {
    const { type } = e.detail;
    console.log('状态显示组件按钮点击:', type);

    if (type === StatusDisplayType.NetworkError || type === 'network-error') {
      // 网络错误时重新加载数据
      this.loadOrderList(true);
    }
  }
});
