// components/userInfoModal/index.ts
import { getImageUrl } from '../../utils/images';
import { updateUserInfo, uploadAvatarImage } from '../../api/my';

interface UserInfoData {
  avatarUrl: string;
  nickname: string;
  gender: '0' | '1' | '2' | ''; // 0-未知，1-男，2-女
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 控制弹框显示/隐藏
    visible: {
      type: Boolean,
      value: false
    },
    // 手机号（从登录页面传入）
    phoneNumber: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 头像URL
    avatarUrl: '',
    // 昵称
    nickname: '',
    // 性别：0-未知，1-男，2-女
    gender: '' as '0' | '1' | '2' | '',
    // 是否可以提交
    canSubmit: false,
    // 提交中状态
    submitting: false,
    // 图标路径
    avatarPicIcon: getImageUrl('login/pic.png'),
    closeIcon: getImageUrl('login/close.png'),
    deleteIcon: getImageUrl('user/feedback_close.png'),
    femaleIcon: getImageUrl('login/women.png'),
    femaleActiveIcon: getImageUrl('login/women_act.png'),
    maleIcon: getImageUrl('login/men.png'),
    maleActiveIcon: getImageUrl('login/men_act.png')
  },

  /**
   * 数据监听器
   */
  observers: {
    'visible'(visible: boolean) {
      console.log('用户信息弹框 visible 变化:', visible);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('用户信息弹框组件已挂载');
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 弹框显示/隐藏变化
     */
    onVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      const { visible } = e.detail;

      if (!visible) {
        // 弹框关闭时重置数据
        this.resetData();
        this.triggerEvent('close');
      }
    },

    /**
     * 关闭弹框
     */
    onClose() {
      this.triggerEvent('close');
    },

    /**
     * 选择头像回调
     */
    onChooseAvatar(e: WechatMiniprogram.CustomEvent<{ avatarUrl: string }>) {
      const { avatarUrl } = e.detail;
      console.log('获取到头像:', avatarUrl);

      if (avatarUrl) {
        this.setData({
          avatarUrl
        });

        wx.showToast({
          title: '头像获取成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        console.log('用户取消选择头像');
        wx.showToast({
          title: '请选择头像',
          icon: 'none'
        });
      }
    },

    /**
     * 删除头像
     */
    onDeleteAvatar() {
      this.setData({
        avatarUrl: ''
      });

      wx.showToast({
        title: '头像已删除',
        icon: 'success',
        duration: 1500
      });
    },

    /**
     * 昵称输入
     */
    onNicknameInput(e: WechatMiniprogram.Input) {
      const nickname = e.detail.value;
      this.setData({ nickname });
    },

    /**
     * 性别选择
     */
    onGenderSelect(e: WechatMiniprogram.TouchEvent) {
      const genderType = e.currentTarget.dataset.gender as 'male' | 'female';
      // 转换为后端需要的格式：1-男，2-女
      const gender = genderType === 'male' ? '1' : '2';
      this.setData({ gender });
    },

    /**
     * 提交用户信息
     */
    async onSubmit() {
      const { nickname, gender, avatarUrl } = this.data;
      const { phoneNumber } = this.properties;

      if (this.data.submitting) {
        return;
      }

      // 验证必填字段
      if (!avatarUrl) {
        wx.showToast({
          title: '请上传头像',
          icon: 'none'
        });
        return;
      }

      if (!nickname.trim()) {
        wx.showToast({
          title: '请输入昵称',
          icon: 'none'
        });
        return;
      }

      if (!gender) {
        wx.showToast({
          title: '请选择性别',
          icon: 'none'
        });
        return;
      }

      this.setData({ submitting: true });

      try {
        // 先上传头像，获取头像URL
        let finalAvatarUrl = avatarUrl;
        if (avatarUrl && !avatarUrl.startsWith('http')) {
          // 如果是本地路径，需要先上传
          const uploadResult = await uploadAvatarImage(avatarUrl);
          if (uploadResult.isSuccess && uploadResult.data) {
            // 实际返回的是数组，但类型定义是对象，需要处理兼容性
            finalAvatarUrl = Array.isArray(uploadResult.data)
              ? uploadResult.data[0]
              : (uploadResult.data as any).url;
          } else {
            throw new Error(uploadResult.message || '头像上传失败');
          }
        }

        // 调用更新用户信息接口
        const result = await updateUserInfo({
          nickname: nickname.trim(),
          avatar: finalAvatarUrl,
          gender,
        });

        this.setData({ submitting: false });

        if (result.isSuccess) {
          wx.showToast({
            title: '个人信息保存成功',
            icon: 'success'
          });

          // 构造用户信息数据
          const userInfo: UserInfoData = {
            avatarUrl,
            nickname: nickname.trim(),
            gender
          };

          // 触发成功事件，传递用户信息
          this.triggerEvent('success', {
            phoneNumber,
            ...userInfo
          });

          // 延迟关闭弹框
          setTimeout(() => {
            this.triggerEvent('close');
          }, 1500);
        } else {
          wx.showToast({
            title: result.message || '保存失败，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('更新用户信息失败:', error);
        this.setData({ submitting: false });
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    },

    /**
     * 重置数据
     */
    resetData() {
      this.setData({
        avatarUrl: '',
        nickname: '',
        gender: '' as '0' | '1' | '2' | '',
        submitting: false
      });
    }
  }
});
