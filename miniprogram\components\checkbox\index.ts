// components/checkboxAgreement/index.ts
import { getImageUrl } from '../../utils/images';

/**
 * 单选框组件
 * 纯粹的单选框，只负责勾选状态
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /** 是否已勾选 */
    value: {
      type: Boolean,
      optionalTypes: [String, Number],
      value: false
    },
    /** 自定义样式类名 */
    customClass: {
      type: String,
      value: ''
    },
    /** 尺寸 */
    size: {
      type: Number || String,
      value: 36
    },
    trueValue: {
      type: Boolean,
      optionalTypes: [String, Number],
      value: true
    },
    falseValue: {
      type: Boolean,
      optionalTypes: [String, Number],
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 勾选框图标
    checkboxIcon: getImageUrl('circle.png'),
    checkboxActiveIcon: getImageUrl('circle_act.png')
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换勾选状态
     */
    onToggle() {
      const newValue = this.data.value === this.data.trueValue ? this.data.falseValue : this.data.trueValue;
      // 触发change事件，通知父组件状态变化
      this.triggerEvent('change', {
        value: newValue
      });
    }
  }
});
