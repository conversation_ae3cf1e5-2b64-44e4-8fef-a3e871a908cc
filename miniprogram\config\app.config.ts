const { env, version } = (function () {
  const {
    miniProgram: { envVersion, version: _version },
  } = wx.getAccountInfoSync();

  if (envVersion === 'develop' || envVersion === 'trial') {
    return {
      env: 'dev',
      version: _version,
    };
  }

  return { env: 'prod', version: _version };
})();

const config: Record<typeof env, string> = {
  dev: 'https://api.test.aitrip123.com',
  // dev: "http://*************:8083",  // 开发环境使用 http，需要在开发者工具中关闭域名校验
  prod: 'https://api.aitrip123.com',  // 生产环境必须使用 https
};

const appConfig = {
  baseUrl: config[env],
  defaultHeaders: {
    /** 小程序渠道标识 */
    'HT-Platform': 4,
    'HT-AppVersion': version || '1.0.0',
    // "HT-SystemVersion": ""
  },
  requestTimeout: 10000,
  /** 运行环境 */
  env,
  /** 静态资源目录 */
  staticBaseUrl: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota'
};

// console.info('appConfig:\n', appConfig);

export default appConfig;
