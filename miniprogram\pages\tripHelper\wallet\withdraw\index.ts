import { getImageUrl } from '../../../../utils/images'
import { getWithdrawLimitation, withdrawTransfer } from '../../../../api/wallet'

Page({
  data: {
    // 提现金额标题
    withdrawTitle: '提现金额',

    // 当前提现金额（可编辑）
    withdrawAmount: 0,

    // 提示文本（从接口获取）
    limitText: '加载中...',

    // 最大提现金额数值（用于校验）
    maxLimitationAmount: 0,

    // 是否显示姓名输入框（没有姓名时显示）
    showNameInput: false as boolean,

    // 是否显示提现至微信栏目（有姓名时显示）
    showWithdrawMethod: false as boolean,

    // 姓名输入值
    userName: '',

    // 从接口获取的用户姓名
    userNameFromApi: '',

    // 微信图标
    wechatIcon: getImageUrl('tripHelper/wx.png'),

    // 提现方式信息
    withdrawMethod: {
      title: '提现至微信',
      account: '已绑定：m***to'
    },

    // 加载状态
    loading: false
  },

  onLoad(options: { amount?: string }) {
    // 获取上个页面传递的默认金额
    if (options.amount) {
      this.setData({
        withdrawAmount: parseFloat(options.amount) || 0
      });
    }

    // 页面加载时获取提现限制信息
    this.loadWithdrawLimitation();
  },

  /**
   * 加载提现限制信息
   */
  async loadWithdrawLimitation() {
    try {
      this.setData({ loading: true });

      const response = await getWithdrawLimitation();

      if (response.code === 200 && response.data) {
        const hasUserName: boolean = !!(response.data.userName && response.data.userName.trim() !== '');

        // 从 maxLimitation 字符串中提取数值（假设格式类似 "2000" 或包含数字）
        const maxAmountMatch = response.data.maxLimitation.match(/\d+(\.\d+)?/);
        const maxAmount = maxAmountMatch ? parseFloat(maxAmountMatch[0]) : 2000;

        this.setData({
          limitText: maxAmount.toString(),
          maxLimitationAmount: maxAmount,
          userNameFromApi: response.data.userName || '',
          showNameInput: !hasUserName, // 没有姓名时显示输入框
          showWithdrawMethod: hasUserName, // 有姓名时显示提现至微信栏目
          withdrawMethod: {
            title: '提现至微信',
            account: hasUserName ? `已绑定：${response.data.userName}` : '已绑定：m***to'
          }
        });
      } else {
        throw new Error(response.message || '获取提现限制信息失败');
      }
    } catch (error: unknown) {
      console.error('获取提现限制信息失败:', error);
      wx.showToast({
        title: '获取提现信息失败',
        icon: 'none'
      });

      // 设置默认值
      this.setData({
        limitText: '2000',
        maxLimitationAmount: 2000,
        showNameInput: true, // 默认显示姓名输入框
        showWithdrawMethod: false, // 默认不显示提现至微信栏目
        userNameFromApi: ''
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 提现金额输入事件
   */
  onAmountInput(e: WechatMiniprogram.Input) {
    const value = parseFloat(e.detail.value) || 0;
    this.setData({
      withdrawAmount: value
    });
  },

  /**
   * 姓名输入事件
   */
  onNameInput(e: WechatMiniprogram.Input) {
    this.setData({
      userName: e.detail.value
    });
  },

  /**
   * 点击提现按钮
   */
  onWithdrawClick() {
    // 1. 空值校验
    if (!this.data.withdrawAmount || this.data.withdrawAmount <= 0) {
      wx.showToast({
        title: '请输入提现金额',
        icon: 'none'
      });
      return;
    }

    // 2. 验证金额格式（最多两位小数）
    const amountStr = this.data.withdrawAmount.toString();
    const decimalMatch = amountStr.match(/\.(\d+)$/);
    if (decimalMatch && decimalMatch[1].length > 2) {
      wx.showToast({
        title: '金额最多保留两位小数',
        icon: 'none'
      });
      return;
    }

    // 3. 验证金额不能超过最大限制
    if (this.data.withdrawAmount > this.data.maxLimitationAmount) {
      wx.showToast({
        title: `提现金额不能超过￥${this.data.maxLimitationAmount}`,
        icon: 'none'
      });
      return;
    }

    // 4. 如果需要姓名，验证姓名是否填写
    if (this.data.showNameInput && !this.data.userName.trim()) {
      wx.showToast({
        title: '请填写姓名',
        icon: 'none'
      });
      return;
    }

    // 5. 直接执行提现，不需要二次确认
    this.performWithdraw();
  },

  /**
   * 执行提现操作
   */
  async performWithdraw() {
    try {
      wx.showLoading({
        title: '提现中...'
      });

      // 准备提现参数
      const finalUserName = this.data.userNameFromApi || this.data.userName.trim() || '微信用户';
      const transferParams: Wallet.IWithdrawTransferRequest = {
        transferMoney: this.data.withdrawAmount.toFixed(2), // 确保金额格式正确
        userName: finalUserName // 优先使用接口返回的姓名，其次使用用户输入的姓名
      };

      // 调用提现接口
      const response = await withdrawTransfer(transferParams);

      wx.hideLoading();

      if (response.code === 200) {
        // 提现成功
        wx.redirectTo({
          url: `/pages/tripHelper/wallet/withdrawResult/index?type=success&amount=${this.data.withdrawAmount}`
        });
      } else {
        // 提现失败
        wx.showModal({
          title: '提现失败',
          content: response.message || '提现失败，请稍后重试',
          showCancel: false,
          success: () => {
            // 可以选择跳转到失败页面或者留在当前页面
            wx.redirectTo({
              url: `/pages/tripHelper/wallet/withdrawResult/index?type=fail&amount=${this.data.withdrawAmount}&reason=${encodeURIComponent(response.message || '提现失败')}`
            });
          }
        });
      }
    } catch (error: unknown) {
      wx.hideLoading();
      console.error('提现失败:', error);

      // 网络错误或其他异常
      const errorMessage = error instanceof Error ? error.message : '网络异常，请稍后重试';
      wx.showModal({
        title: '提现失败',
        content: errorMessage,
        showCancel: false,
        success: () => {
          wx.redirectTo({
            url: `/pages/tripHelper/wallet/withdrawResult/index?type=fail&amount=${this.data.withdrawAmount}&reason=${encodeURIComponent(errorMessage)}`
          });
        }
      });
    }
  }
})
