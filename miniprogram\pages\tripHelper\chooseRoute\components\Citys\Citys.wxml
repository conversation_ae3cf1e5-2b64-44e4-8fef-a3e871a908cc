<!--pages/tripHelper/chooseRoute/components/Citys/Citys.wxml-->
<scroll-view
  class="citys-scroll"
  scroll-y="true"
  scroll-with-animation="true"
  scroll-into-view="{{scrollTargetId}}"
>
  <view class="city-selector">
    <!-- 当前位置区块 -->
    <view class="current-location-section">
      <view class="section-title">当前位置</view>
      <view class="current-location-item {{locationStatus === 'success' ? 'success' : ''}} {{locationCity.locationName ? 'line-flex' : ''}}" bindtap="selectCurrentLocation">
        <view class="location-item-content">
          <t-icon name="location" size="36rpx" />
          <text class="location-text {{locationStatus === 'loading' ? 'loading' : ''}}">{{currentLocation}}</text>
        </view>
        <image wx:if="{{locationCity.locationName}}" class="target-iocn" src="{{staticBaseUrl}}/tripHelper/chooseRoute/targetIocn.png" catchtap="getCurrentLocation" />
      </view>
    </view>

    <!-- 历史记录区块 -->
    <view class="history-section" wx:if="{{historyList.length > 0}}">
      <view class="section-header">
        <text class="section-title">历史记录</text>
        <view class="clear-btn" bindtap="clearHistory">
          <image class="clear-icon" src="{{staticBaseUrl}}/tripHelper/chooseRoute/deleteIcon.png" />
          <text class="clear-text">清空</text>
        </view>
      </view>
      <view class="history-tags-grid">
        <view
          class="city-tag {{item.key === selectedCity.key ? 'selected' : ''}}"
          wx:for="{{historyList}}"
          wx:key="item.key"
          data-key="{{item.key}}"
          data-value="{{item.value}}"
          bindtap="selectCity"
        >
          <text class="city-tag-text">{{item.value}}</text>
        </view>
      </view>
    </view>

    <!-- 热门目的地区块 -->
    <view class="hot-cities-section" id="scroll-to-hot">
      <view class="section-title">热门目的地</view>
      <view class="city-tags-grid">
        <view
          class="city-tag {{item.key === selectedCity.key ? 'selected' : ''}}"
          wx:for="{{hotCities}}"
          wx:key="item.key"
          data-key="{{item.key}}"
          data-value="{{item.value}}"
          bindtap="selectCity"
        >
          <text class="city-tag-text">{{item.value}}</text>
        </view>
      </view>
    </view>

    <!-- 城市列表区块 -->
    <view class="city-list-section">
      <view 
        class="city-letter-group city-letter-{{item.letter}}"
        wx:for="{{regionList}}"
        wx:key="letter"
        id="scroll-to-{{item.letter}}"
      >
        <view class="letter-title">{{item.letter}}</view>
        <view class="city-list-content">
          <view
            class="city-item {{city.key === selectedCity.key ? 'selected' : ''}}"
            wx:for="{{item.regionInfos}}"
            wx:for-item="city"
            wx:key="city.key"
            data-key="{{city.key}}"
            data-value="{{city.value}}"
            bindtap="selectCity"
          >
            <text class="city-item-text">{{city.value}}</text>
          </view>
          <view class="city-divider" wx:if="{{index < regionList.length - 1}}"></view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

<!-- 右侧字母索引 -->
<view class="alphabet-nav" wx:if="{{regionList.length > 0}}">
  <view
    class="alphabet-item"
    wx:for="{{alphabetList}}"
    wx:key="index"
    data-letter="{{item}}"
    bindtap="onAlphabetClick"
  >
    <text class="alphabet-text">{{item}}</text>
  </view>
</view>