<!--pages/order/list/components/customHeader/index.wxml-->
<view class="custom-header">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 头部内容 -->
  <view class="header-content">
    <!-- 返回按钮 -->
    <view class="back-btn" bindtap="onBackClick">
      <t-icon name="chevron-left" size="48rpx" color="#333" />
    </view>
    
    <!-- 搜索框 -->
    <view class="search-container" bindtap="onSearchClick">
      <image src="{{searchIcon}}" style="width: 32rpx; height: 32rpx;" />
      <text class="search-placeholder" wx:if="{{!currentKeyword}}">搜索订单</text>
      <text class="search-keyword" wx:else>{{currentKeyword}}</text>
    </view>

    <!-- 筛选按钮 -->
    <view class="filter-btn" bindtap="onFilterClick">
      <image src="{{selectIcon}}" style="width: 40rpx; height: 40rpx;" />
    </view>
  </view>
</view>
