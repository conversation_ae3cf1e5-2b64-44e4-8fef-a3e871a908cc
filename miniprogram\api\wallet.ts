import { request } from '../utils/request';

/**
 * 获取钱包余额
 * 用于获取当前登录用户的钱包余额信息，包括可提现金额、待结算佣金、本月收入
 *
 * @returns Promise<Request.IResponseResult<Wallet.IBalanceData>> 返回钱包余额信息
 */
export const getWalletBalance = () => request<Wallet.IBalanceData>({
  showLoading: false,
  method: 'GET',
  url: '/hotel/user/wallet/balance'
});

/**
 * 获取提现记录
 * 用于获取当前登录用户的提现记录列表，支持分页查询
 *
 * @param params 请求参数，包含页码和每页大小
 * @returns Promise<Request.IResponseResult<Wallet.IRecordsData>> 返回提现记录列表
 */
export const getWithdrawRecords = (params: Wallet.IRecordsReq) => request<Wallet.IRecordsData>({
  showLoading: false,
  method: 'GET',
  url: '/hotel/user/wallet/records',
  data: params
});

/**
 * 获取用户活动订单列表
 * 用于获取当前登录用户的所有活动订单汇总信息，在钱包页面显示交易记录
 *
 * @param params 分页参数，包含页码和每页大小
 * @returns Promise<Request.IResponseResult<Wallet.IActivityOrderListData>> 返回分页的活动订单列表
 */
export const getActivityOrderList = (params: Wallet.IActivityOrderListReq) => request<Wallet.IActivityOrderListData>({
  showLoading: false,
  method: 'POST',
  url: '/hotel/user/order/atList',
  data: params
});

/**
 * 获取活动参与人订单信息
 * 用于获取活动组织者的参与人订单信息，按集合地分组展示
 *
 * @param params 请求参数，包含活动ID
 * @returns Promise<Request.IResponseResult<Wallet.IActivityParticipantOrderData>> 返回活动参与人订单信息
 */
export const getActivityParticipantOrder = (params: Wallet.IActivityParticipantOrderReq) => request<Wallet.IActivityParticipantOrderData>({
  showLoading: false,
  method: 'GET',
  url: '/hotel/user/order/record',
  data: params
});

/**
 * 获取微信提现限制信息
 * 用于获取当前用户的提现限制提示和配置信息
 *
 * @returns Promise<Request.IResponseResult<Wallet.IWithdrawLimitationResponse>> 返回提现限制信息
 */
export const getWithdrawLimitation = () => request<Wallet.IWithdrawLimitationResponse>({
  showLoading: false,
  method: 'POST',
  url: '/hotel/payment/wechatQuery/v1/limitation'
});

/**
 * 用户提现接口(商家转账)
 * 通过微信商家转账的方式将钱包余额转账到用户的微信账户
 *
 * @param params 提现参数，包含提现金额和用户姓名
 * @returns Promise<Request.IResponseResult<Wallet.IWithdrawTransferResponse>> 返回提现结果
 */
export const withdrawTransfer = (params: Wallet.IWithdrawTransferRequest) => request<Wallet.IWithdrawTransferResponse>({
  showLoading: true,
  method: 'POST',
  url: '/hotel/payment/wechatTransferBatch/v1/transferBills',
  data: params
});
