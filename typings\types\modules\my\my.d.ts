declare enum ESmsType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
}
declare namespace My {
  /**
   * 用户信息接口返回数据结构
   * 包含用户的基本信息，如头像、昵称、生日等
   */
  interface IUserInfoRes {
    /** 用户头像URL，为空时使用默认头像 */
    avatar: string
    /** 用户生日，格式：YYYY-MM-DD */
    birthday?: string
    /** 用户邮箱 */
    email?: string
    /** 用户性别：0-未知，1-男，2-女 */
    gender: string
    /** 用户手机号 */
    mobile?: string
    /** 用户昵称 */
    nickname: string
    /** 用户ID */
    userId?: number
  }
  /**
   * 用户信息事件传递类型
   * avatar事件只带avatar，nickname事件只带nickname
   */
  type UserInfoUpdateEvent =
    | ({ type: 'avatar' } & Pick<IUserInfoRes, 'avatar'>)
    | ({ type: 'nickname' } & Pick<IUserInfoRes, 'nickname'>);
  /**
   * 更新用户信息请求数据结构
   * 从 IUserInfoRes 中排除系统字段和敏感信息
   */
  type IUpdateUserInfoReq = Omit<IUserInfoRes, 'email' | 'mobile' | 'userId'>

  // 注销接口入参
  interface IFetchLogoutReq {
    // 验证码
    smsCode: string
    // 验证码类型
    smsType: ESmsType
  }

  // 上传图片接口返回值
  interface IUploadImageRes {
    // 图片CDN访问地址
    url: string
  }

  // 上传图片接口入参
  interface IUploadImageReq {
    // 图片文件路径
    files: string
    // 业务类型：1-反馈图片，2-用户头像，3-酒店图片，4-房间图片，5-评论图片，6-身份证件图片，99-其他图片
    type: number
  }

  // 提交反馈接口入参
  interface ISubmitFeedbackReq {
    // 反馈类型（1:功能故障 2:产品建议 3:订单问题 4:其它反馈）
    feedbackType: number
    // 反馈内容（文本）
    content: string
    // 图片URL数组
    images?: string[]
  }

  // 提交反馈接口返回值
  interface ISubmitFeedbackRes {
    // 反馈唯一标识
    feedbackId: string
    // 反馈状态（pending=待处理，resolved=已解决）
    status: string
  }

  // 获取用户行文的接口入参
  interface IFavoritesReq {
    // 1:收藏 2:看过 3:住过 4:赞过
    behaviorType: string
    // 分页页码，默认为 1
    page: number
    // 每页数据量，默认为 10
    pageSize: number
  }

  // 获取用户行文的接口出参
  // interface IFavoritesRes {
  //   // 酒店-1, 机票-2等
  //   type: string
  //   // 1:收藏 2:看过 3:住过 4:赞过
  //   behaviorType: string
  //   // 分页页码，默认为 1
  //   page: number
  //   // 每页数据量，默认为 10
  //   pageSize: number
  // }

  // 取消收藏接口入参
  interface IFavoritesBatchReq {
    // 酒店-1, 机票-2等
    type: string
    // 1:收藏 2:看过 3:住过 4:赞过
    behaviorType: string
    // 关联业务项 ID（如航班 ID）
    itemIds: string[]
  }

  // ===== 签到相关 =====

  /**
   * 签到请求参数
   */
  interface ISignInReq {
    /** 活动ID */
    activityId: number;
    /** 订单ID */
    orderId: number;
  }

  /**
   * 签到响应数据
   */
  interface ISignInData {
    // 签到成功后的数据，目前为空对象
  }

  /**
   * 签到响应
   */
  interface ISignInRes {
    code: number;
    message: string;
    data: ISignInData;
  }
}
