/* 悬浮按钮组件样式 */

/* 悬浮按钮 */
.floating-btn {
  position: fixed;
  right: 48rpx;
  bottom: 200rpx;
  z-index: 50;

  /* 编辑按钮 */
  .floating-edit-btn {
    width: 112rpx;
    height: 112rpx;
    background-color: #1976d2;
    border-radius: 56rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
    transition: all 0.3s ease;

    .floating-btn-text {
      font-size: 20rpx;
      color: #fff;
      margin-top: 4rpx;
    }

    &:active {
      transform: scale(0.95);
      background-color: #1565c0;
    }
  }

  /* 取消按钮 */
  .floating-cancel-btn {
    width: 112rpx;
    height: 112rpx;
    background-color: #ff4757;
    border-radius: 56rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
    transition: all 0.3s ease;

    .floating-btn-text {
      font-size: 20rpx;
      color: #fff;
      margin-top: 4rpx;
    }

    &:active {
      transform: scale(0.95);
      background-color: #e84057;
    }
  }
}
