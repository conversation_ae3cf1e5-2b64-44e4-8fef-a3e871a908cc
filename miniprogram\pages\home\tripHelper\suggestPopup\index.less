/* components/suggestPopup/index.less */
.suggest-popup {
  background: #ffffff;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 24rpx;

    .popup-title {
      flex: 1;
      text-align: center;
      color: #33333E;
      font-size: 36rpx;
      font-weight: 500;
    }

    .close-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .close-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .popup-content {
    flex: 1;
    padding:0 40rpx;
    overflow-y: auto;

    .section-title {
      color: #33333E;
      font-size: 32rpx;
      font-weight: 500;
      margin-top: 62rpx;
      margin-bottom: 16rpx;
      display: block;
    }

    .suggestion-section {
      margin-bottom: 48rpx;

      .suggestion-input-wrapper {
        position: relative;

        .custom-placeholder {
          position: absolute;
          top: 32rpx;
          left: 32rpx;
          right: 32rpx;
          pointer-events: none;
          z-index: 1;

          .placeholder-line {
            display: block;
            color: #CCCCCE;
            font-size: 28rpx;
            line-height: 1.5;
            margin-bottom: 8rpx;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .suggestion-input {
          width: 100%;
          min-height: 380rpx;
          padding: 32rpx;
          border-radius: 24rpx;
          font-size: 28rpx;
          color: #333333;
          background: #F9F9F9;
          box-sizing: border-box;

          &::placeholder {
            color: #CCCCCE;
            line-height: 1.5;
          }
        }

        .char-count {
          position: absolute;
          bottom: 12rpx;
          right: 12rpx;
          color: #999999;
          font-size: 24rpx;
          z-index: 2;
        }
      }
    }

    .upload-section {
      // 图片上传组件的样式由组件内部处理
    }
  }

  .popup-footer {
    margin-top: 32rpx;
    padding: 16rpx 32rpx 68rpx;
    border-top: 1rpx solid #F0F0F0;

    .submit-btn {
      width: 100%;
      height: 108rpx;
      background: #0198FF;
      color: #ffffff;
      font-size: 32rpx;
      border-radius: 120rpx;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background: #0198FF;
      }
    }
  }
}
