<!--pages/home/<USER>/index.wxml-->
<view class="trip-helper-container">
  <!-- 顶部背景区域 -->
  <view class="hero-section">
    <image class="bg-image" src="{{bgImage}}" mode="aspectFill"></image>
    <view class="hero-content">
      <view class="hero-text">我们的旅行神器有你参与才完美</view>
      <button class="suggest-btn" bind:tap="onSuggestClick">我来支一招</button>
    </view>
  </view>

  <!-- 领游的产品计划 -->
  <view class="product-plan-section">
    <view class="section-title">
      <image class="title-left" src="{{titleLeftImage}}" mode="aspectFit"></image>
      <text class="title-text">领游的产品计划</text>
      <image class="title-right" src="{{titleRightImage}}" mode="aspectFit"></image>
    </view>

    <view class="plan-list">
      <view wx:for="{{productPlans}}" wx:key="id" class="plan-item {{index === productPlans.length - 1 ? 'last-item' : ''}}" data-id="{{item.id}}" bind:tap="onProductPlanClick">
        <!-- 左侧图标 -->
        <view class="plan-icon">
          <image src="{{item.icon}}" mode="aspectFit"></image>
        </view>

        <view class="plan-content">
          <view class="plan-header">
            <image class="plan-title-img" src="{{item.titleImage}}" mode="aspectFit" style="width: {{item.titleImageWidth}};"></image>
            <view class="plan-time">{{item.time}}</view>
          </view>
          <view class="plan-desc">{{item.description}}</view>
          <view class="plan-images">
            <view wx:for="{{item.images}}" wx:key="url" wx:for-item="img" class="plan-img-item">
              <image src="{{img.url}}" mode="aspectFill" class="plan-img"></image>
              <text class="plan-img-desc">{{img.desc}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能更新区域 -->
  <view class="feature-section">
    <view class="section-title">
      <image class="title-left" src="{{titleLeftImage}}" mode="aspectFit"></image>
      <text class="title-text">功能更新ing，有点意思的都藏在这了</text>
      <image class="title-right" src="{{titleRightImage}}" mode="aspectFit"></image>
    </view>

    <view class="feature-container">
      <view class="feature-content">
        <view wx:for="{{featureRows}}" wx:key="rowId" wx:for-item="row" class="feature-row">
          <view wx:for="{{row.items}}" wx:key="id" class="feature-item" data-id="{{item.id}}" bind:tap="onFeatureClick">
            <image class="feature-icon" src="{{item.icon}}" mode="aspectFit"></image>
            <text class="feature-text">{{item.text}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 自定义底部tabbar -->
<customTabbar current="{{0}}" bind:change="onTabChange"></customTabbar>

<!-- 建议弹框 -->
<suggest-popup
  visible="{{showSuggestPopup}}"
  bind:close="onSuggestPopupClose"
  bind:success="onSuggestPopupSuccess"
></suggest-popup>

