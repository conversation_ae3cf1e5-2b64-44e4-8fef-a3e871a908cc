.withdraw-page {
  min-height: 100vh;
  background-color: #f5f5f5;

  /* 提现金额部分 */
  .amount-section {
    background-color: #ffffff;
    padding: 24rpx;

    .amount-title {
      font-size: 24rpx;
      color: #000000;
      margin-bottom: 8rpx;
    }

    .amount-input-container {
      display: flex;
      align-items: baseline;
      margin-bottom: 8rpx;

      .currency-symbol {
        font-size: 40rpx;
        color: #33333e;
        margin-right: 8rpx;
      }

      .amount-input {
        line-height: 100rpx;
        height: 100rpx;
        flex: 1;
        font-size: 72rpx;
        color: #33333e;
        font-weight: 500;
        border: none;
        outline: none;
        background: transparent;
      }
    }

    .limit-text {
      font-size: 24rpx;
      color: #fb7a1e;
    }
  }

  /* 姓名输入部分 */
  .name-section {
    background-color: #ffffff;
    padding: 24rpx;
    margin-top: 24rpx;

    .name-title {
      font-size: 24rpx;
      color: #000000;
      margin-bottom: 16rpx;
    }

    .name-input {
      width: 100%;
      height: 104rpx;
      border-radius: 8rpx;
      padding: 0 32rpx;
      font-size: 28rpx;
      color: #33333e;
      border-radius: 24rpx;
      background-color: #F9F9F9;
      box-sizing: border-box;

      &::placeholder {
        color: #CCCCCE;
      }
    }
    .limit-text {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #fb7a1e;
    }
  }

  /* 提现方式部分 */
  .method-section {
    background-color: #ffffff;
    padding: 24rpx;
    margin-top: 24rpx;

    .method-content {
      display: flex;

      .wechat-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 24rpx;
      }

      .method-info {
        flex: 1;

        .method-title {
          font-size: 24rpx;
          color: #000000;
          margin-bottom: 8rpx;
        }

        .method-account {
          font-size: 24rpx;
          color: #99999e;
        }
      }
    }
  }

  /* 提现按钮部分 */
  .button-section {
    padding: 24rpx 32rpx 0;

    .withdraw-button {
      height: 108rpx;
      background-color: #0198ff;
      border-radius: 54rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 500;
    }
  }
}
