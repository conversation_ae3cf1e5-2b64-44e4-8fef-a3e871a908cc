/* pages/tripHelper/my/edit/index.less */
.edit-page {
  min-height: 100vh;
  background: #F3F3F3;

  .edit-item {
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 24rpx;
    margin: 0 24rpx;
    border-bottom: 1rpx solid #F0F0F0;

    &:first-child {
      border-top-right-radius: 40rpx;
      border-top-left-radius: 40rpx;
      margin-top: 36rpx;
    }

    &:last-child {
      border-bottom-right-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
      border-bottom: none;
    }

    &.disabled {
      .edit-value {
        color: #CCCCCE;
      }
    }

    .edit-label {
      color: #11111E;
      font-weight: bold;
      font-size: 28rpx;
    }

    .edit-content {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .avatar-button {
        background: transparent;
        border: none;
        text-align: right;
        padding: 0;
        margin: 0;
        line-height: 1;

        .avatar-image {
          width: 88rpx;
          height: 88rpx;
          border-radius: 40rpx;
        }
      }

      .avatar-image {
        width: 88rpx;
        height: 88rpx;
        border-radius: 40rpx;
      }

      .edit-value {
        color: #CCCCCC;
        font-size: 32rpx;

        &.placeholder-text {
          color: #CCCCCC;
        }
      }

      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}

/* 隐藏的选择器 */
.hidden-picker {
  position: absolute;
  left: -9999rpx;
  opacity: 0;
  pointer-events: none;
}

/* 昵称编辑弹框样式 */
.popup-container {
  background: #ffffff;
  overflow: hidden;
  margin: 0 32rpx;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 24rpx;

    .popup-cancel {
      color: #33333E;
      font-size: 32rpx;
    }

    .popup-title {
      color: #11111E;
      font-size: 36rpx;
      font-weight: 500;
    }

    .popup-confirm {
      color: #0198FF;
      font-size: 32rpx;
    }
  }

  .popup-content {
    .nickname-input {
      box-sizing: border-box;
      width: 100%;
      padding-left: 32rpx;
      height: 104rpx;
      border: none;
      outline: none;
      font-size: 32rpx;
      color: #333333;
      border-radius: 24rpx;
      background-color: #F9F9F9;

      &::placeholder {
        color: #CCCCCE;
      }
    }
  }
}