# StatusDisplay 状态显示组件

状态显示组件，用于显示空数据、网络错误等状态页面。

## 属性 Properties

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| type | string | 'data-empty' | 否 | 状态类型，可选值：'data-empty', 'network-error', 'empty', 'error' |
| title | string | '' | 否 | 自定义标题，为空时使用默认标题 |
| visible | boolean | true | 否 | 是否显示组件 |
| customClass | string | '' | 否 | 自定义样式类名 |
| customStyle | string | '' | 否 | 自定义内联样式 |

## 事件 Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| buttonclick | 按钮点击事件（仅在 network-error 或 error 类型时显示按钮） | { type: string } |

## 使用示例

### 基础用法

```xml
<!-- 基础空数据状态 -->
<status-display type="data-empty" />

<!-- 网络错误状态 -->
<status-display 
  type="network-error" 
  bind:buttonclick="onRetry"
/>

<!-- 自定义标题 -->
<status-display 
  type="empty" 
  title="暂无收藏内容"
/>
```

### 自定义样式

```xml
<!-- 使用自定义类名 -->
<status-display 
  type="data-empty"
  customClass="my-custom-status"
/>

<!-- 使用内联样式 -->
<status-display 
  type="empty"
  customStyle="margin-top: 100rpx; opacity: 0.8;"
/>

<!-- 同时使用类名和内联样式 -->
<status-display 
  type="network-error"
  customClass="error-status"
  customStyle="background-color: #f5f5f5; padding: 40rpx;"
  bind:buttonclick="onRetry"
/>
```

### 条件显示

```xml
<!-- 根据数据状态显示 -->
<status-display 
  type="data-empty"
  visible="{{list.length === 0}}"
  customClass="list-empty-status"
/>
```

## 样式定制

可以通过 `customClass` 属性传入自定义类名来覆盖默认样式：

```less
.my-custom-status {
  .status-display__title {
    color: #666;
    font-size: 28rpx;
  }
  
  .status-display__image {
    opacity: 0.6;
  }
}
```

## 类型定义

```typescript
import { StatusDisplayType, StatusDisplayProps } from './types';

// 在页面或组件中使用
interface PageData {
  showEmpty: boolean;
  statusType: StatusDisplayType;
}
```
