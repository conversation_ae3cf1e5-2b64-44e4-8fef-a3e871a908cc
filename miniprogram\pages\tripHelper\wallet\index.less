.wallet-container {
  min-height: 100vh;

  /* 固定在顶部的导航栏 */
  .fixed-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: background-color 0.3s ease;

    .weui-navigation-bar {
      position: relative;
    }
  }

  /* 页面内容区域 */
  .page-content {
    .header-bg {
      position: relative;
      min-height: 640rpx; /* 改为最小高度，允许内容撑开 */
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      padding: 60rpx 32rpx 40rpx;
      padding-top: calc(env(safe-area-inset-top) + 88rpx + 20rpx); /* 安全区域 + 导航栏高度(44px≈88rpx) + 间距 */
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

    .balance-section {
      text-align: center;
      margin-top: 84rpx;

      .balance-amount {
        display: flex;
        align-items: flex-end; /* 底部对齐 */
        justify-content: center;
        font-weight: bold;
        color: #000000;
        line-height: 1;
        margin-bottom: 20rpx;

        /* ¥符号样式 */
        .currency-symbol {
          font-size: 40rpx;
          margin-right: 4rpx;
        }

        /* 数字样式 */
        .amount-number {
          font-size: 96rpx;
        }
      }

      .balance-label {
        font-size: 24rpx;
        color: #000000;
      }
    }

    .income-section {
      display: flex;
      justify-content: space-between;
      margin: 64rpx 0 0;

      .income-item {
        flex: 1;
        text-align: center;

        .income-amount {
          font-size: 28rpx;
          font-weight: bold;
          color: #000000;
          margin-bottom: 8rpx;
        }

        .income-label {
          font-size: 20rpx;
          color: #000000;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 24rpx;
      margin-top: 40rpx;

      .btn-record {
        flex: 1;
        height: 96rpx;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 120rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        color: #33333E;
        font-weight: 500;
      }

      .btn-withdraw {
        flex: 1;
        height: 96rpx;
        border-radius: 120rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &.active {
          background-color: #0198FF;
          color: #ffffff;
        }

        &.disabled {
          background-color: #EEEEEE;
          color: rgba(0, 0, 0, 0.26);
        }
      }
    }
  }

    /* 加载状态容器 */
    .loading-container {
      background-color: #f5f5f5;
      padding: 32rpx;
    }

    .transaction-list {
      background-color: #f5f5f5;
      padding: 32rpx;
      min-height: 200rpx;

      /* 空状态样式 */
      .empty-state {
        text-align: center;
        padding: 100rpx 0;

        .empty-text {
          font-size: 28rpx;
          color: #999;
        }
      }

      /* 加载更多容器 */
      .load-more-container {
        padding: 40rpx 0;
        text-align: center;

        .load-more-item {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16rpx;

          .load-more-text {
            font-size: 24rpx;
            color: #999;
          }

          &.loading .load-more-text {
            color: #666;
          }

          &.no-more .load-more-text {
            color: #ccc;
          }
        }
      }

    .transaction-card {
      background-color: #ffffff;
      border-radius: 32rpx;
      margin-bottom: 24rpx;
      overflow: hidden;

      &:active {
        background-color: #f8f8f8;
      }

      .transaction-header {
        padding: 24rpx 24rpx 16rpx;
        display: flex;
        align-items: flex-start; /* 顶部对齐 */
        justify-content: space-between;

        .header-left {
          display: flex;
          align-items: center;
          flex: 1;

          .transaction-icon {
            width: 64rpx;
            height: 64rpx;
            border-radius: 16rpx;
            margin-right: 16rpx;
          }

          .transaction-info {
            flex: 1;

            .transaction-title {
              font-size: 28rpx;
              color: #33333E;
              font-weight: 500;
              margin-bottom: 4rpx;
            }

            .transaction-date {
              font-size: 24rpx;
              color: #99999E;
            }
          }
        }

        .header-right {
          display: flex;
          flex-direction: column;
          align-items: center;

          .total-income-label {
            font-size: 24rpx;
            color: #99999E;
            margin-bottom: 4rpx;
          }

          .total-amount-row {
            display: flex;
            align-items: center;
            gap: 8rpx;

            .total-amount {
              font-size: 32rpx;
              font-weight: 500;
              color: #33333E;
            }

            .arrow-icon {
              width: 24rpx;
              height: 24rpx;
              opacity: 0.6;
            }
          }
        }
      }

      .transaction-details {
        padding: 0 24rpx 24rpx;

        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: flex-start; /* 改为顶部对齐 */
          padding: 16rpx 0;

          .detail-label {
            font-size: 28rpx;
            color: #99999E;
          }

          .detail-right {
            text-align: right;

            .detail-amount {
              font-size: 28rpx;
              margin-bottom: 4rpx;

              &.income {
                color: #33333E;
              }

              &.expense {
                color: #33333E;
              }
            }

            .detail-breakdown {
              font-size: 24rpx;
              color: #CCCCCC;
            }
          }
        }
      }
    }
    }

    /* 骨架屏样式 */
    .skeleton-container {
      padding: 32rpx;

      .skeleton-item {
      background-color: #ffffff;
      border-radius: 32rpx;
      margin-bottom: 24rpx;
      padding: 24rpx;

      .skeleton-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 16rpx;

        .skeleton-left {
          display: flex;
          align-items: center;
          flex: 1;

          .skeleton-icon {
            width: 64rpx;
            height: 64rpx;
            border-radius: 16rpx;
            background: #f0f0f0;
            margin-right: 16rpx;
            flex-shrink: 0;
          }

          .skeleton-info {
            flex: 1;

            .skeleton-title {
              height: 28rpx;
              background: #f0f0f0;
              border-radius: 14rpx;
              margin-bottom: 8rpx;
              width: 70%;
            }

            .skeleton-date {
              height: 24rpx;
              background: #f0f0f0;
              border-radius: 12rpx;
              width: 50%;
            }
          }
        }

        .skeleton-right {
          display: flex;
          flex-direction: column;
          align-items: center;

          .skeleton-label {
            height: 24rpx;
            background: #f0f0f0;
            border-radius: 12rpx;
            width: 60rpx;
            margin-bottom: 8rpx;
          }

          .skeleton-amount-row {
            display: flex;
            align-items: center;
            gap: 8rpx;

            .skeleton-amount {
              height: 32rpx;
              background: #f0f0f0;
              border-radius: 16rpx;
              width: 80rpx;
            }

            .skeleton-arrow {
              width: 24rpx;
              height: 24rpx;
              background: #f0f0f0;
              border-radius: 12rpx;
            }
          }
        }
      }

      .skeleton-details {
        .skeleton-detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16rpx 0;

          .skeleton-detail-label {
            height: 28rpx;
            background: #f0f0f0;
            border-radius: 14rpx;
            width: 30%;
          }

          .skeleton-detail-amount {
            height: 28rpx;
            background: #f0f0f0;
            border-radius: 14rpx;
            width: 25%;
          }
        }
        }
      }
    }
  }
}
