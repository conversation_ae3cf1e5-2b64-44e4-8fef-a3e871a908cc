import { request } from "../../utils/request";

// 获取订单详情
export const getOrderDetailApi = ( data : ActivityOrderDetail.IOrderDetailParams,needLoading?:boolean ) => request<ActivityOrderDetail.IOrderDetailResponse>({
  url: `/hotel/order/active/getTrailOrderDetail`,
  method: "GET",
  data,
  showLoading:needLoading === true
})

// 打卡详情页
export const checkinfoApi = ( data : ActivityOrderDetail.ICheckInfoParams ) => request<ActivityOrderDetail.ICheckInfoResponse>({
  url: `/trip/activity/v1/perform/checkIn/info`,
  method: "GET",
  data
})

// 退款
export const cancelOrderApi = ( data : ActivityOrderDetail.IOrderDetailParams ) => request<ActivityOrderDetail.ICancelOrderResponse>({
  url: `/hotel/order/active/cancelTrailOrder`,
  method: "POST",
  data,
  showLoading:true
})
