.main{
  background-color: #F7F7F7;
  height: 100vh;
  width: 100vw;
  .flex-center{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .flex-between{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .flex-start-center{
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .flex-start-start{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
  }
}
.top-box{
  width: 100%;
  height: 552rpx;
  background: #F3F3F3;
}
.title-box{
  padding: 16rpx 32rpx;
  background: #FFFFFF;

  .indicator-dots{
    width: 100%;
    view{
      width: 6rpx;
      height: 6rpx;
      background: #F3F3F3;
      margin-left: 8rpx;
    }
  }
  .indicator-dots view:first-child{
    width: 32rpx;
    margin-left: 0rpx;
  }
  .content{
    width: 310rpx;
    height: 56rpx;
    background: #F3F3F3;
    margin-top: 40rpx;
  }
  .btm{
    width: 114rpx;
    height: 48rpx;
    background: #F3F3F3;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-top: 14rpx;
  }
}
.info-box{
  margin-top: 24rpx;
  padding: 32rpx;
  background: #FFFFFF;
  .mt-40{
    margin-top: 40rpx;
  }
  .one{
    width: 162rpx;
    height: 40rpx;
    background: #F3F3F3;
  }
  .two{
    width: 686rpx;
    height: 88rpx;
    background: #F9F9F9;
    margin-top: 16rpx;
    margin-bottom: 40rpx;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
  }
  .three{
    width: 246rpx;
    height: 40rpx;
    background: #F3F3F3;
  }
  .four{
    width: 126rpx;
    height: 34rpx;
    background: #F3F3F3;
  }
  .five{
    width: 72rpx;
    height: 72rpx;
    background: #F3F3F3;
    border-radius: 50%;
  }
  .six{
    width: 42rpx;
    height: 28rpx;
    background: #F3F3F3;
    margin-top: 8rpx;
  }
  .ml-16{
    margin-left: 16rpx;
  }
  .item{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 24rpx;
    margin-top: 24rpx;
  }
}
.desc-box{
  margin-top: 24rpx;
  padding: 32rpx;
  background: #FFFFFF;
  .one{
    width: 150rpx;
    height: 50rpx;
    background: #F3F3F3;
  }
  .two{
    width: 98rpx;
    height: 44rpx;
    background: #F3F3F3;
    margin-top: 24rpx;
  }
  .three{
    width: 58rpx;
    height: 36rpx;
    background: #F3F3F3;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    margin-top: 28rpx;
    margin-left: 8rpx;
  }
  .four{
    width: 686rpx;
    height: 68rpx;
    background: #F3F3F3;
    margin-top: 16rpx;
  }
}