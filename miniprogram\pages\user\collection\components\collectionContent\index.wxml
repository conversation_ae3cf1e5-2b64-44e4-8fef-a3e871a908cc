<!-- 收藏内容展示组件 -->
<view class="collection-content {{isEditMode ? 'edit-mode' : ''}}">
  <!-- 通用列表渲染 -->
  <block wx:for="{{currentList}}" wx:key="id">
    <view class="item-wrapper {{isEditMode ? 'edit-mode' : ''}}">
      <view class="item-container {{currentSwipeIndex === index ? 'swiped' : ''}}" data-index="{{index}}" data-id="{{item.id}}" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd" bindtap="onItemClick">
        <!-- 选择框（编辑模式） -->
        <view class="checkbox-container" wx:if="{{isEditMode}}" data-id="{{item.id}}" catchtap="onSelectItem">
          <image class="checkbox-icon" src="{{item.selected ? circleActIcon : circleIcon}}" mode="aspectFit" />
        </view>

        <view class="card">
          <!-- 酒店卡片内容 -->
          <view class="hotel-card {{currentSwipeIndex === index ? 'swiped' : ''}}" data-hotel-data="{{convertedList[index]}}" catchtap="onHotelCardClick">
            <!-- 使用HotelItem组件，移除点击事件绑定 -->
            <hotel-item
              hotel-data="{{convertedList[index]}}"
            />
          </view>
        </view>

        <!-- 右滑删除按钮 -->
        <view class="delete-btn" data-id="{{item.id}}" bindtap="onDeleteItem">
          <image class="close-icon" src="{{deleteIcon}}"></image>
          <text class="delete-text">删除</text>
        </view>
      </view>
    </view>
  </block>
</view>
