<view wx:if="{{!isLoading}}" scroll-y class="main">
  <view class="order-detail">
    <!-- 订单状态 -->
    <view class="order-statu">
      <view class="title-box">
        <image class="{{orderDetail.iconClass}}" src="{{orderDetail.iconUrl}}" mode=""/>
        <view class="txt-box">
          <text class="title">{{orderDetail.orderStatusDesc}}</text>
          <text class="desc" wx:if="{{orderDetail.orderStatusGoodTips}}">{{orderDetail.orderStatusGoodTips}}</text>
        </view>
      </view>
      <view class="btm-box" wx:if="{{orderDetail.statuBar}}">
        <view class="btm success" bindtap="statubarClick">{{orderDetail.statuBar}}</view>
      </view>
    </view>
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="info-item" wx:for="{{orderDetail.orderInfoList}}" wx:key="index">
        <text>{{item.title}}</text>
        <text>{{item.value}}</text>
      </view>
    </view>
    <!-- 活动信息 -->
    <view class="activity-info">
      <image class="pic" src="{{orderDetail.mainImageUrl}}" mode=""/>
      <view class="cotent">
        <text class="title">{{orderDetail.activityName}}</text>
        <view class="address-box" wx:if="{{orderDetail.cityName}}">
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrderDetail/location_icon.png" mode=""/>
          <text>{{orderDetail.cityName}}</text>
        </view>
      </view>
    </view>
    <!-- 活动二维码 -->
    <view class="ewm-info" wx:if="{{orderDetail.qRCodeImageUrl}}">
      <image src="{{orderDetail.qRCodeImageUrl}}" mode=""/>
      <view class="txt-box">
        <view></view>
        <text>扫码加入活动微信群</text>
        <view></view>
      </view>
    </view>
    <!-- 精彩打卡 -->
    <view class="comment-list" wx:if="{{comment}}">
      <text class="title">精彩打卡</text>
      <view class="comment-content">
        <comment-item commemtData="{{comment}}"></comment-item>
      </view>
    </view>
  </view>
  <refund-pop 
    visible="{{showRefundVisible}}" 
    refundData="{{refundInfo}}"
    bind:refreshPage="initData"></refund-pop>
</view>

<skeleton-screen wx:else></skeleton-screen>