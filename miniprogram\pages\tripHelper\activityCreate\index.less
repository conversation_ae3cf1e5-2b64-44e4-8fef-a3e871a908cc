page {
  background-color: #fff;
  color: @text-color;
  height: 100%;
  box-sizing: border-box;
}

.activity-create-page {
  height: 100%;
  .steps-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    color: @text-deep-color;
    padding-bottom: 24rpx;
    .border(1px, solid, #F3F3F3, 0, bottom);
    .step-item {
      line-height: 52rpx;
      height: 52rpx;
      padding: 4rpx 16rpx;
      box-sizing: border-box;
      background-color: @border-color;
      border-radius: 26rpx;
      display: flex;
      align-items: center;
      &.is-active {
        color: @primary-color;
        background-color: @primary-light-color-2;
        &::before {
          content: "";
          display: block;
          background-image: url('@{static-base-url}/tripHelper/activity/circle-active.png');
          background-size: 100%;
          width: 24rpx;
          height: 24rpx;
          margin-right: 8rpx;
        }
        &.is-checked {
          &::before {
            background-image: url('@{static-base-url}/tripHelper/activity/checked.png');
          }
        }
      }
      &:last-child {
        margin-left: 24rpx;
      }
      .active-project {
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 8rpx 16rpx;
        border-radius: 44rpx;
        height: 44rpx;
        line-height: 44rpx;
        box-sizing: border-box;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-right: 8rpx;
        }
      }
    }
  }
  .main-body {
    height: calc(100% - 76rpx);
    overflow: hidden;
  }
}