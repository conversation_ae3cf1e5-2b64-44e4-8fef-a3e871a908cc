
<wxs src="../../../utils/tool.wxs" module="tool"/>

<view class="main-body">

	<view class="place-item" wx:for="{{placeList}}" wx:key="index">
		<view class="place-item-title">
			<view class="place-item-title-content">
				<text>集合点</text>
			</view>
 			<view class="place-item-title-icon" bind:tap="handleRemove" data-index="{{index}}"></view>
		</view>
		<view class="place-item-value">
			<view class="place-item-value-label">集合时间</view>
			<view class="place-item-value-content {{item.startTime ? null : 'is-placeholder'}}" bind:tap="handleOpenTimePicker" data-index="{{index}}">{{item.startTime ? tool.formatDateTime(item.startTime, 'MM.DD(周dd) HH:mm') : '请选择'}}</view>
		</view>
		<view class="place-item-value">
			<view class="place-item-value-label">集合地点</view>
			<view class="place-item-value-content {{item.placeName ? null : 'is-placeholder'}}" bind:tap="handleChooseLocation" data-index="{{index}}">
				<view class="place-text">{{item.placeName || '请选择'}}</view>
			</view>
		</view>
	</view>

	<view class="place-increase" bind:tap="handleInsertPlace">
		添加地点
	</view>
</view>

<view class="operate-wrap">
	<button bind:tap="handleSubmit">保存提交</button>
</view>

<t-date-time-picker auto-close title="集合时间" visible="{{showTimePicker}}" mode="minute" value="{{activeIndex === null ? '' : placeList[activeIndex].startTime}}" format="YYYY-MM-DD HH:mm" start="{{minDate}}" end="{{maxDate}}" bindchange="handleTimeChange" bindcancel="handleCloseTimePicker" />