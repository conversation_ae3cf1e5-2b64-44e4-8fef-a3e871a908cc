import { request } from "../../utils/request";

/** 获取活动项目列表 */
export const getProjectList = () => request<Activity.IProject[]>({
	url: '/trip/activity/type/v1/queryAll'
});

/**
 * 创建活动
 * @param data 创建活动模型
 */
export const createActivity = (data: Activity.ICreateUpdateActivityPayload) => request<number>({
	url: '/trip/activity/v1/createActivity',
	method: 'POST',
	data
});

/** 更新活动 */
export const updateActivity = (data: Activity.ICreateUpdateActivityPayload) => request<number>({
	url: '/trip/activity/v1/editActivity',
	method: 'POST',
	data
})

/**
 * 获取推开政策
 */
export const getPolicyList = () => request<Activity.IPolicy[]>({
	url: '/trip/activity/policy/v1/queryAll'
})

/** url link内容识别 */
export const generateRoute = (params: { prompt: string;}) => request<Activity.IAIAnalysisResult>({
	url: '/trip/activity/route/v1/generateRoute',
	params
})

/** 活动打卡 */
export const activityClockIn = (data: ClockIn.IPayload) => request({
	url: '/trip/activity/v1/perform/checkIn',
	method: 'POST',
	data
})