<view class="home-bg"></view>
<view class="home-service">
	<view class="app-nav">
		<view class="app-sign">
			<image class="app-logo" src="{{staticBaseUrl}}/home/<USER>" mode="" />
		</view>
		<view class="app-greeting">Hi 早上好</view>
	</view>
	<view class="business-grid">
		<view
			wx:for="{{businessList}}"
			wx:key="type"
			class="business-item {{businessType === item.type ? 'is-active' : null}}"
			bind:tap="handleBusinessSwitch"
			data-type="{{item.type}}"
		>
			<image class="business-item-icon" src="{{businessType === item.type ? item.activeIcon : item.inactiveIcon}}" mode="widthFix" />
			<view class="business-item-label">{{item.name}}</view>
		</view>
	</view>


	<view class="quick-search-container {{businessType}}">
		<HotelQuerier wx:if="{{businessType === 'hotel'}}" />
	</view>
</view>
<view>
	<Hotel></Hotel>
</view>