// 登录管理工具类
import { wxMiniLogin } from '../api/login';
import { StorageKeyEnum } from '../enum/app';

/**
 * 登录管理器
 */
export class LoginManager {



  /**
   * 获取微信登录code
   */
  static getWxCode(): Promise<string> {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            resolve(res.code);
          } else {
            reject(new Error('获取微信登录code失败'));
          }
        },
        fail: (err) => {
          reject(new Error(`微信登录失败: ${err.errMsg}`));
        }
      });
    });
  }

  /**
   * 执行登录流程
   * @param wxCode 微信登录code
   * @param phoneCode 手机号授权code（可选）
   */
  static async login(wxCode: string, phoneCode?: string): Promise<Login.LoginResponse> {
    try {
      // 构建登录参数
      const loginParams: Login.WxMiniLoginRequest = {
        wxCode,
        loginType: 'vx_mini'
      };

      // 如果有手机号code，则添加
      if (phoneCode) {
        loginParams.phoneCode = phoneCode;
      }

      console.log('调用登录接口，参数:', loginParams);

      const result = await wxMiniLogin(loginParams);

      console.log('登录接口返回:', result);

      if (!result.isSuccess) {
        throw new Error(result.message || '登录失败');
      }

      // 处理登录成功的情况
      if (result.data.token) {
        // 保存token
        wx.setStorageSync(StorageKeyEnum.Token, result.data.token);

        // 保存用户信息
        if (result.data.nickName) {
          const userInfo = {
            nickname: result.data.nickName || ''
          };
          wx.setStorageSync(StorageKeyEnum.UserInfo, userInfo);
        }

        // needProfileComplete 不需要存储，直接在登录流程中处理
      }

      return result.data;

    } catch (error: any) {
      // 不在这里打印错误日志，让上层调用者处理
      throw error;
    }
  }

  /**
   * 处理401错误
   */
  static handleTokenExpired() {
    console.log('Token已过期');
  }



  /**
   * 清除登录状态
   */
  static clearLoginState() {
    wx.removeStorageSync(StorageKeyEnum.Token);
    wx.removeStorageSync(StorageKeyEnum.UserInfo);
  }

  /**
   * 跳转到登录页面（使用navigateTo，保留返回按钮）
   * @param backUrl 登录成功后要跳转的页面URL
   * @param isTokenExpired 是否是token过期导致的跳转
   */
  static navigateToLogin(backUrl?: string, isTokenExpired?: boolean) {
    let url = '/pages/login/index';
    const params: string[] = [];

    // 如果有backUrl，添加到URL参数中
    if (backUrl) {
      params.push(`backUrl=${encodeURIComponent(backUrl)}`);
    }

    // 如果是token过期，添加标识参数
    if (isTokenExpired) {
      params.push('tokenExpired=1');
    }

    // 拼接参数
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }
    wx.navigateTo({
      url,
      fail: (err) => {
        console.error('跳转登录页面失败:', err);
      }
    });
  }
}
