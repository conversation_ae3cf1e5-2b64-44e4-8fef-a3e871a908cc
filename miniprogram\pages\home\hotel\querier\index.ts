import { ComponentWithStore } from 'mobx-miniprogram-bindings';
import { store } from '../../../../store/index';
import appConfig from '../../../../config/app.config';

ComponentWithStore({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl
  },

  storeBindings: {
    store,
    fields: ['cityInfo'],
    actions: {}
  } as const,

  lifetimes: {
    ready() {

    }
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})