.project-pick {
	padding: 32rpx 32rpx 192rpx;
	height: calc(100vh - 76rpx);
	box-sizing: border-box;
	overflow-y: auto;

	&-title {
		line-height: 50rpx;
		color: @text-title-color;
		font-weight: bold;
		font-size: 36rpx;
		padding-bottom: 24rpx;
	}

	.project-pick-content {
		display: flex;
		flex-wrap: wrap;

		.project-item {
			margin-top: 40rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: 24rpx;
			line-height: 34rpx;
			width: 222rpx;
			&.is-active {
				color: @primary-color;
				.project-item-logo {
					background: linear-gradient( 138deg, #0198FF 0%, #0DC0FE 100%);
				}
			}

			.project-item-logo {
				width: 120rpx;
				height: 120rpx;
				background: @border-color;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 16rpx;
				image {
					width: 72rpx;
					height: 72rpx;
				}
			}

		}
	}

	.operate-wrap {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		padding-bottom: 68rpx;
		background-color: #fff;

		.trigger-wrap {
			padding: 16rpx 32rpx 0;
			width: 100%;
			.border(1px, solid, @border-color, 0, top);
			.button-item {
				display: block;
				width: 100%;
				height: 108rpx;
				line-height: 108rpx;
				border-radius: 108rpx;
				font-size: 32rpx;
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				&.is-active {
					background-color: @primary-color;
				}
				&.is-inactive {
					background-color: @placeholder-text-color;
				}
			}
		}
	}
}

.skeleton-wrap {
	padding: 32rpx;
	.skeleton-title {
		width: 148rpx;
		height: 50rpx;
		background-color: @bg-gray-color;
	}
	.skeleton-content {
		display: flex;
		flex-wrap: wrap;
		padding-top: 24rpx;
		.skeleton-item {
			margin-top: 40rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.skeleton-icon {
				width: 120rpx;
				height: 120rpx;
				margin: 0 50rpx;
				background: @border-color;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 16rpx;
			}
			.skeleton-label {
				width: 50rpx;
				height: 34rpx;
				background-color: @border-color;
			}
		}
	}
}