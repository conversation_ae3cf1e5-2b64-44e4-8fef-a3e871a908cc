<!--pages/order/list/index.wxml-->
<view class="order-list-page">


  <!-- 自定义头部 -->
  <custom-header
    current-keyword="{{searchKeyword}}"
    bind:searchclick="onSearchClick"
    bind:filterclick="onFilterClick"
    bind:back="onBack"
  />

  <!-- Tab切换 -->
  <view class="tab-container" style="top: {{headerHeight}}rpx;">
    <view class="tab-list">
      <view 
        class="tab-item {{currentTab === item.value ? 'active' : ''}}" 
        wx:for="{{tabs}}" 
        wx:key="value" 
        data-value="{{item.value}}" 
        bindtap="onTabClick"
      >
        <text class="tab-text">{{item.label}}</text>
        <view class="tab-indicator" wx:if="{{currentTab === item.value}}"></view>
      </view>
    </view>
  </view>

  <!-- 使用修复后的下拉刷新上拉加载组件 -->
  <refreshLoadList
    refreshing="{{refreshing}}"
    loading-more="{{loadingMore}}"
    has-more="{{pageParams[currentTab].hasMore}}"
    is-empty="{{orderLists[currentTab].length === 0}}"
    loading="{{loading}}"
    empty-text="{{emptyText}}"
    empty-desc="{{emptyDesc}}"
    empty-icon="order"
    top-offset="{{headerHeight + 128}}rpx"
    bind:refresh="onRefresh"
  >
    <!-- 订单列表 -->
    <view class="order-list">
      <order-card
        wx:for="{{orderLists[currentTab]}}"
        wx:key="orderId"
        order-data="{{item}}"
        bind:cardclick="onOrderClick"
        bind:actionclick="onActionClick"
      />
    </view>
  </refreshLoadList>

  <!-- 搜索弹框 -->
  <search-popup
    visible="{{showSearchPopup}}"
    current-keyword="{{searchKeyword}}"
    current-category="{{filterData.type}}"
    bind:search="onSearch"
    bind:close="onSearchPopupClose"
  />

  <!-- 筛选弹框 -->
  <filter-popup
    visible="{{showFilterPopup}}"
    default-filter="{{filterData}}"
    bind:filter="onFilter"
    bind:close="onFilterPopupClose"
  />
</view>
