import appConfig from '../../../../../config/app.config';
import { emitter } from '../../../../../utils/mitt';
import { getRouteListApi } from '../../../../../api/tripHelper/routeLine';

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    routeId: {
      type: Number,
      value: undefined
    },
    title: {
      type: String,
      value: ''
    },
    description: {
      type: String,
      value: ''
    },
    coverImages: {
      type: Array,
      value: [] as string[]
    },
    activeTab: {
      type: Number,
      value: 1
    },
    projectId: {
      type: Number,
      value: 0
    },
    activeStep: {
      type: Number,
      value: 1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl,
    loading: false,
    tabList: [
      {
        value: 1,
        label: '选择线路',
        activeUrl: `${appConfig.staticBaseUrl}/tripHelper/activity/route-line-active.png`,
        inActiveUrl: `${appConfig.staticBaseUrl}/tripHelper/activity/route-line.png`
      },
      {
        value: 2,
        label: '手动创建',
        activeUrl: `${appConfig.staticBaseUrl}/tripHelper/activity/manual-create-active.png`,
        inActiveUrl: `${appConfig.staticBaseUrl}/tripHelper/activity/manual-create.png`
      }
    ],
    toView: '',
    routeList: [] as RouteLine.Datum[],
    showAIModal: false
  },
  lifetimes: {
    detached() {
      emitter.off('routeLineSelect');
    }
  },
  observers: {
    activeStep(val) {
      if (val === 2) {
        this.setData({
          loading: true
        })
        getRouteListApi({ pageIndex: 1, pageSize: 10, project: this.data.projectId }).then(res => {
          this.setData({
            loading: false,
            routeList: res.isSuccess ? res.data.records : []
          });
          if (res.isSuccess) {
            if (!res.data.records.length) {
              this.triggerEvent('updateActiveTab', { value: 2 });
            }
          }
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /** tab切换 */
    handleSwitchTab(e: WechatMiniprogram.TouchEvent<{}, {}, { value: number }>) {
      const value = e.currentTarget.dataset.value;
      this.triggerEvent('updateActiveTab', { value });
    },
    /** 跳转路线选择 */
    handleJumpToRouteLineList() {
      wx.navigateTo({
        url: `/pages/tripHelper/chooseRoute/chooseRoute?projectId${this.data.projectId}`
      })
      emitter.once('routeLineSelect', ({ value }) => {
        const index = this.data.routeList.findIndex(item => item.routeId === value.routeId);
        const routeList = this.data.routeList;
        if (index === -1) {
          routeList.unshift(value);
        }
        this.setData({
          routeList,
          toView: `item_${index === -1 ? 0 : index}`
        });
        this.updateValue({
          routeId: value.routeId
        })
      })
    },
    /** 图片上传回调 */
    handleImageChange(e: WechatMiniprogram.CustomEvent<{ value: string[] }>) {
      this.updateValue({
        coverImages: e.detail.value ?? []
      });
    },
    handleAIModalToggle(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.setData({
        showAIModal: e.detail.visible
      })
    },
    /** 打开AI识别弹窗 */
    handleOpenAIModal() {
      this.setData({
        showAIModal: true,
      })
    },
    handleTitleChange(e: WechatMiniprogram.CustomEvent<{ value: string }>) {
      this.updateValue({
        title: e.detail.value
      });
    },
    handleContentChange(e: WechatMiniprogram.CustomEvent<{ value: string }>) {
      this.updateValue({
        description: e.detail.value
      })
    },
    /** 更新数据 */
    updateValue(data: Record<string, any>) {
      this.triggerEvent('change', data);
    },
    /** AI识别解析数据更新 */
    handleValueChange(e: WechatMiniprogram.CustomEvent<Pick<Activity.ICreateUpdateActivityPayload, 'title' | 'description' | 'coverImages'>>) {
      this.updateValue(e.detail);
    },
    handleRouteLinePick(e: WechatMiniprogram.TouchEvent<{}, {}, {routeId: number}>) {
      this.updateValue({
        routeId: e.currentTarget.dataset.routeId
      })
    }
  }
})