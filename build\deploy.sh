#!/bin/bash

# 微信小程序自动部署脚本
# 使用方法: ./build/deploy.sh [版本号] [描述]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PACKAGE_JSON_PATH="$PROJECT_ROOT/package.json"

# 默认配置
DEFAULT_VERSION="1.0.0"
DEFAULT_DESC="CI自动部署"

# 获取参数
DESC=${2:-$DEFAULT_DESC}

log_info "开始部署流程..."
log_info "项目根目录: $PROJECT_ROOT"

# 检查必要文件
check_requirements() {
    log_info "检查必要文件..."
    
    # 检查private.key
    PRIVATE_KEY_PATH="$SCRIPT_DIR/private.key"
    if [ ! -f "$PRIVATE_KEY_PATH" ]; then
        log_error "私钥文件不存在: $PRIVATE_KEY_PATH"
        log_info "请将小程序上传密钥文件放置在: $PRIVATE_KEY_PATH"
        exit 1
    fi
    log_success "私钥文件检查通过"
    
    # 检查project.config.json
    PROJECT_CONFIG_PATH="$PROJECT_ROOT/project.config.json"
    if [ ! -f "$PROJECT_CONFIG_PATH" ]; then
        log_error "项目配置文件不存在: $PROJECT_CONFIG_PATH"
        exit 1
    fi
    
    # 提取appid
    APPID=$(node -e "
        try {
            const config = require('$PROJECT_CONFIG_PATH');
            console.log(config.appid || '');
        } catch (e) {
            console.log('');
        }
    ")
    
    if [ -z "$APPID" ]; then
        log_error "无法从project.config.json中获取appid"
        exit 1
    fi
    log_success "AppID: $APPID"
    
    # 检查package.json
    if [ ! -f "$PACKAGE_JSON_PATH" ]; then
        log_error "package.json不存在: $PACKAGE_JSON_PATH"
        exit 1
    fi
    log_success "package.json检查通过"
    
    # 检查node_modules
    NODE_MODULES_PATH="$PROJECT_ROOT/node_modules"
    if [ ! -d "$NODE_MODULES_PATH" ]; then
        log_warning "node_modules不存在，正在安装依赖..."
        cd "$PROJECT_ROOT"
        npm install
        if [ $? -ne 0 ]; then
            log_error "npm install 失败"
            exit 1
        fi
        log_success "依赖安装完成"
    else
        log_success "node_modules检查通过"
    fi

    # 提取version
    VERSION=$(node -e "
        try {
            const packageJson = require('$PACKAGE_JSON_PATH');
            console.log(packageJson.version || '$DEFAULT_VERSION')
        } catch (e) {
            console.log('$DEFAULT_VERSION')
        }
    ")

    if [ -z "$VERSION" ]; then
        log_error "无法从package.json中获取version"
        exit 1
    fi
    log_success "Version: $VERSION"

    if COMMIT_MSG=$(get_latest_commit_message); then
        DESC="$COMMIT_MSG"
    else
        log_error "无法从git中获取commit message"
    fi
    log_success "DESC: $DESC"
}

# 获取最近一次 Git 提交的 commit message
get_latest_commit_message() {
    # 检查当前目录是否是 Git 仓库
    if ! git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
        echo "错误：当前目录不是 Git 仓库" >&2
        return 1
    fi

    # 获取 commit message
    local message
    message=$(git log -1 --pretty=%B)

    # 检查是否获取成功
    if [ -z "$message" ]; then
        echo "错误：无法获取 commit message" >&2
        return 1
    fi

    # 输出 message（不带换行符）
    echo -n "$message"
    return 0
}

# 构建npm包
build_npm() {
    log_info "开始构建npm包..."
    
    cd "$PROJECT_ROOT"
    
    # 检查是否有miniprogram-ci
    if ! npm list miniprogram-ci > /dev/null 2>&1; then
        log_warning "miniprogram-ci未安装，正在安装..."
        npm install --save-dev miniprogram-ci
    fi
    
    # 构建npm包
    npx miniprogram-ci build-npm --project-path "$PROJECT_ROOT" --package-json-path "$PACKAGE_JSON_PATH" --miniprogram-npm-dist-dir "$PROJECT_ROOT/miniprogram"
    
    if [ $? -eq 0 ]; then
        log_success "npm包构建完成"
    else
        log_error "npm包构建失败"
        exit 1
    fi
}

# 上传代码
upload_code() {
    log_info "开始上传代码..."
    
    cd "$PROJECT_ROOT"
    
    # 上传代码
    npx miniprogram-ci upload \
        --project-path "$PROJECT_ROOT" \
        --appid "$APPID" \
        --private-key "$PRIVATE_KEY_PATH" \
        --version "$VERSION" \
        --desc "$DESC" \
        --setting '{"es6":true,"minified":true,"autoPrefixWXSS":true}'
    
    if [ $? -eq 0 ]; then
        log_success "代码上传完成"
    else
        log_error "代码上传失败"
        exit 1
    fi
}

# 使用TypeScript脚本构建（如果可用）
build_with_ts() {
    log_info "尝试使用TypeScript脚本构建..."
    
    # 检查是否有ts-node
    if ! command -v ts-node > /dev/null 2>&1; then
        log_warning "ts-node未安装，使用原生miniprogram-ci"
        return 1
    fi
    
    # 检查ci.ts是否存在
    CI_TS_PATH="$SCRIPT_DIR/ci.ts"
    if [ ! -f "$CI_TS_PATH" ]; then
        log_warning "ci.ts不存在，使用原生miniprogram-ci"
        return 1
    fi
    
    # 运行TypeScript脚本
    cd "$PROJECT_ROOT"
    ts-node "$CI_TS_PATH" \
        --project "$PROJECT_ROOT" \
        --appid "$APPID" \
        --private-key "$PRIVATE_KEY_PATH" \
        --version "$VERSION" \
        --desc "$DESC"
    
    if [ $? -eq 0 ]; then
        log_success "TypeScript脚本构建完成"
        return 0
    else
        log_warning "TypeScript脚本构建失败，回退到原生miniprogram-ci"
        return 1
    fi
}

# 主函数
main() {
    log_info "=== 微信小程序自动部署脚本 ==="
    
    # 检查必要文件
    check_requirements
    
    # 尝试使用TypeScript脚本构建
    if build_with_ts; then
        log_success "部署完成！"
        exit 0
    fi
    
    # 回退到原生miniprogram-ci
    log_info "使用原生miniprogram-ci进行构建..."
    
    # 构建npm包
    build_npm
    
    # 上传代码
    upload_code
    
    log_success "部署完成！"
    log_info "版本: $VERSION"
    log_info "描述: $DESC"
    log_info "AppID: $APPID"
}

# 显示帮助信息
show_help() {
    echo "微信小程序自动部署脚本"
    echo ""
    echo "用法: $0 [版本号] [描述]"
    echo ""
    echo "参数:"
    echo "  版本号    小程序版本号 (默认: 1.0.0)"
    echo "  描述      上传描述 (默认: CI自动部署)"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 1.2.3"
    echo "  $0 1.2.3 '修复bug'"
    echo ""
    echo "注意事项:"
    echo "  1. 确保build/private.key文件存在"
    echo "  2. 确保project.config.json中的appid正确"
    echo "  3. 确保已安装项目依赖 (npm install)"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help|help)
        show_help
        exit 0
        ;;
esac

# 执行主函数
main "$@" 