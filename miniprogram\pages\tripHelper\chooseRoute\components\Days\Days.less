/* pages/tripHelper/chooseRoute/components/Days/Days.less */
@import "../../../../../styles/mixins.less";

.days-container {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 0 32rpx 32rpx;
}

.days-picker {
  height: 500rpx;
  width: 100%;
}

.days-item {
  line-height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #000;
  transition: all 0.2s ease;
  
  &.selected {
    background-color: #fff;
    
  }
}

.days-bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
  gap: 24rpx;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #666;
  
  &:active {
    background-color: #e9ecef;
  }
}

.btn-confirm {
  background-color: @primary-color;
  color: #fff;
  
  &:active {
    background-color: @primary-color;
  }
}