.main{
  background-color: #F7F7F7;
  height: 100vh;
  width: 100vw;
}
.activity-info{
  width: 100%;
  background-color: #FFFFFF;

  .title-box{
    border-top: 1rpx solid #F0F0F0;
    border-bottom: 1rpx solid #F0F0F0;
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;

    .pic{
      width: 144rpx;
      height: 144rpx;
      border-radius: 16rpx;
    }
    .content{
      margin-left: 24rpx;
      flex: 1;
      .title{
        font-weight: 500;
        font-size: 36rpx;
        color: #33333E;
      }
      .address-box{
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        image{
          width: 24rpx;
          height: 24rpx;
          margin-right: 8rpx;
        }
        text{
          font-size: 24rpx;
          color: #33333E;
        }
      }
      
    }
  }
  .activity-content{
    padding: 24rpx 32rpx;
    .between-item{
      display: flex;
      justify-content: space-between;
      align-self: flex-start;
      margin-bottom: 48rpx;

      .title{
        font-weight: 500;
        font-size: 28rpx;
        color: #66666E;
      }
      .kc-txt{
        font-size: 24rpx;
        color: #F04838;
        margin-left: 8rpx;
      }
      .txt{
        font-size: 28rpx;
        color: #33333E;
      }
      .blod{
        font-weight: 500;
      }
      .max-wid{
        max-width: 290rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
      }
      .flex-center{
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .flex-between{
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .wechat-icon{
        width: 40rpx;
        height: 40rpx;
        margin-right: 12rpx;
      }
      .address-list{
        .address-item{
          width: 524rpx;
          height: 88rpx;
          background: #F9F9F9;
          border-radius: 16rpx 16rpx 16rpx 16rpx;
          margin-bottom: 8rpx;

          .check-icon{
            width: 32rpx;
            height: 32rpx;
            margin-right: 16rpx;
            margin-left: 24rpx;
          }
          .map-icon{
            width: 32rpx;
            height: 32rpx;
            margin-right: 24rpx;
          }
          .txt:nth-child(2){
            margin-right: 16rpx;
          }
        }
      }
      .address-list .address-item:last-child{
        margin-bottom: 0;
      }
      .price-list{
        .price-item{
          margin-bottom: 26rpx;

          .txt:nth-child(2) {
            margin-left: 16rpx;
          }

          .num-box{
            height: 60rpx;
            background: #F7F7F7;
            border-radius: 16rpx 16rpx 16rpx 16rpx;
            padding: 0 8rpx;
            margin-left: 16rpx;
            image{
              width: 40rpx;
              height: 40rpx;
            }
            text{
              font-weight: 500;
              font-size: 32rpx;
              color: #33333E;
              margin: 0 16rpx;
              display: flex;
              justify-content: center;
              min-width: 40rpx;
            }
          }
        }
      }
      .price-list .price-item:last-child{
        margin-bottom: 0;
      }
      .price-txt{
        margin-top: 8rpx;
      }
    }
  }
  .activity-content .between-item:last-child{
    margin-bottom: 0;
  }
}
.activity-refund{
  padding: 32rpx;
  background-color: #FFFFFF;
  margin-top: 24rpx;
  .title-box{
    display: flex;
    align-items: center;
    image{
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
    .txt{
      font-weight: 500;
      font-size: 28rpx;
      color: #33333E;
    }
    .desc{
      font-size: 20rpx;
      color: #99999E;
      margin-left: 8rpx;
      flex: 1;
    }
  }
  .refund-table {
    margin-top: 24rpx;
    border: 2rpx solid #F1F6FF;
    overflow: hidden;
    font-size: 24rpx;

    .table-row {
      display: flex;

      .table-cell {
        flex: 1;
        padding: 8rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        border-right: 2rpx solid #F1F6FF;
        border-bottom: 2rpx solid #F1F6FF;
        box-sizing: border-box;
        min-height: 84rpx;
      }

      /* 第一列固定宽度 */
      .table-cell:nth-child(1) {
        width: 296rpx;
        flex: none; /* 禁止伸缩 */
      }

      /* 后面列均分剩余空间 */
      .table-cell:nth-child(n+2) {
        flex: 1; /* 均分剩余空间 */
      }

      .table-cell:last-child {
        border-right: none;
      }
    }
    .head .table-cell{
      font-weight: 500;
      background: #F1F6FF;
    }
  
    /* 最后一行去掉底部边框 */
    .table-row:last-child .table-cell {
      border-bottom: none;
    }
  }
  .bottom-desc{
    margin-top: 16rpx;
    font-size: 20rpx;
    color: #99999E;
  }
}
.activity-tips{
  padding: 32rpx;
  background-color: #FFFFFF;
  margin-top: 24rpx;
  padding-bottom: 300rpx;
  .title-box{
    display: flex;
    align-items: center;
    image{
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
    text{
      font-weight: 500;
      font-size: 28rpx;
      color: #33333E;
    }
  }
  .tips-content-item{
    display: flex;
    align-items: flex-start;
    margin-top: 8rpx;
    .left{
      width: 6rpx;
      height: 6rpx;
      background: #D9D9D9;
      border-radius: 50%;
      margin-right: 24rpx;
      margin-top: 16rpx;
    }
    .right{
      font-size: 28rpx;
      color: #99999E;
      flex: 1;
    }
  }

}
.bottom-bar{
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 100;
  background-color: #FFFFFF;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  .bottom-bar-content{
    padding: 16rpx 32rpx 16rpx 56rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 2rpx solid #F7F7F7;
    view{
      width: 686rpx;
      height: 108rpx;
      background: #0198FF;
      border-radius: 120rpx 120rpx 120rpx 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      text{
        font-weight: 500;
        font-size: 32rpx;
        color: #FFFFFF;
      }
    }
  }
  .disabled{
    opacity: 0.3;
  }
}