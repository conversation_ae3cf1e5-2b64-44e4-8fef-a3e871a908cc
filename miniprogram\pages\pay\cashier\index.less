page {
  height: 100%;
}
.cashier-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  .merchant-container {
    margin-bottom: 24rpx;
    background-color: #fff;
    padding: 32rpx;
    display: flex;
    font-size: 28rpx;
    line-height: 40rpx;
    .pay-to {
      flex-shrink: 0;
      margin-right: 72rpx;
    }
    .merchant-info {
      display: flex;
      .merchant-sign {
        flex-shrink: 0;
        align-self: flex-start;
        margin-right: 16rpx;
        padding: 0 16rpx;
        background: linear-gradient(to right, #02C397, #13E5B5);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        display: inline-block;
        .border(1px, solid, #02C397, 20rpx);
      }
    }
  }

  .payment-container {
    flex: 1;
    background-color: #fff;
    padding: 36rpx 32rpx;
    .payment-title {
      font-size: 28rpx;
      margin-bottom: 24rpx;
    }
    .payment-amount {
      font-size: 80rpx;
      line-height: 84rpx;
      font-weight: bold;
      padding-bottom: 24rpx;
      .border(1px, solid, #F0F0F0, 0, bottom);
      .currency {
        font-size: 40rpx;
        font-weight: 500;
        line-height: 56rpx;
      }
    }
    .payment-btn {
      margin-top: 68rpx;
      width: 100%;
      height: 100rpx;
      border-radius: 50rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}