<!--pages/order/list/components/orderCard/index.wxml-->
<view class="order-card" bindtap="onCardClick">
  <!-- 订单状态和删除按钮 -->
  <view class="card-header">
    <view class="status-section">
      <image src="{{hotelIcon}}" style="width: 32rpx; height: 32rpx;" />
      <text class="status-label">酒店</text>
    </view>
    <view class="status-section">
      <view class="status-text">{{orderData.statusText}}</view>
      <view class="status-gang">|</view>
      <image wx:if="{{showDeleteButton}}" src="{{deleteIcon}}" style="width: 32rpx; height: 32rpx;" bindtap="onDeleteClick" />
    </view>
  </view>

  <!-- 酒店信息 -->
  <view class="hotel-info">
    <view class="hotel-name">{{orderData.hotelName}}</view>
    <view class="hotel-address">{{orderData.roomType}}</view>
  </view>

  <!-- 订单详情 -->
  <view class="order-details">
    <view class="date-info">
      <text class="date-text">{{checkInDate}} 至</text>
    </view>
    <view class="date-info">
      <text class="date-text">{{checkOutDate}} · {{orderData.nights}}晚 · {{orderData.roomType}}</text>
    </view>
  </view>

  <!-- 价格和操作按钮 -->
  <view class="card-footer">
    <view class="price-section">
      <text class="price-label">在线支付</text>
      <text class="currency">¥</text>
      <text class="price">{{orderData.actualAmount}}</text>
    </view>

    <view class="action-buttons">
      <!-- 根据订单状态显示不同按钮 -->
      <view class="action-btn primary" bindtap="onRebookClick">
        再次预订
      </view>
      <view class="action-btn secondary" bindtap="onPayClick">
        去支付
      </view>
    </view>
  </view>
</view>