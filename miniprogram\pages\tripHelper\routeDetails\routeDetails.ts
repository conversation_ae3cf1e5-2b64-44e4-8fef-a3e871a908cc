
import appConfig from "../../../config/app.config";
import { emitter } from '../../../utils/mitt';
import { routeDetailApi } from "../../../api/tripHelper/routeLine";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl,
    routeId: '',
    routeDetails: {} as RouteLine.RouteDetailResponse,
    loading: true, // 添加loading状态
    
    // 轮播图数据（处理后的格式）
    routeImagesList: [] as any[],
    current: 1,
    autoplay: true,
    duration: 500,
    interval: 5000,
    // 指示器配置 - 可以通过这里自定义指示器样式
    navigation: { 
      type: 'dots-bar', // 'dots' | 'dots-bar' | 'fraction'
      showSlideBtn: false, // 是否显示左右切换按钮
      minShowNum: 2 // 最少显示的轮播数量
    },
    paginationPosition: 'bottom',
    
    // 滚动状态
    scrollTop: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    this.setData({
      routeId: options.routeId
    });
    this.getRouteDetail();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },
  async getRouteDetail() {
    try {
      // 开始加载，显示骨架屏
      this.setData({
        loading: true
      });
      
      const {data, code} = await routeDetailApi(this.data.routeId);
      if (code == 200) {      
        this.setData({
          routeDetails: data,
          loading: false // 加载完成，隐藏骨架屏
        });
      } else {
        // API调用失败，隐藏骨架屏
        this.setData({
          loading: false
        });
        wx.displayToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取路线详情失败:', error);
      // 发生错误，隐藏骨架屏
      this.setData({
        loading: false
      });
      wx.displayToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 页面滚动事件监听
   */
  onPageScroll(e: { scrollTop: number }) {
    this.setData({
      scrollTop: e.scrollTop
    });
  },

  /**
   * 图片预览点击事件
   */
  onImagePreview(e: any) {
    const { images, index } = e.currentTarget.dataset;
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  /**
   * 选择线路事件
   */
  onSelectRoute() {
    emitter.emit('routeLineSelect', { 
      value: this.data.routeDetails
    })
    wx.navigateBack({ delta: 2 }) // 返回两级
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    
  }
});