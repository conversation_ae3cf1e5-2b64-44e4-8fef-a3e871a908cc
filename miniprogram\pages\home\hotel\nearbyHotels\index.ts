import type { TMapInstance, IMarkerOption } from "../../../../components/tencentMap/types";
import { ComponentWithComputedStore } from "../../../../core/componentWithStoreComputed";
import { storeBindingsBehavior } from "mobx-miniprogram-bindings";
import { store } from "../../../../store/index";

ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior],
  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: Array,
      value: [] as Home.IRecommendHotel[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },
  storeBindings: {
    store,
    fields: ['geoInfo'],
    actions: {}
  } as const,
  customInstanceProperty: {
    compInstance: null as Nullable<TMapInstance>
  },

  lifetimes: {
    attached() {
      this.compInstance = this.selectComponent('.nearby-hotel-map') as TMapInstance;
    }
  },

  observers: {
    list() {
      if (this.data.list && this.compInstance) {
        const list: IMarkerOption[] = [];
        this.data.list.forEach(item => {
          list.push({
            id: Number(item.outId),
            longitude: item.longitude,
            latitude: item.latitude,
            content: String(item.minPrice),
            hasBorder: true,
            borderRadius: true,
            active: true
          });
        });
        this.compInstance.setMarkers({
          markers: list
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})