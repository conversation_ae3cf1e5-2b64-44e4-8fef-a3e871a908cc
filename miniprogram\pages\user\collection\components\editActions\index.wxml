<!-- 编辑模式底部操作栏组件 -->
<view class="bottom-actions" wx:if="{{visible}}">
  <!-- 左侧：全选按钮 -->
  <view class="action-left">
    <view class="select-all-btn" bindtap="onToggleSelectAll">
      <image class="checkbox-icon" src="{{isAllSelected ? circleActIcon : circleIcon}}" mode="aspectFit" />
      <text class="select-all-text">全选</text>
    </view>
  </view>

  <!-- 右侧：操作按钮 -->
  <view class="action-right">
    <button class="cancel-btn" bindtap="onBatchAction">
      {{actionText}}
    </button>
  </view>
</view>
