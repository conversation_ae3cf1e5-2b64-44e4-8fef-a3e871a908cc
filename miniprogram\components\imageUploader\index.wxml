<!--components/imageUploader/index.wxml-->
<view class="image-uploader">
  <!-- 自定义标题区域 -->
  <view wx:if="{{showTitle}}" class="uploader-header">
    <slot name="title"></slot>
    <view wx:if="{{!hasCustomTitle}}" class="default-title">
      <text class="section-title">{{titleText}} {{value.length}}/{{maxCount}}</text>
    </view>
  </view>

  <!-- 图片上传区域 -->
  <scroll-view class="upload-scroll" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view class="upload-grid">
      <!-- 已上传的图片 -->
      <view
        wx:for="{{value}}"
        wx:key="index"
        class="upload-item image-item"
        data-index="{{index}}"
        bindtap="onPreviewImage"
      >
        <image class="uploaded-image" src="{{item}}" mode="aspectFill"></image>
        <view class="delete-btn" data-index="{{index}}" catchtap="onDeleteImage">
          <image class="delete-icon" src="{{deleteIcon}}" mode="aspectFit"></image>
        </view>
      </view>

      <!-- 上传按钮 -->
      <view
        wx:if="{{value.length < maxCount}}"
        class="upload-item upload-btn {{uploading ? 'uploading' : ''}}"
        bindtap="onUploadImage"
      >
        <image wx:if="{{!uploading}}" class="upload-icon" src="{{uploadIcon}}" mode="aspectFit"></image>
        <text class="upload-text">{{uploading ? uploadingText : uploadText}}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 自定义内容区域 -->
  <view wx:if="{{hasCustomContent}}" class="uploader-content">
    <slot name="content"></slot>
  </view>
</view>
