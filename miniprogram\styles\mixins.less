.border(@width: 1px , @style: solid, @color: #e0e0e0, @radius: 0, @side: all) {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(0.5);
    transform-origin: 0 0;
    box-sizing: border-box;
    pointer-events: none;
    // 处理圆角
    & when not (@radius = 0) {
      border-radius: @radius * 2;
    }
    
    // 处理边框
    .border-process() when (@side = all) {
      border: @width @style @color;
    }
    
    .border-process() when not (@side = all) {
      border-@{side}: @width @style @color;
    }
    
    .border-process();
  }
}

@static-base-url: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota';

@primary-color: #0198FF;
@warning-color: #FB7A1E;
@primary-light-color-2: #EEF8FF;
@text-light-color: #99999E;
@text-color: #66666E;
@text-title-color: #33333E;
@text-deep-color: #000;
@placeholder-text-color: #CCCCCE;
@border-color: #F7F7F7;
@bg-gray-color: #F3F3F3;
@bg-light-gray-color: #F9F9F9;
/** 定义tdesign主题 */
@brand-color: var(--td-brand-color, @primary-color); // 主题色

