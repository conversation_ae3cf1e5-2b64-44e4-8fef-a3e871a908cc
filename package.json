{"name": "miniprogram-ts-less-quickstart", "version": "1.0.1", "description": "", "scripts": {"deploy": "./build/deploy.sh", "deploy:help": "./build/deploy.sh --help"}, "keywords": [], "author": "", "license": "", "resolutions": {"miniprogram-api-typings": "./typings/miniprogram-api-typings"}, "dependencies": {"dayjs": "^1.11.13", "miniprogram-computed": "^7.0.0", "mobx-miniprogram": "^6.12.3", "mobx-miniprogram-bindings": "^5.1.1", "tdesign-miniprogram": "^1.9.4", "umtrack-wx": "^2.8.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "miniprogram-api-typings": "./typings/miniprogram-api-typings", "miniprogram-ci": "^2.1.14", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}