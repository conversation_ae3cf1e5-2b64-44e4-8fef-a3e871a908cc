<t-popup placement="bottom" visible="{{visible}}" bind:visible-change="handleVisibleChange">
	<view class="popup-body">
		<view class="popup-title">
			<text catch:tap="handleClose">取消</text>
			<text class="main-title">活动人数</text>
			<text class="confirm-trigger" catch:tap="handleSubmit">确认</text>
		</view>
		<view class="form-item">
			<view class="form-item-label">活动人数上限 *</view>
			<view class="form-item-content">
				<input value="{{maxNum}}" type="number" placeholder="填写人数" placeholder-style="color: #ccccce;font-weight: normal;" bindinput="handleValueChange" data-type="maxNum" />
				<text>人</text>
			</view>
		</view>
		<view class="form-item">
			<view class="form-item-label">活动人数下限</view>
			<view class="form-item-tips">活动报名人数不满人数下限，活动将自动取消</view>
			<view class="form-item-content">
				<input value="{{minNum}}" type="number" placeholder="填写人数" placeholder-style="color: #ccccce;font-weight: normal;" bindinput="handleValueChange" data-type="minNum" />
				<text>人</text>
			</view>
		</view>
	</view>
</t-popup>