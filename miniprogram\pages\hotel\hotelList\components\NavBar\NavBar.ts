Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 城市名称
    cityName: {
      type: String,
      value: '杭州'
    },
    // 城市ID
    cityId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    menuButtonHeight: 0,
    menuButtonTop: 0,
    navBarHeight: 0,
    menuPaddingRight: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 获取状态栏高度和胶囊按钮位置信息
      const windowInfo = wx.getWindowInfo();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      // 计算导航栏高度 = 胶囊按钮顶部距离 + 胶囊按钮高度 + 顶部间距
      const navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - windowInfo.statusBarHeight) * 2;
      
      this.setData({
        statusBarHeight: windowInfo.statusBarHeight,
        menuButtonHeight: menuButtonInfo.height,
        menuButtonTop: menuButtonInfo.top,
        navBarHeight: navBarHeight,
        menuPaddingRight: windowInfo.windowWidth - menuButtonInfo.left
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理返回按钮点击
     */
    handleBack() {
      wx.navigateBack({
        delta: 1
      });

    },

    /**
     * 处理城市选择点击
     */
    handleCitySelect() {
      // 这里可以跳转到城市选择页面
      wx.navigateTo({
        url: '/pages/hotel/jump/jump?cityId='+this.properties.cityId+'&cityName='+this.properties.cityName
      });
      // 由于城市选择页面还未开发，这里只触发事件
      // this.triggerEvent('citySelect', {
      //   cityId: this.properties.cityId,
      //   cityName: this.properties.cityName
      // });
    }
  }
}) 