/** 本地存储 Key */
export enum StorageKeyEnum {
	/** 用户 token */
	Token = 'HT-Token',
	/** 用户信息 */
	UserInfo = 'dm_userInfo',
	ActivityData = 'activity-data'
}


export enum APIResponseCode {
	/** 成功 */
	SUCCESS = 200,
	/** 请求参数错误 */
	BAD_REQUEST = 400,
	/** 未授权 */
	UNAUTHORIZED = 401,
	/** 禁止访问 */
	FORBIDDEN = 403,
	/** 未找到 */
	NOT_FOUND = 404,
	/** 方法不允许 */
	BAD_METHOD = 405,
	/** 服务器异常 */
	ERROR = 500,
}