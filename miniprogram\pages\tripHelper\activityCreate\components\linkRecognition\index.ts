import { generateRoute } from '../../../../../api/tripHelper/activity';

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    linkUrl: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.triggerEvent('update-visible', e.detail);
    },
    handleInputChange(e: WechatMiniprogram.CustomEvent<{value: string}>) {
      this.setData({
        linkUrl: e.detail.value
      })
    },
    async handleSubmit() {
      const linkUrl = this.data.linkUrl;
      if (!linkUrl) {
        wx.displayToast({
          title: '链接不能为空',
          icon: 'none',
          duration: 3000
        })
        return;
      }
      const res = await generateRoute({ prompt: linkUrl });
      if (res.isSuccess) {
        const { title, output, images } = res.data ?? {};
        this.triggerEvent('update-visible', { visible: false });
        this.triggerEvent('change', {
          title,
          description: output,
          coverImages: images
        });
      }
    },
    handleClose() {
      this.triggerEvent('update-visible', { visible: false });
    }
  }
})