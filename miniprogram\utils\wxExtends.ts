/** 微信API扩展 */
wx.loadingCount = 0;
wx.toastQueue = [];

wx.displayToast = (options) => {
	const toastOptions: WechatMiniprogram.ShowToastOption = typeof options === 'string'
		? { title: options, icon: 'none' }
		: options;

	// 如果有 loading 显示，先加入队列
	if (wx.loadingCount > 0) {
		wx.toastQueue.push(toastOptions);
		return;
	}

	showToastForce(toastOptions);
}

wx.displayLoading = (options) => {
	wx.loadingCount++;
	if (wx.loadingCount === 1) {
		wx.showLoading({ ... options });
	}
}

wx.hidePrevLoading = (options) => {
	if (wx.loadingCount <= 0) return;

	wx.loadingCount--;
	if (wx.loadingCount === 0) {
		wx.hideLoading(options);
		showLastToast();
	}
}

/** 显示最近的toast */
const showLastToast = () => {
	if (wx.toastQueue.length > 0 && wx.loadingCount === 0) {
		const lastToast = wx.toastQueue.pop()!;
		showToastForce(lastToast);
	}
}

/** 直接显示toast */
const showToastForce = (options: WechatMiniprogram.ShowToastOption) => {
	wx.toastQueue = [];

	wx.showToast({
		duration: 3000,
		icon: 'none',
		...options
	});
}