/* 昵称修改弹窗内容样式 - 底部弹出层 */
.nickname-popup-content {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

/* 弹窗头部样式 - 居中标题 */
.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 40rpx;
}

/* 弹窗标题样式 */
.popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

/* 昵称输入框样式 - 圆角输入框 */
.nickname-input {
  height: 108rpx;
  border-radius: 220rpx;
  background: #F9F9F9;
  padding-left: 32rpx;
  font-size: 32rpx;
}

/* 昵称输入容器样式 - 相对定位，用于字符计数 */
.nickname-input-container {
  position: relative;
  margin-bottom: 48rpx;
}

/* 字符计数标签样式 - 右上角定位 */
.nickname-input-label {
  position: absolute;
  right: 32rpx;
  color: #CCCCCE;
  font-size: 26rpx;
  top: 35%; 
}

/* 按钮容器样式 - 水平等分布局 */
.popup-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 取消按钮样式 - 灰色背景 */
.popup-button-cancel {
  flex: 1;
  height: 104rpx;
  line-height: 104rpx;
  display: inline-block;
  text-align: center;
  background: #f7f7f7;
  color: #11111e;
  border-radius: 120rpx;
  margin-right: 16rpx;
}

/* 保存按钮样式 - 蓝色背景 */
.popup-button-save {
  height: 104rpx;
  line-height: 104rpx;
  text-align: center;
  flex: 1;
  display: inline-block;
  background: #568ded;
  color: #fff;
  border-radius: 120rpx;
}
