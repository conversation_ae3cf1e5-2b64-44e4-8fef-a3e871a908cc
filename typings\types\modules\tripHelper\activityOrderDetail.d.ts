declare namespace ActivityOrderDetail {
  interface IOrderDetailParams{
    orderNo: string | number;
  }
  interface IOrderDetailResponse {
    /** 订单编号 */
    orderNo: string;
    /** 订单状态 (1-待支付) */
    orderStatus: number;
    /** 订单状态描述 */
    orderStatusDesc: string;
    /** 订单状态友好提示 */
    orderStatusGoodTips:string;
    /** 订单总金额 */
    totalAmount: number;
    /** 老人数量 */
    oldPersonCount: number;
    /** 老人票价 */
    oldPersonPrice: number;
    /** 儿童数量 */
    childrenCount: number;
    /** 儿童票价 */
    childrenPrice: number;
    /** 成人数量 */
    adultCount: number;
    /** 成人票价 */
    adultPrice: number;
    /** 总票数 */
    totalTicketsCount: number;
    /** 支付方式 */
    paymentType: string;
    /** 订单创建时间 */
    createTime: string;
    /** 支付时间 */
    paymentTime: string;
    /** 活动ID */
    activityId: number;
    /** 活动名称 */
    activityName: string;
    /** 活动主图URL */
    mainImageUrl: string;
    /** 活动开始时间 */
    activityBeginDate: string;
    /** 二维码图片URL */
    qRCodeImageUrl: string;
    /** 城市ID */
    cityId: string;
    /** 城市名称 */
    cityName: string;
    /** 退款金额 */
    refundAmount:number;
    [key: string]: any;
  }
  interface ICheckInfoParams{
    activityId:string | number;
  }
  interface Location {
    name: string;
    longitude: string | number;
    latitude: string | number;
  }
  interface ICheckInfoResponse{
    recommendStar: number;
    position: Location;
    pics: string[];
    comment: string;
  }
  interface ICancelOrderResponse{
    orderNo:string | number;
    cancelTime:string;
  }
}