/**
 * 状态显示组件类型定义
 */

/**
 * 状态显示类型枚举
 */
export enum StatusDisplayType {
  DataEmpty = 'data-empty',
  NetworkError = 'network-error',
  ContentEmpty = 'content-empty',
  LoadFailed = 'load-failed',
  Empty = 'empty',
  Error = 'error'
}

/**
 * 状态显示组件属性接口
 */
export interface StatusDisplayProps {
  /** 状态类型 */
  type?: StatusDisplayType | string;
  /** 自定义标题 */
  title?: string;
  /** 是否显示组件 */
  visible?: boolean;
  /** 自定义样式类名 */
  customClass?: string;
  /** 自定义内联样式 */
  customStyle?: string;
}

/**
 * 状态显示组件事件接口
 */
export interface StatusDisplayEvents {
  /** 按钮点击事件 */
  buttonclick: {
    type: string;
  };
}
