.navbar-container {
  position: flex;
  top: 0;
  z-index: 10;
  width: 100%;
  background-color: #FFFFFF;

  .navbar-placeholder {
    width: 100%;
  }
  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .navbar-left{
      margin-left: 14rpx;
      width: 36rpx;
      height: 36rpx;
    }
    
    .navbar-title{
      font-weight: 500;
      font-size: 32rpx;
      color: #000000;
    }
    .navbar-right{
      width: 36rpx;
      height: 36rpx;
      margin-right: 14rpx;
    }
  }
}








/* 中间城市选择区域 */
.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-city-wrapper {
  display: flex;
  align-items: center;
}

.navbar-city-name {
  font-size: 30rpx;
  max-width: 320rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navbar-city-arrow {
  width: 0;
  height: 0;
  margin-left: 8rpx;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #333;
  display: inline-block;
  vertical-align: middle;
}