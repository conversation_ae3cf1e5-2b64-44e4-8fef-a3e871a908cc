/**
 * 昵称修改弹框组件
 * 用于修改用户昵称的底部弹出层组件
 */

/**
 * 组件属性接口定义
 */
interface NicknamePopupProps {
  /** 弹窗是否可见 */
  visible: boolean;
  /** 当前昵称值 */
  currentNickname: string;
}

/**
 * 组件数据接口定义
 */
interface NicknamePopupData {
  /** 输入的昵称 */
  nickname: string;
  /** 字符计数 */
  charCount: number;
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /** 弹窗是否可见 */
    visible: {
      type: Boolean,
      value: false
    },
    /** 当前昵称值 */
    currentNickname: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    /** 输入的昵称 */
    nickname: '',
    /** 字符计数 */
    charCount: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例进入页面节点树时执行
     */
    attached() {
      // 初始化昵称和字符计数
      this.initNickname();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    /**
     * 监听弹窗显示状态和当前昵称变化
     * @param visible 弹窗是否可见
     * @param currentNickname 当前昵称
     */
    'visible, currentNickname': function(visible: boolean, currentNickname: string) {
      if (visible) {
        // 弹窗显示时，重新初始化昵称
        this.setData({
          nickname: currentNickname || '',
          charCount: (currentNickname || '').length
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化昵称数据
     */
    initNickname() {
      const currentNickname = this.properties.currentNickname || '';
      this.setData({
        nickname: currentNickname,
        charCount: currentNickname.length
      });
    },

    /**
     * 处理昵称输入事件
     * @param e 输入事件对象
     */
    onNicknameChange(e: WechatMiniprogram.Input) {
      const value = e.detail.value;
      this.setData({
        nickname: value,
        charCount: value.length
      });
    },

    /**
     * 处理输入框获得焦点事件
     */
    onInputFocus() {
      // 可以在这里添加获得焦点时的逻辑
    },

    /**
     * 处理输入框失去焦点事件
     */
    onInputBlur() {
      // 可以在这里添加失去焦点时的逻辑
    },

    /**
     * 处理取消按钮点击事件
     * 触发取消事件并关闭弹窗
     */
    onCancel() {
      this.triggerEvent('cancel');
    },

    /**
     * 处理保存按钮点击事件
     * 验证昵称并触发保存事件
     */
    onSave() {
      const nickname = this.data.nickname.trim();
      
      // 昵称不能为空
      if (!nickname) {
        wx.displayToast({ 
          title: '昵称不能为空', 
          icon: 'none' 
        });
        return;
      }

      // 触发保存事件，传递新昵称
      this.triggerEvent('save', { 
        nickname: nickname 
      });
    }
  }
});
