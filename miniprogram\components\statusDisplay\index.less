/* components/statusDisplay/index.less */
.status-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &__image {
    margin-bottom: 32rpx;
  }

  &__img {
    width: 255rpx;
    height: 255rpx;
  }

  &__title {
    font-size: 28rpx;
    font-weight: 400;
    color: #66666E;
    text-align: center;
  }

  &__description {
    font-size: 28rpx;
    color: #66666E;
    text-align: center;
  }

  &__button {
    margin-top: 24rpx;
  }

  &__btn {
    background: #1890FF;
    color: #FFFFFF;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 48rpx;
    padding: 24rpx 48rpx;
    border: none;
    min-width: 200rpx;
    
    &::after {
      border: none;
    }

    &:active {
      background: #0E7CE8;
    }
  }
}