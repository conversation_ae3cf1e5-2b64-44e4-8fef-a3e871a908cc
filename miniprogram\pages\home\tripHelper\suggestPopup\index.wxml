<!--components/suggestPopup/index.wxml-->
<t-popup visible="{{visible}}" placement="bottom" bind:visible-change="onClose">
  <view class="suggest-popup">
    <!-- 弹框头部 -->
    <view class="popup-header">
      <text class="popup-title">我来支一招</text>
      <view class="close-btn" bindtap="onClose">
        <image class="close-icon" src="{{closeIcon}}" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 弹框内容 -->
    <view class="popup-content">
      <!-- 详细意见 -->
      <view class="suggestion-section">
        <text class="section-title">详细意见</text>
        <view class="suggestion-input-wrapper">
          <!-- 自定义placeholder提示 -->
          <view wx:if="{{!suggestion}}" class="custom-placeholder">
            <text class="placeholder-line">最近出门遇到啥烦恼了吗？</text>
            <text class="placeholder-line">有什么功能你觉得"要是有就太好了"？</text>
            <text class="placeholder-line">随便说说，我们认真听！</text>
          </view>
          <textarea
            class="suggestion-input"
            placeholder=""
            value="{{suggestion}}"
            bindinput="onInput"
            maxlength="{{maxTextLength}}"
            auto-height
          ></textarea>
          <view class="char-count">{{suggestion.length}}/{{maxTextLength}}</view>
        </view>
      </view>

      <!-- 上传图片 -->
      <view class="upload-section">
        <image-uploader
          id="image-uploader"
          value="{{uploadedImageUrls}}"
          title-text="上传图片"
          max-count="{{maxImageCount}}"
          has-custom-title="{{false}}"
          bind:change="onImageChange"
          bind:uploadSuccess="onUploadSuccess"
          bind:uploadError="onUploadError"
        />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="popup-footer">
      <button class="submit-btn" bindtap="onSubmit">提交</button>
    </view>
  </view>
</t-popup>
