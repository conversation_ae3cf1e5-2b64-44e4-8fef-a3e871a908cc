<!--pages/order/list/components/searchPopup/index.wxml-->
<t-popup
  visible="{{visible}}"
  placement="left"
  bind:visible-change="onVisibleChange"
  custom-style="width: 75vw;"
>
  <view class="search-content">
    <!-- 搜索内容 -->
    <scroll-view class="search-body" scroll-y="{{true}}">
      <!-- 搜索输入框 -->
      <view class="search-input-section">
        <view class="search-input-container">
          <image src="{{searchIcon}}" class="search-icon" />
          <input
            class="search-input"
            value="{{keyword}}"
            placeholder="位置/品牌/酒店名"
            bind:input="onInputChange"
            bind:confirm="onSearchClick"
            focus="{{true}}"
            confirm-type="search"
          />
          <view class="clear-btn" wx:if="{{keyword}}" bindtap="onClearInput">
            <t-icon name="close" size="32rpx" color="#999" />
          </view>
        </view>
      </view>
    
    <!-- 历史搜索 -->
    <view class="search-section" wx:if="{{searchHistory.length > 0}}">
      <view class="section-title">历史搜索</view>
      <view class="tag-grid">
        <view
          class="tag-item"
          wx:for="{{searchHistory}}"
          wx:key="index"
          data-keyword="{{item}}"
          bindtap="onHistoryClick"
        >
          {{item}}
        </view>
      </view>
    </view>

    <!-- 分类搜索 -->
    <view class="search-section" wx:if="{{categoryList.length > 0}}">
      <view class="section-title">分类搜索</view>
      <view class="tag-grid">
        <view
          class="tag-item {{selectedCategory === item.key ? 'selected' : ''}}"
          wx:for="{{categoryList}}"
          wx:key="key"
          data-key="{{item.key}}"
          bindtap="onCategoryClick"
        >
          {{item.value}}
        </view>
      </view>
    </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="search-footer">
      <t-button
        theme="primary"
        size="large"
        bind:tap="onSearchClick"
        disabled="{{!keyword.trim() && !selectedCategory}}"
        class="search-btn"
      >
        搜索
      </t-button>
    </view>
  </view>
</t-popup>
