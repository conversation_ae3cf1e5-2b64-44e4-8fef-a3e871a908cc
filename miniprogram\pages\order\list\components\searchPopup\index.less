/* pages/order/list/components/searchPopup/index.less */
.search-content {
  padding-top: 122rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .search-body {
    flex: 1;
    box-sizing: border-box;
    padding: 0 32rpx 120rpx 32rpx;

    .search-input-section {
      padding: 32rpx 0;
      margin-bottom: 32rpx;

      .search-input-container {
        display: flex;
        align-items: center;
        height: 72rpx;
        background-color: #F3F3F3;
        border-radius: 36rpx;
        padding: 0 24rpx;

        .search-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }

        .search-input {
          flex: 1;
          height: 100%;
          font-size: 28rpx;
          color: #11111E;
          background-color: transparent;
        }

        .clear-btn {
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 16rpx;

          &:active {
            opacity: 0.6;
          }
        }
      }
    }

    .search-section {
      margin-bottom: 64rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #11111E;
        margin-bottom: 32rpx;
      }

      .tag-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24rpx;

        .tag-item {
          height: 88rpx;
          display: flex;
          min-width: 166rpx;
          align-items: center;
          justify-content: center;
          background-color: #F3F3F3;
          border-radius: 120rpx;
          font-size: 28rpx;
          color: #11111E;
          transition: all 0.2s ease;
          border: 1rpx solid transparent;

          &:active {
            background-color: #ebebeb;
          }

          &.selected {
            background-color: #568DED;
            color: #fff;
            font-weight: 500;
          }
        }
      }
    }
  }
}
