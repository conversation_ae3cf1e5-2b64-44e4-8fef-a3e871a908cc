/* pages/order/list/components/customHeader/index.less */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;

  .header-content {
    height: 108rpx;
    display: flex;
    align-items: center;
    padding: 0 32rpx;

    .back-btn {
      width: 36px;
      height: 36rpx;
      display: flex;
      align-items: center;

      &:active {
        opacity: 0.6;
      }
    }

    .search-container {
      height: 76rpx;
      width: 314rpx;
      background-color: #f5f5f5;
      border-radius: 32rpx;
      display: flex;
      align-items: center;
      padding: 0 24rpx;
      gap: 24rpx;

      .search-placeholder {
        font-size: 32rpx;
        color: #CCCCCE;
      }

      .search-keyword {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      &:active {
        background-color: #ebebeb;
      }
    }

    .filter-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      &:active {
        opacity: 0.6;
      }
    }
  }
}
