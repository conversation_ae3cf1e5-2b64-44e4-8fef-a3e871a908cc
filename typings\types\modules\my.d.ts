declare namespace My {
  /**
   * 活动查询参数
   */
  interface IActivityQueryParams {
    /** 页码 */
    page: number;
    /** 每页大小 */
    size: number;
  }

  /**
   * 行程订单列表查询参数
   */
  interface ITrailOrderListRequest {
    /** 页码 */
    pageIndex: number;
    /** 每页大小 */
    pageSize: number;
  }

  /**
   * 行程订单项
   */
  interface ITrailOrderItem {
    /** 活动ID */
    activityId: number;
    /** 活动名称 */
    activityName: string;
    /** 创建时间 */
    createTime: string;
    /** 订单状态 */
    orderStatus: number;
    /** 订单状态描述 */
    orderStatusDesc: string;
    /** 订单金额 */
    totalAmount: number;
    /** 购买老年数量 */
    oldPersonCount: number;
    /** 老年价格 */
    oldPersonPrice: number | null;
    /** 购买儿童数量 */
    childrenCount: number;
    /** 儿童价格 */
    childrenPrice: number | null;
    /** 购买成人数量 */
    adultCount: number;
    /** 成人价格 */
    adultPrice: number;
    /** 订单号 */
    orderNo: string;
    /** 图片地址 */
    mainImagUrl: string | null;
    /** 退款金额 */
    refundAmount: number | null;
    /** 退款比例 */
    refundRate: number | null;
  }

  /**
   * 行程订单列表响应
   */
  interface ITrailOrderListResponse {
    /** 当前页码 */
    currentPage: number;
    /** 每页条数 */
    pageSize: number;
    /** 总条数 */
    totalCount: number;
    /** 总页数 */
    totalPage: number;
    /** 订单列表 */
    trailOrderItemList: ITrailOrderItem[];
  }

  /**
   * 活动DTO
   */
  interface IActivityDTO {
    /** 活动ID */
    id: number;
    /** 用户ID */
    userId: number;
    /** 活动标题 */
    title: string;
    /** 活动描述 */
    description: string;
    /** 封面图片列表 */
    coverImages: string[];
    /** 活动类型 */
    activityType: string;
    /** 报名截止时间 */
    signUpDeadLineTime: string;
    /** 线路信息 */
    routeInfo: {
      /** 线路ID */
      routeId: number;
      /** 线路名称 */
      name: string;
      /** 渠道 */
      channel: number[];
      /** 项目 */
      project: number[];
      /** 描述 */
      description: string;
      /** 城市列表 */
      cities: string[];
      /** 城市名称列表 */
      cityNames: string[];
      /** 渠道名称列表 */
      channelName: string[];
      /** 项目名称列表 */
      projectName: string[];
      /** 图片URL列表 */
      picUrls: string[];
      /** 行程信息列表 */
      journeyInfos: {
        /** 第几天 */
        dayNo: number;
        /** 时间 */
        time: string;
        /** 地点列表 */
        locations: {
          /** 地点ID */
          locationId: number;
          /** 地点名称输入 */
          locationNameInput: string;
          /** 地点信息 */
          location: {
            /** 地点ID */
            locationId: number;
            /** 地点名称 */
            name: string;
            /** 地址 */
            address: string;
            /** 类型 */
            type: number;
            /** 类型名称 */
            typeName: string;
            /** 分类 */
            category: number;
            /** 分类名称 */
            categoryName: string;
            /** 游玩标签 */
            playTag: number[];
            /** 游玩标签名称 */
            playTagName: string[];
            /** 游玩内容 */
            playContent: number[];
            /** 游玩内容名称 */
            playContentName: string[];
            /** 其他标签 */
            otherTag: number[];
            /** 其他标签名称 */
            otherTagName: string[];
            /** 联系方式 */
            contact: string;
            /** 描述 */
            description: string;
            /** 图片列表 */
            pics: {
              url: string | null;
              isMain: boolean | null;
            }[];
            /** 图片URL列表 */
            picUrls: string[];
            /** 视频列表 */
            videos: string[];
          };
          /** 描述 */
          description: string;
        }[];
      }[];
    };
    /** 活动开始时间 */
    startTime: string;
    /** 日期描述 */
    dateDesc: string;
    /** 最大人数 */
    maxPeopleCount: number;
    /** 最小人数 */
    minPeopleCount: number;
    /** 当前人数 */
    currentPeopleCount: number;
    /** 人员费用 */
    perCost: {
      /** 费用 */
      perCost: number;
      /** 类型 */
      type: number;
    }[];
    /** 退款政策 */
    policyDTO: {
      /** 政策ID */
      id: number;
      /** 政策名称 */
      name: string;
      /** 政策描述 */
      desc: string;
      /** 政策详情 */
      policyDetails: {
        /** 详情ID */
        id: number;
        /** 退款规则 */
        refundRule: number;
        /** 时间类型 */
        timeType: number;
        /** 时间 */
        time: number;
        /** 是否可退款 */
        canRefund: number;
        /** 退款比例 */
        refundProportion: number;
      }[];
    };
    /** 是否允许候补 */
    allowWaiting: number;
    /** 活动状态 */
    status: number;
    /** 微信图片 */
    wxImage: string;
    /** 是否显示候补按钮 */
    showAlternateButton: boolean;
    /** 是否显示详情按钮 */
    showDetailBtn: boolean;
    /** 是否显示签到按钮 */
    showSignInBtn: boolean;
    /** 是否显示打卡按钮 */
    showCheckinBtn: boolean;
    /** 是否显示报名按钮 */
    showSignUpBtn: boolean;
    /** 是否显示活动管理按钮 */
    showActivityManageBtn: boolean;
    /** 是否已签到 */
    ifSignIn: boolean;
    /** 是否已打卡 */
    ifCheckIn: boolean;
  }

  /**
   * 分页活动查询响应
   */
  interface IActivityPageResponse {
    /** 当前页 */
    current: number;
    /** 每页大小 */
    size: number;
    /** 总数 */
    total: number;
    /** 活动列表 */
    records: IActivityDTO[];
    /** 是否有下一页 */
    hasNext: boolean;
  }

  /**
   * API响应基础结构
   */
  interface IApiResponse<T> {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 响应数据 */
    data: T;
  }
}
