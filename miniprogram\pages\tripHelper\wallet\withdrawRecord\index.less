.withdraw-record-page {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding: 24rpx;

  .record-list {
    .record-item {
      background-color: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx 32rpx;
      margin-bottom: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .record-left {
        flex: 1;

        .amount {
          font-size: 40rpx;
          color: #33333E;
          font-weight: 500;
          margin-bottom: 8rpx;
        }

        .date {
          font-size: 24rpx;
          color: #99999E;
        }
      }

      .record-right {
        .status {
          font-size: 24rpx;
          font-weight: 400;

          // 提现中 - 橙色
          &.status-41 {
            color: #FB7A1E;
          }

          // 提现成功 - 蓝色
          &.status-43 {
            color: #00C2CC;
          }

          // 提现失败 - 红色
          &.status-44 {
            color: #FF4757;
          }

          // 默认状态 - 灰色
          &:not(.status-41):not(.status-43):not(.status-44) {
            color: #99999E;
          }
        }
      }
    }
  }

  /* 骨架屏样式 */
  .skeleton-container {
    padding: 0;

    .skeleton-item {
      background-color: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx 32rpx;
      margin-bottom: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .skeleton-left {
        flex: 1;

        .skeleton-amount {
          height: 40rpx;
          background: #f0f0f0;
          border-radius: 20rpx;
          margin-bottom: 12rpx;
          width: 60%;
        }

        .skeleton-date {
          height: 24rpx;
          background: #f0f0f0;
          border-radius: 12rpx;
          width: 40%;
        }
      }

      .skeleton-right {
        .skeleton-status {
          height: 24rpx;
          background: #f0f0f0;
          border-radius: 12rpx;
          width: 80rpx;
        }
      }
    }
  }
}
