// pages/order/list/components/filterPopup/index.ts
import { getOrderFilterConditions } from '../../../../../api/order';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer(newVal: boolean, oldVal: boolean) {
        console.log('筛选弹框 visible 属性变化:', { oldVal, newVal });
        if (newVal && !oldVal) {
          console.log('筛选弹框从隐藏变为显示，准备加载筛选条件');
          // 延迟一下确保组件已经完全初始化
          setTimeout(() => {
            this.loadFilterConditions();
          }, 100);
        } else if (newVal) {
          console.log('筛选弹框应该显示了');
        } else {
          console.log('筛选弹框应该隐藏了');
        }
      }
    },
    defaultFilter: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    filterGroups: [
      {
        title: "订单状态",
        type: "orderStatus",
        options: [
          { key: "valid", value: "有效" },
          { key: "invalid", value: "无效" }
        ]
      },
      {
        title: "预定时间",
        type: "orderDate",
        options: [
          { key: "within_1_year", value: "一年内" },
          { key: "2024", value: "2024年" },
          { key: "2023", value: "2023年" }
        ]
      },
      {
        title: "旅行产品",
        type: "type",
        options: [
          { key: "hotel", value: "酒店" },
          { key: "flight", value: "机票" }
        ]
      }
    ] as any[],
    selectedFilters: {
      orderStatus: '',
      orderDate: '',
      type: ''
    },
    loading: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭弹框
     */
    onClose() {
      console.log('筛选弹框关闭按钮被点击');
      this.triggerEvent('close');
    },
    /**
     * 加载筛选条件
     */
    async loadFilterConditions() {
      console.log('loadFilterConditions 方法被调用');
      console.log('当前状态:', {
        loading: this.data.loading,
        filterGroupsLength: this.data.filterGroups.length
      });

      if (this.data.loading || this.data.filterGroups.length > 0) {
        console.log('跳过加载：正在加载中或已有数据');
        return;
      }

      try {
        console.log('设置 loading 为 true');
        this.setData({ loading: true });

        console.log('开始调用 getOrderFilterConditions API...');

        // 测试API是否能正常导入和调用
        console.log('getOrderFilterConditions 函数:', getOrderFilterConditions);

        const response = await getOrderFilterConditions();

        console.log('筛选条件API响应:', response);

        if (response && response.data && Array.isArray(response.data)) {
          console.log('API响应成功，数据:', response.data);
          this.setData({
            filterGroups: response.data
          });

          console.log('筛选条件加载成功，设置到组件状态');
        } else {
          console.error('筛选条件加载失败，响应格式不正确:', response);
          wx.showToast({
            title: '加载筛选条件失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载筛选条件异常:', error);
        wx.showToast({
          title: '加载筛选条件失败',
          icon: 'none'
        });
      } finally {
        console.log('设置 loading 为 false');
        this.setData({ loading: false });
      }
    },


    /**
     * 选择筛选选项
     */
    onOptionSelect(e: WechatMiniprogram.BaseEvent) {
      console.log('筛选选项被点击，事件对象:', e);
      console.log('当前数据集:', e.currentTarget.dataset);

      const { type, key } = e.currentTarget.dataset;

      console.log('解析的数据:', { type, key });

      if (!type || key === undefined) {
        console.log('数据不完整，跳过处理');
        return;
      }

      const selectedFilters = { ...this.data.selectedFilters };

      console.log('当前选中状态:', selectedFilters);

      // 如果点击的是已选中的选项，则取消选择
      if (selectedFilters[type as keyof Order.FilterFormData] === key) {
        selectedFilters[type as keyof Order.FilterFormData] = '';
        console.log('取消选择:', type, key);
      } else {
        selectedFilters[type as keyof Order.FilterFormData] = key;
        console.log('选择:', type, key);
      }

      console.log('更新后的选中状态:', selectedFilters);
      this.setData({ selectedFilters });

      console.log('筛选选项变更完成:', { type, key, selectedFilters });
    },

    /**
     * 重置筛选条件
     */
    onReset() {
      this.setData({
        selectedFilters: {
          orderStatus: '',
          orderDate: '',
          type: ''
        }
      });
      
      console.log('筛选条件已重置');
    },

    /**
     * 确认筛选
     */
    onConfirm() {
      const { selectedFilters } = this.data;
      
      // 触发筛选事件，传递选中的筛选条件
      this.triggerEvent('filter', selectedFilters);
      
      // 关闭弹框
      this.onClose();
      
      console.log('确认筛选:', selectedFilters);
    },

    /**
     * popup visible-change 事件
     */
    onVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      const { visible } = e.detail;

      console.log('筛选弹框 visible 变化:', visible);

      if (visible) {
        console.log('筛选弹框显示，开始加载筛选条件...');

        // 弹框显示时加载筛选条件
        this.loadFilterConditions();

        // 初始化选中状态（如果有默认值）
        const defaultFilter = this.properties.defaultFilter as any || {};
        this.setData({
          selectedFilters: {
            orderStatus: defaultFilter.orderStatus || '',
            orderDate: defaultFilter.orderDate || '',
            type: defaultFilter.type || ''
          }
        });
      }

      if (!visible) {
        this.triggerEvent('close');
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('筛选弹框组件已挂载');
    },

    detached() {
      console.log('筛选弹框组件已卸载');
    }
  }
});
