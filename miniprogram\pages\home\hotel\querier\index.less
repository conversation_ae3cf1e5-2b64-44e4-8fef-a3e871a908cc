.location-and-keyword {
	display: flex;
	align-items: center;
	.border(1px, solid, #F3F3F3, 0, bottom);
	padding-bottom: 24rpx;
	.location-wrap {
		display: flex;
		justify-content: space-between;
		flex: 1;
		font-size: 36rpx;
		font-weight: bold;
		.location-trigger {
			width: 40rpx;
		}
	}
	.separator {
		.border(1px, solid, #F0F0F0, 0, left);
		height: 32rpx;
		margin: 0 30rpx;
	}
	.keyword-wrap {
		display: flex;
		flex: 1;
		font-size: 36rpx;
		font-weight: bold;
	}
}

.date-container {
	padding: 24rpx 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.border(1px, solid, #F3F3F3, 0, bottom);
	.date-wrap {
		font-size: 24rpx;
		color: #11111E;
		line-height: 28rpx;
		.text-number {
			font-weight: bold;
			font-size: 40rpx;
			line-height: 40rpx;
		}
		.text-date {
			font-size: 32rpx;
			font-weight: bold;
			line-height: 40rpx;
		}
	}
	.separator-wrap {
		position: relative;
		.separator-line {
			width: 142rpx;
			height: 0;
			.border(1px, solid, #CCCCCE, 0, top);
			position: absolute !important;
			top: 0;
			bottom: 0;
			z-index: 0;
			margin: auto;
			margin-left: 50%;
			transform: translateX(-50%);
		}
		.separator-text {
			padding: 6rpx 16rpx;
			line-height: 28rpx;
			font-size: 20rpx;
			color: #000;
			font-weight: bold;
			background-color: #fff;
			position: relative;
			.border(1px, solid, #CCCCCE, 20rpx);
		}
	}
}

.price-level {
	padding: 24rpx 0 32rpx;
	font-weight: bold;
}

.search-trigger {
	width: 100% !important;
	height: 112rpx;
	line-height: 112rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 56rpx;
	background-color: @primary-color;
	color: #fff;
}