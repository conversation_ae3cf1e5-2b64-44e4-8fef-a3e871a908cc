// pages/tripHelper/routeDetails/components/NavBar/NavBar.ts
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 线路名称
    routeName: {
      type: String,
      value: ''
    },
    // 标题最大宽度
    maxTitleWidth: {
      type: String,
      value: '400rpx'
    },
    // 显示标题的滚动阈值(px)
    scrollThreshold: {
      type: Number,
      value: 120
    },
    // 外部传入的滚动位置（性能优化模式）
    externalScrollTop: {
      type: Number,
      value: -1 // -1表示使用内部监听模式
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 系统信息
    statusBarHeight: 0,
    navBarHeight: 0,
    
    // 滚动状态
    scrollTop: 0,
    
    // 样式控制
    backgroundColor: 'rgba(255, 255, 255, 0)', // 背景色
    iconColor: '#ffffff', // 返回按钮颜色
    titleOpacity: 0, // 标题透明度
    titleVisible: false // 标题是否显示
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initSystemInfo();
    }
  },

  /**
   * 属性观察器
   */
  observers: {
    'externalScrollTop'(scrollTop: number) {
      // 外部模式：使用传入的scrollTop
      if (scrollTop >= 0) {
        this.updateNavBarState(scrollTop);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化系统信息
     */
    initSystemInfo() {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      // 计算导航栏高度 = 状态栏高度 + 44px（标准导航栏内容高度）
      const navBarHeight = systemInfo.statusBarHeight + 44;
      
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        navBarHeight: navBarHeight
      });
    },



    /**
     * 更新导航栏状态
     */
    updateNavBarState(scrollTop: number) {
      const threshold = this.properties.scrollThreshold;
      
      // 计算进度比例 (0-1)
      const progress = Math.min(scrollTop / threshold, 1);
      
      // 计算背景透明度
      const bgOpacity = progress;
      
      // 计算图标颜色 (白色到黑色)
      const iconColorValue = Math.round(255 * (1 - progress));
      const iconColor = `rgb(${iconColorValue}, ${iconColorValue}, ${iconColorValue})`;
      
      // 计算标题透明度 (在80%进度后开始显示)
      const titleOpacity = progress > 0.8 ? (progress - 0.8) / 0.2 : 0;
      const titleVisible = progress > 0.7;
      
      this.setData({
        scrollTop,
        backgroundColor: `rgba(255, 255, 255, ${bgOpacity})`,
        iconColor,
        titleOpacity,
        titleVisible
      });
    },

    /**
     * 处理返回按钮点击
     */
    onBackClick() {
      wx.navigateBack({
        delta: 1,
        fail: () => {
          // 如果返回失败（比如只有一个页面），则跳转到首页
          wx.switchTab({
            url: '/pages/home/<USER>/index'
          });
        }
      });
         }
   }
 });