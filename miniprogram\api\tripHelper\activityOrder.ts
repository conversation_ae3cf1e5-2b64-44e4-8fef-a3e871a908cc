import { request } from "../../utils/request";
// 初始化
export const initOrderApi = ( data : ActivityOrder.IOrderPagelParams ) => request<ActivityOrder.IOrderPageResponse>({
  url: `/trip/activity/v1/activityDetail`,
  method: "GET",
  data
})
// 创单
export const createTrailOrderApi = ( data : ActivityOrder.OrderSubmitParams ) => request<ActivityOrder.PaymentInfo>({
  url: `/hotel/order/active/createTrailOrder`,
  method: "POST",
  data,
  showLoading:true
})
// 支付
export const wechatPayApi = ( data : ActivityOrder.IOrderlParams ) => request<ActivityOrder.IOrderlResponse>({
  url: `/hotel/payment/wechatPay/v1/jsapi`,
  method: "POST",
  data,
  showLoading:true
})