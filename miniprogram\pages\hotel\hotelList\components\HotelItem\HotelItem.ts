// pages/hotel/hotelList/components/hotelItem/hotelItem.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 酒店数据对象
    hotelData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 图片基础URL
    imgBaseUrl: getApp().globalData.imgBaseUrl,
    // 图片加载状态管理
    imageLoadState: 'hidden', // 'hidden' | 'loaded' | 'error'
    showImage: false, // 控制图片显示
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化时设置默认状态
      if ((this.properties.hotelData as any)?.hotel?.thumbNailUrl) {
        this.setData({
          imageLoadState: 'hidden'
        });
      } else {
        this.setData({
          imageLoadState: 'error'
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理酒店卡片点击事件
     */
    handleHotelClick() {
      // 触发酒店点击事件，传递酒店数据
      this.triggerEvent('hotelClick', {
        hotelData: this.properties.hotelData
      });
    },

    /**
     * 图片加载成功处理
     */
    onImageLoad(e: any) {
      this.setData({
        imageLoadState: 'loaded',
        showImage: true
      });
      
      // 图片加载成功后，预加载其他可能需要的图片（如详情页图片）
      this.preloadRelatedImages();
    },

    /**
     * 图片加载失败处理
     */
    onImageError(e: any) {
      this.setData({
        imageLoadState: 'error',
        showImage: false
      });
      
      console.warn('Hotel image load failed:', (this.properties.hotelData as any)?.hotel?.thumbNailUrl);
    },

    /**
     * 预加载相关图片
     */
    preloadRelatedImages() {
      const hotelData = this.properties.hotelData as any;
      if (hotelData?.hotel?.imageUrls && Array.isArray(hotelData.hotel.imageUrls)) {
                 // 预加载前2张详情页图片
         hotelData.hotel.imageUrls.slice(0, 2).forEach((url: string) => {
           // 使用类型断言处理API兼容性
           const wxAny = wx as any;
           wxAny.preloadImageAsync && wxAny.preloadImageAsync(url);
         });
      }
    },

    /**
     * 获取占位图URL
     */
    getPlaceholderImage() {
      return `${this.data.imgBaseUrl}/common/hotel-placeholder.png`;
    }
  }
})