.popup-body {
	padding: 32rpx;
	.popup-title {
		display: flex;
		justify-content: space-between;
		color: @text-title-color;
		font-weight: bold;
		font-size: 36rpx;
		line-height: 50rpx;
		margin-bottom: 62rpx;
		.close-trigger {
			width: 36rpx;
			height: 36rpx;
			background-image: url('@{static-base-url}/close.png');
			background-size: 100%;
		}
	}
}

.popup-content {
	display: flex;
	justify-content: center;
}

.operate-wrap {
	padding: 16rpx 32rpx 68rpx;
	display: flex;
	.border(1px, solid, @border-color, 0, top);
	button {
		width: 100%;
		height: 108rpx;
		line-height: 108rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 54rpx;
		background-color: @primary-color;
		color: #fff;
	}
}