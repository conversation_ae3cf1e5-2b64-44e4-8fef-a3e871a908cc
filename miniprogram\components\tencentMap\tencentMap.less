@active-color: #568DED;

.map-container {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.custom-callout-item {
	padding: 8rpx 14rpx;
	background-color: #fff;
	border-radius: 8rpx;
	display: flex;
	font-size: 28rpx;
	line-height: 28rpx;
	align-items: baseline;
	box-sizing: border-box;
	
	&.is-active {
		background-color: @active-color;
		color: #fff;
		&::before, &::after {
			border-top-color: @active-color;
		}
	}

	&.is-radius {
		border-radius: 44rpx;
	}
	
	&.has-border {
		border: 1px solid #fff;
		&.is-active {
			&::before {
				border-top-color: #fff;
			}
		}
	}

	&::after,
	&::before {
		content: '';
		position: absolute;
		width: 0;
		height: 0;
		left: 0;
		right: 0;
		margin: auto;
		border-style: solid;
	}

	&::before {
		bottom: -12px;
		border-width: 12px 6px 0 6px;
		border-color: #fff transparent transparent transparent;
	}


	&::after {
		bottom: -10px;
		border-width: 12px 6px 0 6px;
		border-color: #fff transparent transparent transparent;
	}

	.prefix-text {
		font-size: 20rpx;
		line-height: 24rpx;
	}
}