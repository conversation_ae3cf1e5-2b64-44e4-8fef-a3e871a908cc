<!--components/statusDisplay/index.wxml-->
<view class="status-display {{customClass}}" style="{{customStyle}}" wx:if="{{visible}}">
  <!-- 插画图片 -->
  <view class="status-display__image">
    <image 
      src="{{displayImage}}" 
      mode="aspectFit"
      class="status-display__img"
    />
  </view>
  
  <!-- 标题 -->
  <view class="status-display__title" wx:if="{{displayTitle}}">
    {{displayTitle}}
  </view>
  
  <!-- 描述文字 -->
  <view class="status-display__description" wx:if="{{displayDescription}}">
    {{displayDescription}}
  </view>
  
  <!-- 操作按钮 -->
  <view class="status-display__button" wx:if="{{shouldShowButton}}">
    <button 
      class="status-display__btn"
      bindtap="onButtonClick"
    >
      {{displayButtonText}}
    </button>
  </view>
</view>