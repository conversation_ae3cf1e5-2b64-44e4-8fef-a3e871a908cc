declare namespace Registration {
  interface ISignUpPageParams{
    id:string | number
  }

  interface ActivitySignUpCountDTO {
    activityId: number;
    assemblyPlaceDTO: AssemblyPlaceDTO;
    userDTOList: UserDTO[];
    [key: string]: any;
  }
  
  interface AssemblyPlaceDTO {
    startTime: string;
    placeName: string;
    longitude: number;
    latitude: number;
  }
  
  interface PerNumDTO {
    count: number;
    type: number;
  }
  
  interface UserDTO {
    userId: string;
    userName: string;
    avatar: string;
    mobile: string;
    isSignIn:boolean;
    sex:number;
    perNumDTO: PerNumDTO[];
  }
  
  interface ISignUpPageResponse {
    activitySignUpCountDTOS: ActivitySignUpCountDTO[];
  }
}