<!-- 昵称修改底部弹出层组件 -->
<t-popup
  visible="{{ visible }}"
  placement="bottom"
  bind:overlay-click="onCancel"
  bind:close="onCancel"
  t-class-content="nickname-popup"
>
  <view class="nickname-popup-content">
    <!-- 弹窗头部：标题 -->
    <view class="popup-header">
      <text class="popup-title">修改昵称</text>
    </view>

    <!-- 昵称输入区域：包含输入框和字符计数 -->
    <view class="nickname-input-container">
      <input
        class="nickname-input"
        value="{{ nickname }}"
        placeholder="请输入昵称"
        maxlength="15"
        bind:input="onNicknameChange"
        bind:focus="onInputFocus"
        bind:blur="onInputBlur"
      />
      <!-- 字符计数显示 -->
      <view slot="label" class="nickname-input-label">{{ charCount }}/15</view>
    </view>

    <!-- 弹窗底部按钮区域：取消和保存 -->
    <view class="popup-buttons">
      <text class="popup-button-cancel" bindtap="onCancel">取消</text>
      <text class="popup-button-save" bindtap="onSave">保存修改</text>
    </view>
  </view>
</t-popup>
