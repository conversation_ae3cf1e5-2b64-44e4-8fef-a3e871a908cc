import { isPositiveInteger } from '../../../../../utils/index'

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    minValue: {
      type: Number,
      value: undefined
    },
    maxValue: {
      type: Number,
      value: undefined
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    minNum: '',
    maxNum: ''
  },
  observers: {
    visible() {
      if (this.data.visible) {
        this.setData({
          minNum: this.data.minValue ? String(this.data.minValue) : '',
          maxNum: this.data.maxValue ? String(this.data.maxValue) : ''
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.triggerEvent('update-visible', e.detail);
    },
    handleSubmit() {
      const isPassed = this.validate();
      if (!isPassed) return;

      this.triggerEvent('update-visible', { visible: false });
      this.triggerEvent('change', {
        min: this.data.minNum,
        max: this.data.maxNum
      });
    },
    handleClose() {
      this.triggerEvent('update-visible', { visible: false });
    },
    handleValueChange(e: WechatMiniprogram.CustomEvent<{ value: string }, {}, { type: string }>) {
      const value = e.detail.value;
      const { type } = e.currentTarget.dataset;
      this.setData({
        [type]: value
      });
    },
    validate() {
      const { maxNum, minNum } = this.data;

      // 检查是否为空
      if (!maxNum) {
        wx.displayToast({
          title: '活动人数上限不能为空',
          icon: 'none',
          duration: 3000
        });
        return false;
      }

      if (!isPositiveInteger(maxNum) || (minNum && !isPositiveInteger(minNum))) {
        wx.displayToast({
          title: '人数格式必须为正整数',
          icon: 'none',
          duration: 3000
        });
        return false;
      }

      // 检查最小值是否小于最大值（如果有最小值）
      if (minNum && Number(minNum) > Number(maxNum)) {
        wx.displayToast({
          title: '最小人数不能大于最大人数',
          icon: 'none',
          duration: 3000
        });
        return false;
      }

      return true;
    }
  }
})