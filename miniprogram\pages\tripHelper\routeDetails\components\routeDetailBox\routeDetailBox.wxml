<!-- components/routeDetailBox/routeDetailBox.wxml -->


  <!-- 线路介绍区域 -->
  <view class="introduction-section" wx:if="{{routeDetails.description}}">
    <view class="section-title">{{sectionTitle}}</view>
    <view class="introduction-content">
      <text class="introduction-text">{{routeDetails.description}}</text>
    </view>
  </view>

  <!-- 行程详情区域 -->
  <view class="itinerary-section">
    <!-- 遍历天数 -->
    <view wx:for="{{routeDetails.journeyInfos}}" wx:key="dayNo" wx:for-item="dayItem" wx:for-index="dayIndex" class="day-section">
      <!-- 天数标题 -->
      <view class="day-title">{{routeDetails.journeyInfos.length === 1 ? '行程安排' : '第' + dayItem.dayNo + '日'}}</view>

      <!-- 遍历当天的地点 -->
      <view wx:for="{{dayItem.locations}}" wx:key="locationId" wx:for-item="locationItem" wx:for-index="locationIndex" class="spot-item {{locationIndex === dayItem.locations.length - 1 ? 'last-spot' : ''}}">
        <view class="spot-container">
          <!-- 左侧：时间轴 -->
          <view class="timeline-left">
            <!-- 地点图标 -->
            <view class="spot-icon">
              <image class="icon-image" src="{{staticBaseUrl}}/tripHelper/chooseRoute/location.png" mode="aspectFit" />
            </view>
            <!-- 连接线 -->
            <view class="timeline-line" wx:if="{{locationIndex < dayItem.locations.length - 1}}"></view>
          </view>

          <!-- 右侧：内容区域 -->
          <view class="spot-content">
            <!-- 地点名称和类型 -->
            <view class="spot-header">
              <text class="spot-name">{{locationItem.location.name}}</text>
              <text class="spot-type" wx:if="{{locationItem.location.categoryName || locationItem.location.typeName}}">{{locationItem.location.categoryName || locationItem.location.typeName}}</text>
            </view>

            <!-- 地点描述 -->
            <view class="spot-description" wx:if="{{locationItem.location.description}}">
              <text class="description-text">{{locationItem.location.description}}</text>
            </view>

            <!-- 地点图片 -->
            <view class="spot-images" wx:if="{{locationItem.location.picUrls && locationItem.location.picUrls.length > 0}}">
              <view wx:for="{{locationItem.location.picUrls}}" wx:key="*this" wx:for-item="image" wx:for-index="imageIndex" class="image-item" wx:if="{{imageIndex < 3}}" data-images="{{locationItem.location.picUrls}}" data-index="{{imageIndex}}" bind:tap="onImagePreview">
                <image class="spot-image" src="{{image}}" mode="aspectFill" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 多天行程结束提示 -->
    <view class="end-tip" wx:if="{{routeDetails.journeyInfos && routeDetails.journeyInfos.length > 1}}">一 到底了 一</view>
  </view>
