// pages/hotel/hotelList/components/RecSort/RecSort.ts

interface SortOption {
  key: string;
  desc: string;
}

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 排序选项列表
    sortList: {
      type: Array,
      value: []
    },
    // 当前选中的排序key
    currentSort: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imgBaseUrl: getApp().globalData.imgBaseUrl
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 处理排序选项点击
    onSortItemClick(e: any) {
      const { key, desc } = e.currentTarget.dataset;
      console.log('选择排序:', { key, desc });
      
      // 触发排序选择事件
      this.triggerEvent('sortSelect', {
        sortOption: { key, desc }
      });
    },

    // 判断是否为当前选中项
    isSelected(sortKey: string): boolean {
      return this.properties.currentSort === sortKey;
    }
  }
})