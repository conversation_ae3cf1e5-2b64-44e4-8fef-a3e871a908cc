// 登录相关类型定义

declare namespace Login {
  /**
   * 微信小程序登录请求参数
   */
  interface WxMiniLoginRequest {
    /** 微信登录code */
    wxCode: string;
    /** 手机号授权code（可选，登录态失效时可能不需要） */
    phoneCode?: string;
    /** 登录类型 */
    loginType: 'vx_mini';
  }

  /**
   * 登录响应数据
   * 根据实际API返回结构：{code, message, data: {nickName, token}}
   */
  interface LoginResponse {
    /** 用户token */
    token?: string;
    /** 用户ID */
    userId?: string;
    /** 用户昵称 - 注意API返回的是nickName而不是nickname */
    nickName?: string;
    /** 用户头像 */
    avatar?: string;
    /** 手机号 */
    phone?: string;
    /** 是否需要手机号授权 */
    needPhoneAuth: boolean;
    /** 是否需要完善用户资料 */
    needProfileComplete: boolean;
  }

  /**
   * 用户信息
   */
  interface UserInfo {
    /** 用户ID */
    userId: string;
    /** 用户昵称 */
    nickname: string;
    /** 用户头像 */
    avatar: string;
    /** 手机号 */
    phone: string;
    /** 性别 */
    gender?: number;
  }

  /**
   * 更新用户信息请求参数
   */
  interface UpdateUserInfoRequest {
    /** 用户昵称 */
    nickname: string;
    /** 用户头像 */
    avatar: string;
    /** 性别：male/female */
    gender: 'male' | 'female';
  }

  /**
   * 更新用户信息响应
   */
  interface UpdateUserInfoResponse {
    /** 是否成功 */
    success: boolean;
    /** 消息 */
    message?: string;
  }
}
