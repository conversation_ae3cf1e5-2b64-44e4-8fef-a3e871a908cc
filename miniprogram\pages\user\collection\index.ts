// pages/user/collection/index.ts
import { getFavorites, sendFavorites } from "../../../api/my";
import { getImageUrl } from "../../../utils/images";
import { ComponentWithComputedStore } from "../../../core/componentWithStoreComputed";
import { storeBindingsBehavior } from "mobx-miniprogram-bindings";
import { behavior as computedBehavior } from "miniprogram-computed";
import { store } from "../../../store/index";

ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior, computedBehavior],

  storeBindings: {
    store,
    fields: ['userInfo'],
    actions: []
  } as const,

  data: {
    // Tab相关
    currentTab: 'favorite' as Collection.TabType,
    tabs: [
      { label: '收藏', value: 'favorite', behaviorType: 1 },
      { label: '看过', value: 'viewed', behaviorType: 2 },
      { label: '住过', value: 'stayed', behaviorType: 3 },
      { label: '赞过', value: 'liked', behaviorType: 4 }
    ],
    // 图标资源
    deleteIcon: getImageUrl('user/delete_red.png'),
    circleIcon: getImageUrl('user/circle_black.png'),
    circleActIcon: getImageUrl('user/circle_act.png'),
    // 编辑模式
    isEditMode: false,
    selectedItems: [] as string[], // 选中的项目ID列表
    isAllSelected: false,

    // 数据列表
    favoriteList: [] as Collection.List,
    viewedList: [] as Collection.List,
    stayedList: [] as Collection.List,
    likedList: [] as Collection.List,

    // 分页参数
    pageParams: {
      favorite: { page: 1, hasMore: true },
      viewed: { page: 1, hasMore: true },
      stayed: { page: 1, hasMore: true },
      liked: { page: 1, hasMore: true }
    },
    pageSize: 10,

    // 加载状态
    loading: false,
    loadingMore: false, // 上拉加载状态
    refreshing: false,  // 下拉刷新状态

    // 右滑相关
    currentSwipeIndex: -1, // 当前右滑的项目索引

    // 计算属性
    currentList: [] as Collection.List, // 当前Tab的数据列表
    currentListLength: 0,     // 当前列表长度
    emptyText: '',           // 空状态文本
    emptyDesc: '',           // 空状态描述

    // 上拉加载防抖
    lastScrollToLowerTime: 0, // 上次触发上拉加载的时间
  },

  computed: {
    // 当前Tab的数据列表
    currentList(data: Record<string, unknown>) {
      const typedData = data as unknown as {
        currentTab: Collection.TabType;
        favoriteList: Collection.List;
        viewedList: Collection.List;
        stayedList: Collection.List;
        likedList: Collection.List;
      };
      const { currentTab, favoriteList, viewedList, stayedList, likedList } = typedData;
      switch (currentTab) {
        case 'favorite': return favoriteList;
        case 'viewed': return viewedList;
        case 'stayed': return stayedList;
        case 'liked': return likedList;
        default: return [];
      }
    },
    // 当前列表长度
    currentListLength(data: Record<string, unknown>) {
      const typedData = data as unknown as {
        currentTab: Collection.TabType;
        favoriteList: Collection.List;
        viewedList: Collection.List;
        stayedList: Collection.List;
        likedList: Collection.List;
      };
      const { currentTab, favoriteList, viewedList, stayedList, likedList } = typedData;
      let currentList: Collection.List = [];
      switch (currentTab) {
        case 'favorite': currentList = favoriteList; break;
        case 'viewed': currentList = viewedList; break;
        case 'stayed': currentList = stayedList; break;
        case 'liked': currentList = likedList; break;
        default: currentList = [];
      }
      return currentList.length;
    },
    // 空状态文本
    emptyText(data: Record<string, unknown>) {
      const typedData = data as unknown as { currentTab: Collection.TabType };
      const emptyTextMap = {
        favorite: '收藏',
        viewed: '看过',
        stayed: '住过',
        liked: '赞过'
      };
      return `暂无${emptyTextMap[typedData.currentTab as keyof typeof emptyTextMap] || '数据'}`;
    },
    // 空状态描述
    emptyDesc(data: Record<string, unknown>) {
      const typedData = data as unknown as { currentTab: Collection.TabType };
      const emptyDescMap = {
        favorite: '收藏',
        viewed: '浏览',
        stayed: '入住',
        liked: '点赞'
      };
      return `快去${emptyDescMap[typedData.currentTab as keyof typeof emptyDescMap] || '操作'}你喜欢的酒店吧`;
    }
  },

  lifetimes: {
    attached() {
      // 初始化加载收藏数据
      this.loadData('favorite');
    }
  },

  methods: {
    /**
     * 组件下拉刷新事件
     */
    onRefresh() {
    // 编辑模式下不允许下拉刷新
    if (this.data.isEditMode) {
      console.log('编辑模式下禁用下拉刷新');
      return;
    }

    const currentTab = this.data.currentTab;
    console.log('组件下拉刷新触发:', currentTab);

    // 显示刷新状态
    this.setData({ refreshing: true });

    // 重置分页参数
    this.setData({
      [`pageParams.${currentTab}.page`]: 1,
      [`pageParams.${currentTab}.hasMore`]: true
    });

    // 重新加载数据
    this.loadData(currentTab, false).then(() => {
      // 延迟一点时间，让用户看到刷新提示
      setTimeout(() => {
        this.setData({ refreshing: false });
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        });
        console.log('组件下拉刷新完成');
      }, 500);
    }).catch((error) => {
      console.error('组件下拉刷新失败:', error);
      this.setData({ refreshing: false });
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 1500
      });
    });
  },

  /**
   * 组件上拉加载事件
   */
  onLoadMore() {
    // 编辑模式下不允许上拉加载
    if (this.data.isEditMode) {
      console.log('编辑模式下禁用上拉加载');
      return;
    }

    const currentTab = this.data.currentTab;
    console.log('组件上拉加载触发:', currentTab);

    // 执行上拉加载
    this.loadData(currentTab, true);
  },

  /**
   * 页面下拉刷新（兼容原生）
   */
  onPullDownRefresh() {
    // 直接调用组件的刷新方法
    this.onRefresh();
  },

  /**
   * 页面上拉触底（兼容原生）
   */
  onReachBottom() {
    console.log('页面onReachBottom触发');

    // 编辑模式下不允许上拉加载
    if (this.data.isEditMode) {
      console.log('编辑模式下禁用上拉加载');
      return;
    }

    const currentTab = this.data.currentTab;
    const pageParams = this.data.pageParams[currentTab];

    // 检查是否可以加载更多
    if (this.data.loadingMore || !pageParams.hasMore || this.data.loading) {
      console.log('上拉加载被阻止:', {
        loadingMore: this.data.loadingMore,
        hasMore: pageParams.hasMore,
        loading: this.data.loading
      });
      return;
    }

    // 直接调用loadData，不需要更新页码（loadData内部会处理）
    this.loadData(currentTab, true);
  },

  /**
   * 获取当前Tab的behaviorType
   */
  getCurrentBehaviorType(tabValue: string): number {
    const tab = this.data.tabs.find(t => t.value === tabValue);
    return tab ? tab.behaviorType : 1;
  },

  /**
   * 获取操作文本
   */
  getActionText(tabValue: string): string {
    switch (tabValue) {
      case 'favorite': return '取消收藏';
      case 'viewed': return '删除看过';
      case 'stayed': return '删除住过';
      case 'liked': return '取消赞过';
      default: return '删除';
    }
  },

  /**
   * 更新当前Tab的数据列表
   */
  updateCurrentList(newList: Collection.List) {
    const { currentTab } = this.data;
    switch (currentTab) {
      case 'favorite':
        this.setData({ favoriteList: newList });
        break;
      case 'viewed':
        this.setData({ viewedList: newList });
        break;
      case 'stayed':
        this.setData({ stayedList: newList });
        break;
      case 'liked':
        this.setData({ likedList: newList });
        break;
    }
  },

  /**
   * 加载数据（通用方法）
   */
  async loadData(tabValue: Collection.TabType, isLoadMore: boolean = false) {
    const loadingKey = isLoadMore ? 'loadingMore' : 'loading';
    this.setData({ [loadingKey]: true });

    try {
      const behaviorType = this.getCurrentBehaviorType(tabValue);
      const currentPage = isLoadMore ?
        this.data.pageParams[tabValue as keyof typeof this.data.pageParams].page : 1;

      const params: My.IFavoritesReq = {
        behaviorType: behaviorType.toString(),
        page: currentPage,
        pageSize: this.data.pageSize
      };

      const { isSuccess, data } = await getFavorites(params);

      if (isSuccess && data) {
        // 处理数据
        const newList = Array.isArray(data) ? data : [];
        const hasMore = newList.length === this.data.pageSize;

        if (isLoadMore) {
          // 上拉加载：追加数据
          const currentList = this.getCurrentList(tabValue);
          const updatedList = [...currentList, ...newList];
          this.updateListByTab(tabValue, updatedList);
        } else {
          // 首次加载或刷新：替换数据
          this.updateListByTab(tabValue, newList);
        }

        // 更新分页参数
        this.setData({
          [`pageParams.${tabValue}.page`]: isLoadMore ? currentPage + 1 : 2, // 首次加载后page=2，上拉加载后page+1
          [`pageParams.${tabValue}.hasMore`]: hasMore
        });

        console.log('分页参数更新:', {
          tabValue,
          isLoadMore,
          currentPage,
          newPage: isLoadMore ? currentPage + 1 : 2,
          hasMore,
          dataLength: newList.length
        });
      } else {
        // 如果接口失败且是首次加载，使用模拟数据
        if (!isLoadMore && tabValue === 'favorite') {
          const mockData: Collection.List = [
            {
              id: '1',
              title: '全季古北财富中心酒店',
              hotelName: '全季古北财富中心酒店',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel1.jpg',
              createTime: '2024-01-01'
            },
            {
              id: '2',
              title: '全季上海虹桥古北财富中心',
              hotelName: '全季上海虹桥古北财富中心',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel2.jpg',
              createTime: '2024-01-02'
            },
            {
              id: '3',
              title: '全季酒店3',
              hotelName: '全季酒店3',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel3.jpg',
              createTime: '2024-01-03'
            },
            {
              id: '11',
              title: '全季古北财富中心酒店',
              hotelName: '全季古北财富中心酒店',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel1.jpg',
              createTime: '2024-01-11'
            },
            {
              id: '12',
              title: '全季上海虹桥古北财富中心',
              hotelName: '全季上海虹桥古北财富中心',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel2.jpg',
              createTime: '2024-01-12'
            },
            {
              id: '13',
              title: '全季酒店3',
              hotelName: '全季酒店3',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel3.jpg',
              createTime: '2024-01-13'
            },
            {
              id: '111',
              title: '全季古北财富中心酒店',
              hotelName: '全季古北财富中心酒店',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel1.jpg',
              createTime: '2024-01-21'
            },
            {
              id: '112',
              title: '全季上海虹桥古北财富中心',
              hotelName: '全季上海虹桥古北财富中心',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel2.jpg',
              createTime: '2024-01-22'
            },
            {
              id: '113',
              title: '全季酒店3',
              hotelName: '全季酒店3',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel3.jpg',
              createTime: '2024-01-23'
            }
          ];
          this.updateListByTab(tabValue, mockData);

          // 模拟数据设置hasMore为true，允许上拉加载
          this.setData({
            [`pageParams.${tabValue}.hasMore`]: true
          });
        }
      }
  const mockData: Collection.List = [
            {
              id: '1',
              title: '全季古北财富中心酒店',
              hotelName: '全季古北财富中心酒店',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel1.jpg',
              createTime: '2024-01-01'
            },
            {
              id: '2',
              title: '全季上海虹桥古北财富中心',
              hotelName: '全季上海虹桥古北财富中心',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel2.jpg',
              createTime: '2024-01-02'
            },
            {
              id: '3',
              title: '全季酒店3',
              hotelName: '全季酒店3',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel3.jpg',
              createTime: '2024-01-03'
            },
            {
              id: '11',
              title: '全季古北财富中心酒店',
              hotelName: '全季古北财富中心酒店',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel1.jpg',
              createTime: '2024-01-11'
            },
            {
              id: '12',
              title: '全季上海虹桥古北财富中心',
              hotelName: '全季上海虹桥古北财富中心',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel2.jpg',
              createTime: '2024-01-12'
            },
            {
              id: '13',
              title: '全季酒店3',
              hotelName: '全季酒店3',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel3.jpg',
              createTime: '2024-01-13'
            },
            {
              id: '111',
              title: '全季古北财富中心酒店',
              hotelName: '全季古北财富中心酒店',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel1.jpg',
              createTime: '2024-01-21'
            },
            {
              id: '112',
              title: '全季上海虹桥古北财富中心',
              hotelName: '全季上海虹桥古北财富中心',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel2.jpg',
              createTime: '2024-01-22'
            },
            {
              id: '113',
              title: '全季酒店3',
              hotelName: '全季酒店3',
              address: '虹桥地区 靠近伊犁路(地铁站)',
              price: 695,
              rating: 4.8,
              image: 'https://example.com/hotel3.jpg',
              createTime: '2024-01-23'
            }
          ];
          this.updateListByTab(tabValue, mockData);

          // 模拟数据设置hasMore为true，允许上拉加载
          this.setData({
            [`pageParams.${tabValue}.hasMore`]: true
          });
      this.setData({ [loadingKey]: false });
    } catch (error) {
      console.error('加载数据失败:', error);
      this.setData({ [loadingKey]: false });

      // 错误时显示提示
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 根据Tab更新对应的数据列表
   */
  updateListByTab(tabValue: Collection.TabType, newList: Collection.List) {
    switch (tabValue) {
      case 'favorite':
        this.setData({ favoriteList: newList });
        break;
      case 'viewed':
        this.setData({ viewedList: newList });
        break;
      case 'stayed':
        this.setData({ stayedList: newList });
        break;
      case 'liked':
        this.setData({ likedList: newList });
        break;
    }

  },

  /**
   * 获取指定Tab的数据列表
   */
  getCurrentList(tabValue?: Collection.TabType): Collection.List {
    const currentTabValue = tabValue || this.data.currentTab;
    const { favoriteList, viewedList, stayedList, likedList } = this.data;
    switch (currentTabValue) {
      case 'favorite': return favoriteList;
      case 'viewed': return viewedList;
      case 'stayed': return stayedList;
      case 'liked': return likedList;
      default: return [];
    }
  },



  /**
   * Tab点击事件
   */
  onTabClick(e: WechatMiniprogram.BaseEvent) {
    const { value } = e.currentTarget.dataset;

    // 如果点击的是当前Tab，不做处理
    if (value === this.data.currentTab) return;

    this.setData({
      currentTab: value,
      isEditMode: false,
      selectedItems: [],
      currentSwipeIndex: -1
    });

    // 重置当前Tab的分页参数
    this.setData({
      [`pageParams.${value}.page`]: 1,
      [`pageParams.${value}.hasMore`]: true
    });

    // 加载对应Tab的数据
    this.loadData(value);
  },

  /**
   * Tab切换（保留兼容性）
   */
  onTabChange(e: WechatMiniprogram.CustomEvent<{ value: Collection.TabType }>) {
    const { value } = e.detail;
    this.onTabClick({ currentTarget: { dataset: { value } } } as unknown as WechatMiniprogram.BaseEvent);
  },

  /**
   * 切换编辑模式
   */
  onToggleEditMode() {
    const isEditMode = !this.data.isEditMode;
    this.setData({
      isEditMode,
      selectedItems: [],
      isAllSelected: false,
      currentSwipeIndex: -1
    });

    // 更新列表项的选中状态
    this.updateListItemsSelectedState();
  },

  /**
   * 选择/取消选择项目
   */
  onSelectItem(e: WechatMiniprogram.CustomEvent<ComponentEvent.Detail>) {
    if (!this.data.isEditMode) return;

    const itemId = e.detail.itemId;
    if (!itemId) return;

    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(itemId);

    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }

    const currentList = this.getCurrentList();
    const isAllSelected = selectedItems.length === currentList.length;

    this.setData({
      selectedItems,
      isAllSelected
    });

    // 更新列表项的选中状态
    this.updateListItemsSelectedState();
  },

  /**
   * 全选/取消全选
   */
  onToggleSelectAll() {
    const currentList = this.getCurrentList();
    const isAllSelected = !this.data.isAllSelected;

    this.setData({
      isAllSelected,
      selectedItems: isAllSelected ? currentList.map(item => item.id) : []
    });

    // 更新列表项的选中状态
    this.updateListItemsSelectedState();
  },



  /**
   * 处理组件触摸结束事件
   */
  onTouchEnd(e: WechatMiniprogram.CustomEvent<ComponentEvent.Detail>) {
    const { index, deltaX, deltaTime } = e.detail;

    // 判断是否为有效的左滑手势
    if (deltaX && deltaTime && deltaX > 50 && deltaTime < 300) {
      // 左滑显示删除按钮
      this.setData({
        currentSwipeIndex: this.data.currentSwipeIndex === index ? -1 : index
      });
    } else if (deltaX && deltaTime && deltaX < -50 && deltaTime < 300) {
      // 右滑隐藏删除按钮
      this.setData({
        currentSwipeIndex: -1
      });
    } else if (deltaX && deltaTime && Math.abs(deltaX) < 10 && deltaTime < 300) {
      // 点击事件，隐藏删除按钮
      this.setData({
        currentSwipeIndex: -1
      });
    }
  },

  /**
   * 删除单个项目
   */
  async onDeleteItem(e: WechatMiniprogram.CustomEvent<ComponentEvent.Detail>) {
    const itemId = e.detail.itemId;
    if (!itemId) return;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个收藏吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 调用删除接口
            const behaviorType = this.getCurrentBehaviorType(this.data.currentTab);
            const { isSuccess } = await sendFavorites({
              type: '1', // 酒店类型
              behaviorType: behaviorType.toString(),
              itemIds: [itemId]
            });

            if (isSuccess) {
              // 从列表中移除
              const currentList = this.getCurrentList();
              const newList = currentList.filter(item => item.id !== itemId);
              this.updateListByTab(this.data.currentTab, newList);

              this.setData({ currentSwipeIndex: -1 });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('删除失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 批量取消收藏
   */
  async onBatchCancel() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要取消的项目',
        icon: 'none'
      });
      return;
    }

    const actionText = this.getActionText(this.data.currentTab);

    wx.showModal({
      title: `确认${actionText}`,
      content: `确定要${actionText}这${this.data.selectedItems.length}个项目吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const behaviorType = this.getCurrentBehaviorType(this.data.currentTab);
            const { isSuccess } = await sendFavorites({
              type: '1', // 酒店类型
              behaviorType: behaviorType.toString(),
              itemIds: this.data.selectedItems
            });

            if (isSuccess) {
              // 从列表中移除选中项目
              const currentList = this.getCurrentList();
              const newList = currentList.filter(item => !this.data.selectedItems.includes(item.id));
              this.updateListByTab(this.data.currentTab, newList);

              this.setData({
                selectedItems: [],
                isAllSelected: false,
                isEditMode: false
              });

              wx.showToast({
                title: `${actionText}成功`,
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('批量操作失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },



  /**
   * 点击项目（非编辑模式下跳转详情）
   */
  onItemClick(e: WechatMiniprogram.CustomEvent<ComponentEvent.Detail>) {
    const { itemId, hotelData, originalData } = e.detail;

    if (hotelData && originalData) {
      // 如果是酒店数据，跳转到酒店详情页
      console.log('跳转到酒店详情页:', {
        hotelId: hotelData.hotelId,
        originalData: originalData
      });
      // TODO: 实现酒店详情页跳转
      // wx.navigateTo({
      //   url: `/pages/hotel/detail/index?hotelId=${hotelData.hotelId}`
      // });
    } else if (itemId) {
      // 兼容旧的点击方式
      console.log('跳转到详情页:', itemId);
    }
  },

  /**
   * 更新列表项的选中状态
   */
  updateListItemsSelectedState() {
    const { selectedItems, currentTab } = this.data;
    const currentList = this.getCurrentList();

    // 为每个列表项添加 selected 属性
    const updatedList = currentList.map(item => ({
      ...item,
      selected: selectedItems.includes(item.id)
    }));

    // 更新当前Tab的列表数据
    this.updateListByTab(currentTab, updatedList);
    }
  }
});
