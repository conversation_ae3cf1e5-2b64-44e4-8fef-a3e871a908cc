<!--pages/tripHelper/order/index.wxml-->
<view class="order-container">
  <!-- 空状态和错误状态 -->
  <status-display
    wx:if="{{showEmptyState}}"
    customStyle="margin-top: 300rpx"
    type="{{emptyStateType}}"
    bind:buttonclick="onStatusDisplayButtonClick"
  />

  <!-- 订单列表 -->
  <refresh-load-list
    wx:else
    enable-refresh="{{false}}"
    enable-load-more="{{orderList.length > 0}}"
    loading="{{loading}}"
    loading-more="{{loadingMore}}"
    has-more="{{hasMore}}"
    is-empty="{{false}}"
    bind:loadmore="onLoadMore"
  >
    <!-- 订单列表 -->
    <view class="order-list">
      <view wx:for="{{orderList}}" wx:key="activityId" class="order-item">
        <!-- 上部分：图片、标题、时间、状态 -->
        <view class="order-header">
          <!-- 左侧：图片和信息 -->
          <view class="order-info">
            <image class="order-image" src="{{item.mainImagUrl}}" mode="aspectFill"></image>
            <view class="order-details">
              <text class="order-title">{{item.activityName}}</text>
              <text class="registration-time">报名时间：{{item.createTime}}</text>
            </view>
          </view>

          <!-- 右侧：状态 -->
          <view class="order-status status-{{item.orderStatus}}">
            {{item.orderStatusDesc}}
          </view>
        </view>

        <!-- 中部分：金额信息 -->
        <view class="order-amount-section">
          <!-- 订单金额 -->
          <view class="amount-row">
            <text class="amount-label">订单金额</text>
            <view class="amount-info">
              <text class="amount-value">¥ {{item.totalAmount}}</text>
              <view class="amount-details">
                <!-- 成人票 -->
                <text wx:if="{{item.adultCount > 0}}" class="detail-item">
                  ¥ {{item.adultPrice}}×{{item.adultCount}}
                </text>
                <!-- 儿童票 -->
                <text wx:if="{{item.childrenCount > 0}}" class="detail-item">
                  ¥ {{item.childrenPrice}}×{{item.childrenCount}}
                </text>
                <!-- 老年票 -->
                <text wx:if="{{item.oldPersonCount > 0}}" class="detail-item">
                  ¥ {{item.oldPersonPrice}}×{{item.oldPersonCount}}
                </text>
              </view>
            </view>
          </view>

          <!-- 退款金额（仅已退款状态显示） -->
          <view wx:if="{{item.orderStatus === 3}}" class="amount-row">
            <text class="amount-label">退款金额</text>
            <view class="amount-info">
              <text class="amount-value">¥ {{item.refundAmount}}</text>
              <text wx:if="{{item.refundRate}}" class="refund-ratio">退款比例：{{item.refundRate}}%</text>
            </view>
          </view>
        </view>

        <!-- 分割线和操作按钮 - 只在有按钮时显示 -->
        <block wx:if="{{item.orderStatus === 0 || item.orderStatus === 2 || item.orderStatus === 1}}">
          <!-- 分割线 -->
          <view class="divider"></view>

          <!-- 下部分：操作按钮 -->
          <view class="order-actions">
            <!-- 去支付按钮 - 仅待支付状态显示 -->
            <button
              wx:if="{{item.orderStatus === 0}}"
              class="pay-btn"
              bindtap="onGoPay"
              data-activity-id="{{item.activityId}}"
              data-order-no="{{item.orderNo}}"
            >
              去支付
            </button>

            <!-- 申请退款按钮 - 候补中和已支付状态显示 -->
            <button
              wx:if="{{item.orderStatus === 2 || item.orderStatus === 1}}"
              class="refund-btn"
              bindtap="onApplyRefund"
              data-activity-id="{{item.activityId}}"
              data-order-no="{{item.orderNo}}"
            >
              申请退款
            </button>
          </view>
        </block>
      </view>
    </view>
  </refresh-load-list>
</view>
