<view class="project-pick" wx:if="{{!loading}}">
	<view class="project-pick-title">选择项目</view>
	<view class="project-pick-content">
		<view class="project-item {{activeProject && activeProject.id === item.id ? 'is-active' : null}}" wx:for="{{projectList}}" wx:key="code" bind:tap="handleProjectSelect" data-item="{{item}}">
			<view class="project-item-logo">
				<image src="{{activeProject && activeProject.code === item.code ? item.activeUrl : item.inactiveUrl}}" mode="widthFix"/>
			</view>
			<view>{{item.name}}</view>
		</view>
	</view>
	<view class="operate-wrap">
		<view class="trigger-wrap">
			<button class="button-item {{activeProject ? 'is-active' : 'is-inactive'}}" bind:tap="handleSubmit">{{activeProject ? '确定' : '请选择您要创建的项目'}}</button>
		</view>
	</view>
</view>

<!-- 骨架屏 -->
<view class="skeleton-wrap" wx:if="{{loading}}">
	<view class="skeleton-title"></view>
	<view class="skeleton-content">
		<view class="skeleton-item">
			<view class="skeleton-icon"></view>
			<view class="skeleton-label"></view>
		</view>
		<view class="skeleton-item">
			<view class="skeleton-icon"></view>
			<view class="skeleton-label"></view>
		</view>
		<view class="skeleton-item">
			<view class="skeleton-icon"></view>
			<view class="skeleton-label"></view>
		</view>
		<view class="skeleton-item">
			<view class="skeleton-icon"></view>
			<view class="skeleton-label"></view>
		</view>
		<view class="skeleton-item">
			<view class="skeleton-icon"></view>
			<view class="skeleton-label"></view>
		</view>
	</view>
</view>
