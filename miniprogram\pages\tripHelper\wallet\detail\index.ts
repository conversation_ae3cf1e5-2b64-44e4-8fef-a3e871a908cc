import { getImageUrl } from '../../../../utils/images'
import { GenderEnum } from '../../../../enum/my'
import { getActivityParticipantOrder } from '../../../../api/wallet';

Page({
  data: {
    // 活动ID
    activityId: 0,

    // 活动参与人订单信息（直接存储接口返回的数据）
    participantOrderData: null as Wallet.IActivityParticipantOrderData | null,

    // 加载状态
    loading: true,

    // 性别图标
    genderMaleIcon: getImageUrl('tripHelper/my/men.png'),
    genderFemaleIcon: getImageUrl('tripHelper/my/women.png'),

    // 工具函数
    getImageUrl: getImageUrl,

    // 默认头像
    defaultAvatar: getImageUrl('user/avatar_default.png')
  },

  onLoad(options: { activityId?: string }) {
    console.log('钱包详情页面加载，参数：', options);
    const { activityId } = options;
    if (activityId) {
      this.setData({ activityId: parseInt(activityId) });
      this.loadParticipantOrderData();
    } else {
      wx.displayToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载活动参与人订单信息
   */
  async loadParticipantOrderData() {
    try {
      this.setData({ loading: true });

      const response = await getActivityParticipantOrder({
        activityId: this.data.activityId
      });

      if (response.code === 0 && response.data) {
        this.setData({
          participantOrderData: response.data
        });
      } else {
        console.error('获取活动参与人订单信息失败:', response.message);
        wx.displayToast({
          title: response.message || '获取数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载活动参与人订单信息异常:', error);
      wx.displayToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },



  /**
   * 获取性别图标
   */
  getGenderIcon(gender: GenderEnum): string {
    if (gender === GenderEnum.Male) {
      return this.data.genderMaleIcon
    } else if (gender === GenderEnum.Female) {
      return this.data.genderFemaleIcon
    }
    return ''
  },

  /**
   * 获取性别图标背景色
   */
  getGenderIconBgColor(gender: GenderEnum): string {
    if (gender === GenderEnum.Male) {
      return '#0198FF' // 男生蓝色
    } else if (gender === GenderEnum.Female) {
      return '#FF4EAA' // 女生粉色
    }
    return '#0198FF' // 默认蓝色
  },

  /**
   * 是否显示性别图标
   */
  shouldShowGenderIcon(gender: GenderEnum): boolean {
    return gender === GenderEnum.Male || gender === GenderEnum.Female
  }
})
