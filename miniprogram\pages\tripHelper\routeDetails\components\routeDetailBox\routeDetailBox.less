/* components/routeDetailBox/routeDetailBox.less */



/* 线路介绍区域 */
.introduction-section {
  border-radius: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.introduction-content {
  width: 100%;
}

.introduction-text {
  font-size: 28rpx;
  color: #000000;
  display: block;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 行程详情区域 */
.itinerary-section {
  margin-top: 32rpx;
  padding-bottom: 180rpx; /* 为底部按钮留出空间 */
}

.day-section {
  margin-bottom: 48rpx;
}

.day-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

.spot-item {
  position: relative;
  margin-bottom: 32rpx;
}

.spot-item.last-spot {
  margin-bottom: 0;
}

.spot-container {
  display: flex;
  align-items: stretch; /* 改为stretch让子元素等高 */
}

/* 左侧时间轴 */
.timeline-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  position: relative;
  width: 64rpx; /* 明确设置宽度 */
}

.spot-icon {
  width: 40rpx;
  height: 40rpx;
  background: #0198FF;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

.icon-image {
  width: 22rpx;
  height: 22rpx;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 48rpx; /* 从图标下方开始 */
  transform: translateX(-50%);
  width: 2rpx;
  background: #E5E5E5;
  height: 92%; /* 撑满剩余高度并延伸到下一个地点 */
}

/* 右侧内容区域 */
.spot-content {
  flex: 1;
  padding-bottom: 24rpx;
}

.spot-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.spot-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #33333e;
  margin-right: 16rpx;
}

.spot-type {
  font-size: 20rpx;
  color: #0198FF;
  background: #EEF8FF;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.spot-description {
  margin-bottom: 16rpx;
}

.description-text {
  font-size: 24rpx;
  color: #66666E;
  line-height: 1.6;
}

/* 地点图片网格 */
.spot-images {
  display: flex;
  gap: 12rpx;
}

.image-item {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.spot-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.image-item:active .spot-image {
  transform: scale(0.95);
}

/* 结束提示 */
.end-tip {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
} 