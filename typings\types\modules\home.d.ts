declare namespace Home {
	interface IBusinessItem {
		/** 选中业务logo */
		activeIcon: string;
		/** 反选业务logo */
		inactiveIcon: string;
		/** 业务名称 */
		name: string;
		/** 业务标识 */
		type: "flight" | "train" | "hotel" | "ticket"
	}
	/** 推荐酒店模型 */
	interface IRecommendHotel extends ILocation, ICity  {
		/** 酒店id */
		outId: string;
		/** 酒店名称 */
		hotelName: string;
		/** 类型 */
		category: number;
		/** 类型描述 */
		categoryDesc: string;
		/** 星级 */
		starRate: number;
		/** 酒店地址 */
		address: string;
		/** 酒店主图 */
		mainImage: string;
		/** 距离 */
		distance: string;
		/** 评分 */
		serviceScore: string;
		/** 评分描述 */
		serviceScoreDesc: string;
		/** 酒店描述 */
		hotelDesc: string;
		/** 最低价格 */
		minPrice: number;
		/** 币种 */
		currency: string;
	}

	type GetRecommendHotelParams = ILocation & API.Common.CommonSearchParams & Pick<ICity, 'cityName'> & {
		/** 国内/国际标识 1:国内 2:国际 */
		domesticIntl: 1 | 2;
		/** 详见HotelBusinessTypeEnum */
		businessType: number;
	}

	// 首页"我来支一招"反馈相关接口类型定义

	// 上传图片接口返回值
	interface IUploadImageRes {
		// 图片CDN访问地址
		url: string
	}

	// 上传图片接口入参
	interface IUploadImageReq {
		// 图片文件路径
		files: string
		// 业务类型：1-反馈图片，2-用户头像，3-酒店图片，4-房间图片，5-评论图片，6-身份证件图片，99-其他图片
		type: number
	}

	// 提交反馈接口入参
	interface ISubmitFeedbackReq {
		// 反馈内容（文本）
		detailedOpinion: string
		// 图片URL数组
		imageUrls?: string[]
	}

	// 提交反馈接口返回值
	interface ISubmitFeedbackRes {
		// 反馈唯一标识
		feedbackId: string
		// 反馈状态（pending=待处理，resolved=已解决）
		status: string
	}
}