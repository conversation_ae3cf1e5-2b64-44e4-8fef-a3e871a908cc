<!--pages/tripHelper/message/index.wxml-->
<view class="message-page">
  <!-- 自定义透明导航栏 -->
  <navigationBar
    theme="transparent"
    title="消息中心"
    color="#000000"
    back-icon-color="#000000"
    title-size="32rpx"
    bind:back="onNavigationBack"
  />

  <!-- 使用refreshLoadList组件实现上拉加载 -->
  <refreshLoadList
    enable-refresh="{{false}}"
    enable-load-more="{{messageList.length > 0}}"
    disabled="{{false}}"
    refreshing="{{refreshing}}"
    loading-more="{{loadingMore}}"
    has-more="{{hasMore}}"
    is-empty="{{!loading && messageList.length === 0}}"
    loading="{{loading}}"
    hasCustomEmpty="{{false}}"
    hasCustomLoading="{{true}}"
    statusDisplayType="data-empty"
    statusDisplayTitle="暂无消息"
    statusDisplayStyle="margin-top: 100rpx;"
    bind:refresh="onRefresh"
    bind:loadmore="onLoadMore"
  >
    <!-- 自定义加载状态插槽 - 骨架屏 -->
    <view slot="loading">
      <view class="skeleton-container">
        <view wx:for="{{[1, 2, 3]}}" wx:key="*this" class="skeleton-item">
          <!-- 消息头部骨架 -->
          <view class="skeleton-header">
            <view class="skeleton-image"></view>
            <view class="skeleton-info">
              <view class="skeleton-title"></view>
              <view class="skeleton-date"></view>
            </view>
            <view class="skeleton-arrow"></view>
          </view>
          <!-- 消息内容骨架 -->
          <view class="skeleton-content">
            <view class="skeleton-status-row">
              <view class="skeleton-status"></view>
              <view class="skeleton-time"></view>
            </view>
            <view class="skeleton-description"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="message-list">
      <view
        class="message-item"
        wx:for="{{messageList}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onMessageClick"
      >
        <!-- 消息头部 -->
        <view class="message-header">
          <view class="message-info">
            <image class="message-image" src="{{item.image}}" mode="aspectFill"></image>
            <view class="message-title-section">
              <text class="message-title">{{item.title}}</text>
              <view class="message-date">
                <image class="date-icon" src="{{timeIcon}}" mode="aspectFit"></image>
                <text class="date-text">{{item.date}} {{item.weekday}}</text>
              </view>
            </view>
          </view>
          <image class="arrow-icon" src="{{arrowIcon}}" mode="aspectFit"></image>
        </view>

        <!-- 消息内容 -->
        <view class="message-content">
          <view class="message-status-row">
            <text class="message-status">{{item.status}}</text>
            <text class="message-time">{{item.time}}</text>
          </view>
          <text class="message-description">{{item.description}}</text>
        </view>
      </view>
    </view>
  </refreshLoadList>
</view>
