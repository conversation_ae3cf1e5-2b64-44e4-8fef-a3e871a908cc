// components/routeDetailBox/routeDetailBox.ts
import appConfig from "../../../../../config/app.config";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    sectionTitle: {
      type: String,
      value: ''
    },
    // 路线详情数据
    routeDetails: {
      type: Object,
      value: {} as RouteLine.RouteDetailResponse
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    staticBaseUrl: appConfig.staticBaseUrl,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 图片预览点击事件
     */
    onImagePreview(e: any) {
      const { images, index } = e.currentTarget.dataset;
      wx.previewImage({
        current: images[index],
        urls: images
      });
    }
  }
}) 