.bottom-modal-content{
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  .pop-title{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32rpx 32rpx 32rpx 76rpx;

    text{
      width: 598rpx;
      text-align: center;
      font-weight: 500;
      font-size: 36rpx;
      line-height: 36rpx;
      color: #11111E;
    }
    image{
      width: 36rpx;
      height: 36rpx;
      margin-left: 8rpx;
    }
  }
  .pop-content{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 32rpx;
    .item{
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin: 32rpx 0;
      width: 100%;
      text{
        font-size: 28rpx;
        color: #11111E;
      }
      .txt{
        flex: 1;
        margin-right: 50rpx;
      }
    }
    .ment-title{
      margin: 32rpx 0;
      font-weight: 400;
      font-size: 28rpx;
      color: #33333E;
    }
  }
}