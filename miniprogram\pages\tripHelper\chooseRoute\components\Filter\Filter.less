/* pages/tripHelper/chooseRoute/components/Filter/Filter.less */
@import "../../../../../styles/mixins.less";

.filter-container {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 0 16rpx;
  height: 104rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  flex: 1;
}

.filter-item.active {
  color: @primary-color;
  
  .filter-text {
    color: @primary-color;
    font-weight: 500;
  }
  
  .filter-arrow {
    color: @primary-color;
  }
}

.filter-text {
  font-size: 28rpx;
  color: #33333e;
}

.city-label, .days-label {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filter-content {
  display: flex;
  align-items: center;
  margin-left: 8rpx;
}

.filter-arrow {
  color: #999;
  transition: all 0.3s ease;
  
  &.rotated {
    transform: rotate(180deg);
  }
}