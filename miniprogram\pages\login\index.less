/* pages/login/index.wxss */
page {
  background-color: #fff;
  height: 100vh;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 0 32rpx;
  
  .avatar-section {
    margin-top: 166rpx;
    margin-bottom: 208rpx;
    
    .avatar-image {
      width: 144rpx;
      height: 144rpx;
      border-radius: 50%;
    }
  }
  
  .agreement-section {
    width: 100%;
    margin-bottom: 48rpx;

    .agreement-checkbox {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 16rpx;

      .agreement-text {
        font-size: 28rpx;
        color: #99999E;
        text-align: center;

        .agreement-link {
          color: #0198FF;
        }
      }
    }
  }
  
  .login-section {
    width: 100%;
    
    .login-button {
      width: 100%;
      height: 108rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 120rpx;
      font-size: 32rpx;
      font-weight: 600;
      border: none;
      
      &.active {
        background: #0198FF;
        color: #ffffff;
      }
      
      &.disabled {
        background-color: #f0f0f0;
        color: #cccccc;
        box-shadow: none;
      }
      
      &::after {
        border: none;
      }
    }
  }
}

// 隐藏的获取手机号按钮
.hidden-button {
  position: absolute;
  left: -9999rpx;
  opacity: 0;
  pointer-events: none;
}
