.popup-body {
	padding: 32rpx 0 32rpx;
	box-sizing: border-box;
	.popup-title {
		display: flex;
		padding: 0 32rpx;
		justify-content: space-between;
		color: @text-title-color;
		font-weight: bold;
		font-size: 36rpx;
		line-height: 50rpx;
		margin-bottom: 46rpx;
		.close-trigger {
			width: 36rpx;
			height: 36rpx;
			background-image: url('@{static-base-url}/close.png');
			background-size: 100%;
		}
	}
	.popup-content {
		display: flex;
		flex-direction: column;
		.tips-wrap {
			background-color: #FFF6F0;
			padding: 24rpx 32rpx;
			.tips-title {
				line-height: 40rpx;
				color: @text-title-color;
				font-weight: bold;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				&::before {
					content: "";
					width: 32rpx;
					height: 32rpx;
					background-image: url('@{static-base-url}/tripHelper/activity/warning.png');
					background-size: 100%;
					margin-right: 16rpx;
				}
			}
			.tips-desc {
				margin-top: 8rpx;
				text-indent: 46rpx;
				line-height: 34rpx;
				font-size: 24rpx;
			}
		}
		.policy-list {
			height: calc(75vh - 440rpx);
			padding: 24rpx 32rpx 0;
			box-sizing: border-box;
			.policy-item {
				display: flex;
				align-items: center;
				align-items: center;
				padding: 20rpx 28rpx;
				color: @text-color;
				font-size: 24rpx;
				line-height: 42rpx;
				background-color: @bg-light-gray-color;
				border-radius: 32rpx;
				margin-bottom: 16rpx;
				.policy-content {
					margin-left: 20rpx;
					.policy-title {
						font-size: 28rpx;
						font-weight: bold;
						line-height: 48rpx;
					}
				}
			}
		}
	}
}

.operate-wrap {
	padding: 16rpx 32rpx 68rpx;
	display: flex;
	.border(1px, solid, @border-color, 0, top);
	button {
		width: 100%;
		height: 108rpx;
		line-height: 108rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 54rpx;
		background-color: @primary-color;
		color: #fff;
	}
}