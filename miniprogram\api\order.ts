import { request } from "../utils/request";

/**
 * 获取订单列表
 */
export const getOrderList = (data: Order.IOrderListReq) => request<Order.IOrderListRes>({
  url: "/hotel/order/orderList",
  method: "POST",
  data
});

/**
 * 获取订单详情
 */
export const getOrderDetail = (orderId: string) => request<Order.OrderItem>({
  url: `/order/detail/v1?orderId=${orderId}`,
  method: "GET"
});

/**
 * 取消订单
 */
export const cancelOrder = (orderId: string, reason?: string) => request<{ success: boolean }>({
  url: "/order/cancel/v1",
  method: "POST",
  data: {
    orderId,
    reason
  }
});

/**
 * 删除订单
 */
export const deleteOrder = (orderNo: string) => request<Order.IDeleteOrderRes>({
  url: `/hotel/order/deleteOrder?orderNo=${orderNo}`,
  method: "GET"
});

/**
 * 再次预订
 */
export const rebookOrder = (orderId: string) => request<{ success: boolean; newOrderId?: string }>({
  url: "/order/rebook/v1",
  method: "POST",
  data: {
    orderId
  }
});

/**
 * 获取订单筛选条件
 */
export const getOrderFilterConditions = () => request<Order.FilterConditionsResponse>({
  url: "/hotel/order/searchCondition",
  method: "GET"
});

/**
 * 获取活动订单列表
 * 用于获取用户的活动订单列表，支持分页
 *
 * @param params 请求参数，包含分页信息
 * @returns Promise<Request.IResponseResult<Order.ActivityOrderListResponse>> 返回活动订单列表
 */
export const getActivityOrderList = (params: Order.ActivityOrderListRequest) => request<Order.ActivityOrderListResponse>({
  url: '/hotel/order/active/trailOrderList',
  method: 'POST',
  data: params,
  showLoading: false
});
