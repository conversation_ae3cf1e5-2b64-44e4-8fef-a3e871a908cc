<!--pages/tripHelper/routeDetails/routeDetails.wxml-->
<import src="./components/skeleton/routeDetails.skeleton.wxml" />
<template is="skeleton" wx:if="{{loading}}" />

<view wx:else>
<!-- 自定义导航栏 -->
<nav-bar route-name="{{routeDetails.name}}" external-scroll-top="{{scrollTop}}" />
<!-- 轮播图区域 -->
<t-swiper height="500rpx" current="{{1}}" autoplay="{{autoplay}}" duration="{{duration}}" interval="{{interval}}" navigation="{{navigation}}" paginationPosition="{{paginationPosition}}" list="{{routeDetails.picUrls}}">
</t-swiper>
<view class="route-box">
  <!-- 线路信息卡片 -->
  <view class="route-card">
    <view class="route-header">
      <!-- 左侧：线路名称和类型 -->
      <view class="route-left">
        <view class="route-name">{{routeDetails.name}}</view>
        <view class="route-type" wx:if="{{routeDetails.projectInfos && routeDetails.projectInfos.length > 0}}">
          <view class="route-type-box" wx:for="{{routeDetails.projectInfos}}" wx:key="*this" wx:for-index="index">
            <image class="type-icon" src="{{item.projectIcon}}" mode="aspectFit" />
            <text class="type-text">{{item.projectName}}</text>
          </view>
        </view>
      </view>

      <!-- 右侧：位置信息 -->
      <view class="route-right" wx:if="{{routeDetails.cityNames && routeDetails.cityNames.length > 0}}">
        <image class="location-icon" src="{{staticBaseUrl}}/tripHelper/chooseRoute/map.png" mode="aspectFit" />
        <view class="location-text-box">
          <view class="location-text" wx:for="{{routeDetails.cityNames}}" wx:key="*this" wx:for-index="index">
            {{item}} <text wx:if="{{index < routeDetails.cityNames.length - 1}}"></text>
          </view>
        </view>
      </view>
    </view>
  </view>
<!-- 使用封装的RouteDetailBox组件 -->
<route-detail-box route-details="{{routeDetails}}" section-title="线路介绍" />
</view>
<!-- 底部按钮 -->
<view class="bottom-action">
  <view class="action-button" bind:tap="onSelectRoute">选择当前线路</view>
</view>
</view>

