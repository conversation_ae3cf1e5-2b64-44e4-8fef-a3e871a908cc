<!-- 自定义骨架屏模板 -->
<template name="skeleton">
  <view class="skeleton-container">
    <!-- 导航栏骨架 -->
    <view class="skeleton-navbar">
      <view class="skeleton-status-bar"></view>
      <view class="skeleton-nav-content">
        <view class="skeleton-back-btn"></view>
        <view class="skeleton-nav-title"></view>
        <view class="skeleton-nav-right"></view>
      </view>
    </view>

    <!-- 轮播图骨架 -->
    <view class="skeleton-swiper">
      <view class="skeleton-swiper-item"></view>
      <view class="skeleton-swiper-dots">
        <view class="skeleton-dot skeleton-dot-active"></view>
        <view class="skeleton-dot"></view>
        <view class="skeleton-dot"></view>
      </view>
    </view>

    <!-- 路线信息骨架 -->
    <view class="skeleton-route-info">
      <view class="skeleton-route-header">
        <view class="skeleton-route-left">
          <view class="skeleton-route-title"></view>
          <view class="skeleton-route-tags">
            <view class="skeleton-tag"></view>
            <view class="skeleton-tag"></view>
          </view>
        </view>
        <view class="skeleton-route-right">
          <view class="skeleton-location-icon"></view>
          <view class="skeleton-location-text"></view>
        </view>
      </view>
    </view>

    <!-- 描述区域骨架 -->
    <view class="skeleton-description">
      <view class="skeleton-section-title"></view>
      <view class="skeleton-text-lines">
        <view class="skeleton-text-line"></view>
        <view class="skeleton-text-line"></view>
        <view class="skeleton-text-line skeleton-text-line-short"></view>
      </view>
    </view>

    <!-- 行程详情骨架 -->
    <view class="skeleton-itinerary">
      <!-- 第一天 -->
      <view class="skeleton-day-section">
        <view class="skeleton-day-title"></view>
        <view class="skeleton-spot-item">
          <view class="skeleton-timeline">
            <view class="skeleton-spot-icon"></view>
            <view class="skeleton-timeline-line"></view>
          </view>
          <view class="skeleton-spot-content">
            <view class="skeleton-spot-header">
              <view class="skeleton-spot-name"></view>
              <view class="skeleton-spot-type"></view>
            </view>
            <view class="skeleton-spot-desc">
              <view class="skeleton-text-line"></view>
              <view class="skeleton-text-line skeleton-text-line-short"></view>
            </view>
            <view class="skeleton-spot-images">
              <view class="skeleton-image"></view>
              <view class="skeleton-image"></view>
              <view class="skeleton-image"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 第二天 -->
      <view class="skeleton-day-section">
        <view class="skeleton-day-title"></view>
        <view class="skeleton-spot-item">
          <view class="skeleton-timeline">
            <view class="skeleton-spot-icon"></view>
            <view class="skeleton-timeline-line"></view>
          </view>
          <view class="skeleton-spot-content">
            <view class="skeleton-spot-header">
              <view class="skeleton-spot-name"></view>
              <view class="skeleton-spot-type"></view>
            </view>
            <view class="skeleton-spot-desc">
              <view class="skeleton-text-line"></view>
              <view class="skeleton-text-line skeleton-text-line-short"></view>
            </view>
            <view class="skeleton-spot-images">
              <view class="skeleton-image"></view>
              <view class="skeleton-image"></view>
              <view class="skeleton-image"></view>
            </view>
          </view>
        </view>
        <view class="skeleton-spot-item">
          <view class="skeleton-timeline">
            <view class="skeleton-spot-icon"></view>
          </view>
          <view class="skeleton-spot-content">
            <view class="skeleton-spot-header">
              <view class="skeleton-spot-name"></view>
              <view class="skeleton-spot-type"></view>
            </view>
            <view class="skeleton-spot-desc">
              <view class="skeleton-text-line"></view>
              <view class="skeleton-text-line skeleton-text-line-short"></view>
            </view>
            <view class="skeleton-spot-images">
              <view class="skeleton-image"></view>
              <view class="skeleton-image"></view>
              <view class="skeleton-image"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮骨架 -->
    <view class="skeleton-bottom-action">
      <view class="skeleton-action-button"></view>
    </view>
  </view>
</template> 