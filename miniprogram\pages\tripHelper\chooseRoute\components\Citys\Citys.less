/* pages/tripHelper/chooseRoute/components/Citys/Citys.wxss */
@import "../../../../../styles/mixins.less";

.city-selector {
  padding: 32rpx 42rpx 0 32rpx;
  background-color: #fff;
  position: relative;
  /* 当前位置区块 */
  .current-location-section {
    margin-bottom: 64rpx;
    
    .section-title {
      font-size: 28rpx;
      color: @text-title-color;
      font-weight: 400;
      margin-bottom: 24rpx;
      line-height: 1.4;
    }
    
    .current-location-item {
      background-color: @border-color;
      border-radius: 24rpx;
      padding: 0 32rpx;
      text-align: center;
      height: 104rpx;
      display: inline-flex;
      line-height: 104rpx;
      justify-content: space-between;
      align-items: center;
      min-width: 0;
      box-sizing: border-box;
      transition: all 0.3s ease;
      color: @text-title-color;
      // 定位中状态
      // &.loading {
      //   color: @primary-color;
      //   animation: pulse 1.5s infinite;
      // }
      // 成功状态
      &.success {
        background-color: @primary-light-color-2;
        color: @primary-color;
        .location-text {
          font-weight: 500;
        }
      }
      &.line-flex{
        display:flex;
      }
      .location-text {
        font-size: 28rpx;
        // color: @text-title-color;
        margin-left: 8rpx;
        font-weight: 500; 
      }
      .location-item-content{
        display: flex;
        align-items: center;
      }
      .target-iocn{
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
  
  /* 历史记录区块 */
  .history-section {
    margin-bottom: 64rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .section-title {
        font-size: 28rpx;
        color: @text-title-color;
        font-weight: 400;
        line-height: 1.4;
      }
      
      .clear-btn {
        display: flex;
        align-items: center;
        
        .clear-icon {
          width: 28rpx;
          height: 28rpx;
          margin-right: 8rpx;
        }
        
        .clear-text {
          font-size: 24rpx;
          color: @text-color;
          line-height: 1.4;
        }
      }
    }
  }
  
  /* 热门目的地区块 */
  .hot-cities-section {
    margin-bottom: 64rpx;
    
    .section-title {
      font-size: 28rpx;
      color: @text-title-color;
      font-weight: 400;
      margin-bottom: 24rpx;
      line-height: 1.4;
    }
  }
  /* 城市标签网格 */
  .history-tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    max-height: 150rpx;
    overflow: hidden;
    .city-tag {
      background-color: @border-color;
      border-radius: 24rpx;
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 36rpx;
      min-width: 90rpx;
      
      &.selected {
        background-color: @primary-light-color-2;
        
        .city-tag-text {
          color: @primary-color;
          font-weight: 500;
        }
      }
      
      .city-tag-text {
        font-size: 28rpx;
        color: @text-title-color;
        line-height: 1.4;
        white-space: nowrap;
      }
    }
  }
  /* 城市标签网格 */
  .city-tags-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 8rpx;
    
    .city-tag {
      background-color: @border-color;
      border-radius: 24rpx;
      padding: 0 32rpx;
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 81rpx;
      
      &.selected {
        background-color: @primary-light-color-2;
        
        .city-tag-text {
          color: @primary-color;
          font-weight: 500;
        }
      }
      
      .city-tag-text {
        font-size: 28rpx;
        color: @text-title-color;
        line-height: 1.4;
        white-space: nowrap;
      }
    }
  }
  
  /* 城市列表区块 */
  .city-list-section {
    margin-bottom: 50rpx;
    
    .city-letter-group {
      .letter-title {
        font-size: 28rpx;
        color: @text-title-color;
        font-weight: 600;
        margin-bottom: 32rpx;
        line-height: 1.4;
      }
      
      .city-list-content {
        margin-bottom: 32rpx;
        
        .city-item {
          padding: 32rpx 0;
          border-bottom: 2rpx solid #F0F0F0;
          &.selected {
            .city-item-text {
              color: @primary-color;
              font-weight: 500;
            }
          }
          
          .city-item-text {
            font-size: 28rpx;
            color: @text-title-color;
            line-height: 1.4;
          }
        }
        
        .city-divider {
          background-color: @border-color;
          margin: 0 0 32rpx 0;
        }
      }
    }
  }
}

.citys-scroll {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* 右侧字母索引 */
.alphabet-nav {
    position: fixed;
    right: 6rpx;
    top: 60%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 9999;
    font-size: 24rpx;
    font-weight: 500;
    .alphabet-item {
        
      &:first-child {
        writing-mode: vertical-lr;
        letter-spacing: 4rpx;
        .alphabet-text {
          color: @primary-color;
          font-weight: 500;
        }
      }
      
      .alphabet-text {
        font-size: 24rpx;
        color: @primary-color;
        font-weight: 500;
        text-align: center;
        display: block;
      }
    }
  }

/* 定位中动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

