<!-- 悬浮按钮组件 -->
<view class="floating-btn" wx:if="{{visible}}">
  <!-- 编辑模式：显示取消按钮（退出编辑模式） -->
  <view wx:if="{{isEditMode}}" class="floating-cancel-btn" bindtap="onToggleMode">
    <t-icon name="close" size="24" color="#fff" />
    <text class="floating-btn-text">取消</text>
  </view>
  <!-- 普通模式：显示编辑按钮 -->
  <view wx:else class="floating-edit-btn" bindtap="onToggleMode">
    <t-icon name="edit" size="24" color="#fff" />
    <text class="floating-btn-text">编辑</text>
  </view>
</view>
