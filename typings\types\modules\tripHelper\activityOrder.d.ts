declare namespace ActivityOrder {
  interface IOrderPagelParams {
    id: string | number;
  }
  interface IOrderPageResponse {
    userDTOList: UserDTO[];      // 用户列表
    activityDTO: ActivityDTO;    // 活动详细信息
  }
  interface UserDTO {
    userId: string;      // 用户ID
    userName: string;    // 用户名
    avatar: string;      // 头像地址
    sex: number;         // 性别（0: 未知, 1: 男, 2: 女）
  }
  interface ActivityDTO {
    id: number;
    userId: number;               // 发起人ID
    participantIds: string[];     // 参与人ID
    title: string;                // 活动标题
    description: string;          // 活动描述
    assemblyPlaceDTOS: AssemblyPlaceDTO[]; // 集合地点
    coverImages: string[];        // 封面图
    activityType: string;         // 活动类型
    signUpDeadLineTime: string;   // 报名截止时间
    routeInfo: any;               // 路线信息
    startTime: string;            // 开始时间
    endTime: string;              // 结束时间
    dateDesc: string;             // 日期描述
    maxTicketCount: number;       // 最大票数
    minTicketCount: number;       // 最小票数
    leftTicketCount: number;      // 剩余票数
    perCost: PerCost[];           // 单人价格列表
    policyDTO: PolicyDTO;         // 退款政策
    allowWaiting: number;         // 是否允许候补
    status: number;               // 活动状态
    wxImage: string;              // 微信分享图
    showAlternateButton: boolean; // 是否展示候补按钮
    showDetailBtn: boolean;       // 是否展示详情按钮
    showSignInBtn: boolean;       // 是否展示签到按钮
    showCheckinBtn: boolean;      // 是否展示打卡按钮
    showSignUpBtn: boolean;       // 是否展示报名按钮
    cityName: string;             // 城市名称
    showActivityManageBtn: boolean; // 是否展示活动管理按钮
    ifSignIn:boolean;               // 是否已签到
    ifCheckIn:boolean;              // 是否已打卡
  } 
  interface AssemblyPlaceDTO {
    id:string;           // 集合地点id
    startTime: string;   // 集合时间
    placeName: string;   // 地点名称
    cityName: string;    // 所在城市名称
    longitude: number;   // 经度
    latitude: number;    // 纬度
    isChecked?:boolean;  // 是否选择这个集合电
  }
  interface PerCost {
    perCost: number;  // 单人花费
    type: number;     // 类型（如成人票、儿童票等）
  }
  interface PolicyDetail {
    id: number;                   // ID
    userId: string;              // 用户ID
    refundRule: number;          // 退款规则类型
    timeType: number;            // 时间类型
    time: number;                // 时间点
    canRefund: number;           // 是否可退（1可退 0不可退）
    refundProportion: number;    // 退款比例（%）
    dateDesc: string;            // 时间描述
    refundProportionDesc: string; // 比例描述
    refundAmount:string;          // 实际可退款金额
    isExpire: boolean;           // 是否过期
  }
  interface PolicyDTO {
    id: number;
    name: string;             // 政策名称
    desc: string;             // 政策描述
    payTimeDesc:string;       // 付款时间描述
    refundRuleDesc:string;    // 退款规则描述
    policyDetails: PolicyDetail[];  // 详细规则
  } 
  interface IOrderlParams{
    orderNo: string | number;
    activityId: string | number,
    orderPayMoney: string | number,
    goodsName: string
  }
  interface IOrderlResponse {
    appId: string;         // 小程序 App ID
    prepayId: string;      // 预支付交易会话标识（带前缀 "prepay_id="）
    orderNo: string;       // 订单编号
    paidMoney: string;     // 实际支付金额（单位：元，字符串格式）
    payTime: string;       // 支付时间（格式：YYYY-MM-DD HH:mm:ss）
    payStatus: number;     // 支付状态码（例如：10 表示已支付）
    timeStamp: string;     // 时间戳（字符串格式，前端 wx.requestPayment 要求 string 类型）
    nonceStr: string;      // 随机字符串（用于签名）
    signType: "MD5" | "HMAC-SHA256" | "RSA";      // 签名类型（如 RSA）
    paySign: string;       // 支付签名（由后端生成）
  }
  interface PerOtem{
    type:number|string;           // 人群
    price:number|string;   // 价格
    num:number;            // 购买数量
    typename:string;
  }
  interface RefundItem{
    date:string;
    refundProportion:string;
    amount:string|number;
  }
  interface OrderPageData {
    headImg:string;         // 头图
    title:string;           // 标题
    cityName:string;        // 城市
    activitytime:string;    // 活动时间 
    assemblyPlaceList:AssemblyPlaceDTO[]; // 集合地点
    stock:number;           // 库存 -1不限库存
    perList:PerOtem[];      // 人群购买信息
    totalPrice:number|string;// 总价
    payTimeDesc:string;      // 付款时间描述
    refundRuleDesc:string;   // 退款规则描述
    orderRefundList:RefundItem[];
    activityId:number | string;
    isWaiting:boolean;
  }
  interface OrderSubmitParams {
    activityId: number|string; // 活动 ID
    pointId: number|string;    // 集合点 ID
    pointName: string;         // 集合点名称
    paymentAmount: number;     // 支付金额（单位：分或元，取决于后端）
    paymentType: number;       // 支付方式（如：0=未支付, 1=微信, 2=支付宝）
    oldPersonPrice?: number;    // 老年人单价
    oldPersonCount?: number;    // 老年人数量
    childrenPrice?: number;     // 儿童单价
    childrenCount?: number;     // 儿童数量
    adultPrice?: number;        // 成人单价
    adultCount?: number;        // 成人数量
    isWaiting: number;         // 是否候补（0=否，1=是）
  }
  interface PaymentInfo {
    orderNo: string;         // 订单编号
    paymentAmount: number;   // 支付金额（单位：元）
  }
}