<!-- 个人资料页面 - 用户信息编辑和管理页面 -->
<view class="userInfo-my-page">
  <!-- 用户信息编辑区域 -->
  <view class="userInfo">
    <!-- 头像编辑项 -->
    <view class="avatar userInfo-item">
      <text class="userInfo-title">头像</text>
      <view class="userInfo-content" bindtap="onAvatarClick">
        <image class="userInfo-avatar" src="{{storeUserInfo.avatar}}"></image>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
    
    <!-- 昵称编辑项 -->
    <view class="nickname userInfo-item">
      <text class="userInfo-title">昵称</text>
      <view class="userInfo-content" bindtap="onNicknameClick">
        <!-- 条件渲染：昵称为空时显示灰色样式 -->
        <text class="userInfo-text {{!storeUserInfo.nickname ? 'userInfo-text-empty' : ''}}">{{storeUserInfo.nickname || ''}}</text>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
    
    <!-- 性别编辑项 -->
    <view class="gender userInfo-item">
      <text class="userInfo-title">性别</text>
      <view class="userInfo-content" bindtap="onGenderClick">
        <!-- 根据性别显示状态动态应用样式类 -->
        <text class="userInfo-text {{genderDisplay.className}}">{{genderDisplay.text}}</text>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
    
    <!-- 生日编辑项 -->
    <view class="birthday userInfo-item">
      <text class="userInfo-title">生日</text>
      <view class="userInfo-content" bindtap="onBirthdayClick">
        <!-- 条件渲染：生日为空时显示"未设置"和灰色样式 -->
        <text class="userInfo-text {{!storeUserInfo.birthday ? 'userInfo-text-empty' : ''}}">{{storeUserInfo.birthday || '未设置'}}</text>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
  </view>
  
  <!-- 退出登录按钮区域 -->
  <view class="login-out">
    <text class="login-out-btn">退出登录</text>
  </view>
</view>

<!-- 性别选择器组件 - 使用TDesign组件库 -->
<t-picker
  visible="{{genderPickerVisible}}"
  title="选择性别"
  cancelBtn="取消"
  confirmBtn="确认"
  value="{{genderPickerValue}}"
  bindcancel="onGenderPickerCancel"
  bindconfirm="onGenderPickerConfirm"
>
  <t-picker-item options="{{genderOptions}}" />
</t-picker>

<!-- 生日选择器组件 - 日期模式，限制选择范围 -->
<t-date-time-picker
  visible="{{birthdayPickerVisible}}"
  title="选择生日"
  cancelBtn="取消"
  confirmBtn="确认"
  mode="date"
  value="{{birthdayPickerValue}}"
  start="{{birthdayStartDate}}"
  end="{{birthdayEndDate}}"
  bindcancel="onBirthdayPickerCancel"
  bindconfirm="onBirthdayPickerConfirm"
/>

<!-- 昵称修改弹框组件 -->
<nickname-popup
  visible="{{ showNicknamePopup }}"
  current-nickname="{{ storeUserInfo.nickname }}"
  bind:cancel="onCancelNickname"
  bind:save="onSaveNickname"
/>