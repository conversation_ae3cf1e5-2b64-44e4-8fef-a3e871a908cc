Component({

  /**
   * 组件的属性列表
   */
  properties: {
    activeProject: {
      type: Object,
      value: undefined
    },
    projectList: {
      type: Array,
      value: [] as Activity.IProject[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: false,
    projectList: [] as Activity.IProject[]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleProjectSelect(e: WechatMiniprogram.TouchEvent<{}, {}, { item: Activity.IProject }>) {
      this.triggerEvent('change', e.currentTarget.dataset.item);
    },
    handleSubmit() {
      if (this.data.activeProject) {
        this.triggerEvent('next');
      }
    }
  }
})