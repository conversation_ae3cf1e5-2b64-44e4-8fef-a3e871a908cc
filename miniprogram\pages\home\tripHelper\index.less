/* pages/home/<USER>/index.wxss */
page {
  background-color: #fff;
  padding-bottom: 164rpx; /* 为自定义tabbar预留空间 */
}

.trip-helper-container {
  min-height: 100vh;

  // 顶部背景区域
  .hero-section {
    position: relative;
    height: 822rpx;

    .bg-image {
      width: 100%;
      height: 100%;
    }

    .hero-content {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      color: white;

      .hero-text {
        font-size: 28rpx;
        margin-bottom: -40rpx;
      }

      .suggest-btn {
        background: #0198FF;
        color: #fff;
        transform: translateY(50%);
        display: flex;
        width: 446rpx;
        align-items: center;
        justify-content: center;
        border-radius: 120rpx;
        height: 112rpx;
        font-size: 40rpx;
        border: none;

        &::after {
          border: none;
        }
      }
    }
  }

  // 产品计划区域
  .product-plan-section {
    margin-top: 68rpx;
    padding: 40rpx 12rpx;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32rpx;

      .title-left, .title-right {
        width: 48rpx;
        height: 48rpx;
      }

      .title-text {
        font-size: 36rpx;
        font-weight: bold;
        color: #33333E;
        margin: 0 8rpx;
      }
    }

    .plan-list {
      .plan-item {
        display: flex;
        background: white;
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        position: relative;

        // 竖线（除了最后一个item）
        &:not(.last-item)::after {
          content: '';
          position: absolute;
          left: 46rpx; // 图标中心位置 (24rpx padding + 22rpx 图标中心)
          top: 68rpx; // 图标下方开始，无上边空白
          width: 2rpx;
          height: calc(100% + 24rpx); // item高度 + margin-bottom
          background: #DBDBDB;
        }

        // 最后一个item不显示竖线
        &.last-item::after {
          display: none;
        }

        // 左侧图标
        .plan-icon {
          width: 44rpx;
          height: 44rpx;
          margin-right: 16rpx;
          flex-shrink: 0;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .plan-content {
          flex: 1;
          .plan-header {
            display: flex;
            align-items: flex-end;
            justify-content: flex-start;
            margin-bottom: 8rpx;

            .plan-title-img {
              height: 48rpx;
              margin-right: 16rpx;
              flex-shrink: 0;
            }

            .plan-time {
              font-size: 24rpx;
              color: #66666E;
              flex-shrink: 0;
            }
          }

          .plan-desc {
            font-size: 24rpx;
            color: #66666E;
            margin-bottom: 24rpx;
          }

          .plan-images {
            display: flex;
            gap: 16rpx;

            .plan-img-item {
              display: flex;
              flex-direction: column;
              align-items: center;

              .plan-img {
                width: 180rpx;
                height: 180rpx;
                border-radius: 24rpx;
                margin-bottom: 12rpx;
              }

              .plan-img-desc {
                font-size: 24rpx;
                color: #66666E;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }

  // 功能更新区域
  .feature-section {
    padding: 0 32rpx 40rpx;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32rpx;

      .title-left, .title-right {
        width: 48rpx;
        height: 48rpx;
      }

      .title-text {
        font-size: 36rpx;
        font-weight: bold;
        color: #33333E;
        margin: 0 8rpx;
        text-align: center;
      }
    }

    .feature-container {
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;

      .feature-bg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .feature-content {
        position: relative;
        z-index: 2;

        .feature-row {
          display: flex;
          gap: 16rpx;
          justify-content: space-between;
          margin-bottom: 40rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .feature-item {
            flex: 1;
            width: 218rpx;
            height: 208rpx;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            text-align: center;
            background: #F9F9F9;
            border-radius: 24rpx;


            .feature-icon {
              width: 56rpx;
              height: 56rpx;
              margin-bottom: 16rpx;
            }

            .feature-text {
              font-size: 28rpx;
              color: #66666E;
            }
          }
        }
      }
    }
  }
}