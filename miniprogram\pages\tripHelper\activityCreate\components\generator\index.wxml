<wxs src="../../../../../utils/tool.wxs" module="tool"/>
<wxs src="./util.wxs" module="util"/>

<view class="creater-page">
	<view class="creater-body">
		<RouteLinePick id="routeline" routeId="{{formModel.routeId}}" title="{{formModel.title}}" description="{{formModel.description}}" coverImages="{{formModel.coverImages}}" activeTab="{{activeTab}}" projectId="{{formModel.activityType}}" activeStep="{{activeStep}}" bindchange="handleRouteLineChange" bind:updateActiveTab="handleUpdateActiveTab" />

		<!-- 表单区 -->
		<view class="card-wrap">
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/date.png'}}" mode="widthFix" />
						<text class="form-item-label-text">活动日期*</text>
					</view>
					<view class="form-item-content {{formModel.startTime ? null : 'is-placeholder'}}" bind:tap="onDateRangeOpen">
						<view class="form-item-content-value">{{formModel.startTime ? util.activeDateFormat(formModel.startTime, formModel.endTime) : '请选择'}}</view>
					</view>
				</view>
			</view>
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/place.png'}}" mode="widthFix" />
						<text class="form-item-label-text">集合地点*</text>
					</view>
					<view class="form-item-content {{formModel.assemblyPlaces.length ? null : 'is-placeholder'}}" bind:tap="handleJumpMeetingPoint">
						<view class="form-item-content-value">{{formModel.assemblyPlaces.length ? '' : '请选择'}}</view>
					</view>
				</view>
				<view class="data-item" wx:for="{{formModel.assemblyPlaces}}" wx:key="index">
					<text class="time-text">{{tool.formatDateTime(item.startTime, 'MM.DD HH:mm')}}</text>
					<text class="poi-text">{{item.placeName}}</text>
					<view class="delete-trigger" bind:tap="handlePlaceRemove" data-index="{{index}}"></view>
				</view>
			</view>
		</view>

		<view class="card-wrap pb-24rpx">
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/multiple-people.png'}}" mode="widthFix" />
						<text class="form-item-label-text">活动人数*</text>
					</view>
					<view class="form-item-content {{formModel.ticketCount ? null : 'is-placeholder'}}" bind:tap="handleOpenNumberPeoplePicker">
						<view class="form-item-content-value {{formModel.ticketCount ? null : 'is-placeholder'}}">{{formModel.grouponCount ? formModel.grouponCount + '-' + formModel.ticketCount +'人' : formModel.ticketCount ? formModel.ticketCount + '人' : '请填写'}}</view>
					</view>
				</view>
				<view class="form-item-tips" hidden="{{!formModel.grouponCount}}">报名截止人数不满{{formModel.grouponCount}}人，活动将自动取消</view>
			</view>
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/price.png'}}" mode="widthFix" />
						<text class="form-item-label-text">活动价格*</text>
					</view>
					<view class="form-item-content {{formModel.perCost.length ? null : 'is-placeholder'}}" bind:tap="handleOpenPriceModal">
						<view class="form-item-content-value">{{ formModel.perCost.length ? '' : '请填写'}}</view>
					</view>
				</view>
				<block wx:if="{{formModel.perCost.length}}">
					<view class="data-item" wx:for="{{formModel.perCost}}" wx:key="type">
						<text>{{AgeGroupDisplay[item.type]}}</text>
						<text>{{item.perCost}}元/人</text>
					</view>
				</block>
			</view>
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/cancel-policy.png'}}" mode="widthFix" />
						<text class="form-item-label-text">取消政策*</text>
					</view>
					<view class="form-item-content {{formModel.cancelPolicyId ? null : 'is-placeholder'}}" bind:tap="handleOpenCancelPolicyModal">
						<view class="form-item-content-value">{{ formModel.cancelPolicyId ? '' : '请选择'}}</view>
					</view>
				</view>
				<view class="data-item" wx:if="{{formModel.cancelPolicyId}}">
					<view>
						<view class="policy-title">{{tool.findValueByKey(policyList, 'id', formModel.cancelPolicyId, 'name')}}</view>
						<rich-text class="policy-desc" nodes="{{tool.findValueByKey(policyList, 'id', formModel.cancelPolicyId, 'desc')}}"/>
					</view>
				</view>
			</view>
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/date.png'}}" mode="widthFix" />
						<text class="form-item-label-text">报名截止*</text>
					</view>
					<view class="form-item-content {{formModel.deadlineTime ? null : 'is-placeholder'}}" bind:tap="handleOpenDeadlineTimePicker">
						<view class="form-item-content-value">{{formModel.deadlineTime ? tool.formatDateTime(formModel.deadlineTime, 'MM.DD(周dd) HH:mm')  : '请选择'}}</view>
					</view>
				</view>
			</view>
			<view class="cell-item">
				<view class="form-item">
					<view class="form-item-label">
						<image class="form-item-label-icon" src="{{staticBaseUrl + '/tripHelper/activity/wechat.png'}}" mode="widthFix" />
						<text class="form-item-label-text">活动微信群</text>
					</view>
					<view class="form-item-content {{formModel.wxImage ? '' : 'is-placeholder'}}" bind:tap="handleOpenQrcodeModal">
						<view class="form-item-content-value">
							<view class="qrcode" hidden="{{!formModel.wxImage}}"></view>
							<text hidden="{{formModel.wxImage}}">仅在报名后展示</text>
						</view>
					</view>
				</view>
			</view>

			<view class="wait-replace-wrap">
				<view class="wait-replace-label">
					<image src="{{staticBaseUrl + '/tripHelper/activity/wait-replace.png'}}" mode="" />
					<view class="explain-wrap">
						允许候补
					</view>
				</view>
				<Checkbox value="{{formModel.allowWaiting}}" trueValue="{{1}}" falseValue="{{0}}" size="40" bindchange="handleAllowWaitingChange" />
			</view>
		</view>
	</view>

	<!-- 操作区 -->
	<view class="operate-wrap">
		<view class="draft-wrap" catch:tap="handleSaveDraft">
			<view class="draft-icon"></view>
			<text>草稿</text>
		</view>
		<button class="trigger-item" catch:tap="handleSubmit" disabled="{{loading}}">{{activityId ? '更新' : '创建'}}活动</button>
	</view>
</view>

<Daterange visible="{{showDateRange}}" value="{{[formModel.startTime, formModel.endTime]}}" min="{{nowTime}}" bind:change="onDateRangeChange" bind:visible-change="onDateRangeVisibleChange" />
<!-- 人数选择 -->
<NumberPeoplePicker visible="{{showNumberPeoplePicker}}" maxValue="{{formModel.ticketCount}}" minValue="{{formModel.grouponCount}}" bind:update-visible="handleNumberPickerChange" bindchange="handleNumberPeopleChange" />
<!-- 活动报名截止时间 -->
<t-date-time-picker auto-close title="报名截止时间" visible="{{showDeadlineTime}}" mode="minute" value="{{formModel.deadlineTime}}" format="YYYY-MM-DD HH:mm:ss" start="{{nowTime}}" end="{{formModel.startTime ? formModel.startTime + ' 00:00:00' : null}}" bindchange="handleDeadlineTimeChange" bindcancel="handleCloseDeadlineTimePicker" />
<!-- 活动价格 -->
<PriceModal visible="{{showPriceModal}}" priceList="{{formModel.perCost}}" bind:update-visible="handlePriceModalChange" bindchange="handlePriceChange" />
<!-- 活动二维码 -->
<QrcodeModal visible="{{showQrcodeModal}}" url="{{formModel.wxImage}}" bind:update-visible="handleQrcodeModalChange" bindchange="handleQrcodeValueChange" />
<!-- 取消政策 -->
<CancelPolicy visible="{{showCancelPolicyModal}}" value="{{formModel.cancelPolicyId}}" list="{{policyList}}" bindchange="handleCancelPolicyChange" bind:update-visible="handleCancelPolicyModalToggle" />