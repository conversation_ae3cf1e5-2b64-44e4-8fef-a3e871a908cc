Component({
  properties: {
    commemtData: {
      type: Object,
      value: {} as ActivityOrderDetail.ICheckInfoResponse,
    }
  },
  data: {
    processedImgList: [] as string[],
    starImages: [] as string[],
  },
  lifetimes: {
    attached() {
      this.processImageList();
      this.processStar();
    }
  },
  methods: {
    processImageList() {
      const { commemtData } = this.properties;
      
      if (commemtData && commemtData.pics) {
        const limitedImgList = commemtData.pics.slice(0, 3);
        this.setData({
          processedImgList: limitedImgList
        });
      }
    },
    processStar() {
      const { commemtData } = this.properties;
      const activeStar = 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrderDetail/star_icon.png';
      const inactiveStar = 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityOrderDetail/star_dis_icon.png';
      
      if(commemtData && commemtData.recommendStar >=0){
        const rating = commemtData.recommendStar

        const stars = Array(5).fill(0).map((_, index) => 
          index < rating ? activeStar : inactiveStar
        );

        this.setData({
          starImages: stars
        });
      }
    },
    viewImage(event: WechatMiniprogram.BaseEvent){
      const currentUrl = event.currentTarget.dataset.url
      wx.previewImage({
        current: currentUrl,
        urls:this.data.processedImgList
      })
    },
  }
});