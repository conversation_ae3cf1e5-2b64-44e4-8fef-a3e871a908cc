/* pages/tripHelper/order/index.less */
page {
  background-color: #F3F3F3;
  height: 100vh;
}

.order-container {
  height: 100vh;

  .order-list {
    padding: 24rpx 32rpx;

    .order-item {
      background: #FFFFFF;
      border-radius: 32rpx;
      padding: 24rpx;
      margin-bottom: 24rpx;

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 32rpx;

        .order-info {
          display: flex;
          flex: 1;

          .order-image {
            width: 64rpx;
            height: 64rpx;
            border-radius: 16rpx;
            margin-right: 24rpx;
          }

          .order-details {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;

            .order-title {
              font-size: 28rpx;
              color: #66666E;
              margin-bottom: 4rpx;
            }

            .registration-time {
              font-size: 24rpx;
              color: #99999E;
            }
          }
        }

        .order-status {
          margin-top: -24rpx;
          margin-right: -24rpx;
          width: 122rpx;
          height: 72rpx;
          line-height: 72rpx;
          text-align: center;
          border-radius: 0rpx 30rpx 0rpx 32rpx;
          font-size: 24rpx;
          white-space: nowrap;

          // 待支付(0) - 橙色系
          &.status-0 {
            background: #FFF6F0;
            color: #FB7A1E;
          }

          // 报名成功(1) - 蓝色系
          &.status-1 {
            background: #E6FEFF;
            color: #00C2CC;
          }

          // 候补中(2) - 橙色系
          &.status-2 {
            background: #FFF6F0;
            color: #FB7A1E;
          }

          // 已退款(3) - 灰色系
          &.status-3 {
            background: #F3F3F3;
            color: #33333E;
          }

          // 已取消(4) - 灰色系
          &.status-4 {
            background: #F3F3F3;
            color: #33333E;
          }
        }
      }

      .order-amount-section {
        margin-bottom: 32rpx;

        .amount-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 24rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .amount-label {
            font-size: 28rpx;
            color: #66666E;
          }

          .amount-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .amount-value {
              font-size: 28rpx;
              font-weight: 500;
              color: #33333E;
              margin-bottom: 8rpx;
            }

            .amount-details {
              font-size: 24rpx;
              color: #99999E;
              display: flex;
              flex-direction: row;
              gap: 16rpx;
              flex-wrap: wrap;

              .detail-item {
                white-space: nowrap;
              }
            }

            .refund-ratio {
              font-size: 20rpx;
              color: #99999E;
            }
          }
        }
      }

      .divider {
        height: 2rpx;
        background: #F0F0F0;
        margin: 24rpx 0;
      }

      .order-actions {
        text-align: right;
        width: 100%;

        // 通用按钮样式
        .pay-btn,
        .refund-btn {
          background: #0198FF;
          color: #FFFFFF;
          font-size: 24rpx;
          height: 72rpx;
          width: 178rpx;
          padding: 20rpx 40rpx;
          font-weight: 500;
          border-radius: 120rpx;
          border: none;
          line-height: 32rpx;
          text-align: center;
          display: inline-block;
          box-sizing: border-box;

          &::after {
            border: none;
          }

          &:active {
            background: #0198FF;
          }
        }
      }
    }
  }
}


