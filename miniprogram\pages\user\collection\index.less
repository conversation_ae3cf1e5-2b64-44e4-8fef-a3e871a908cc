/* pages/user/collection/index.less */
.collection-page {
  width: 100vw;
  min-height: 100vh;
  background-color: #f5f5f5;

  /* 编辑模式下的样式调整 */
  &.edit-mode {
    /* 给内容区域添加底部间距，避免被底部操作栏遮挡 */
    .collection-content {
      padding-bottom: 200rpx; /* 底部操作栏高度 */
    }
  }

  /* Tab切换 */
  .tab-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
    .tab-list {
      display: flex;
      padding: 0 32rpx;

      .tab-item {
        flex: 1;
        position: relative;
        height: 112rpx;
        line-height: 112rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s ease;

        .tab-text {
          font-size: 28rpx;
          color: #33333E;
          transition: color 0.3s;
        }

        .tab-indicator {
          position: absolute;
          bottom: 22rpx;
          width: 48rpx;
          height: 6rpx;
          background-color: #568DED;
        }

        &.active .tab-text {
          color: #568DED;
          font-weight: 600;
        }

        &:active {
          background-color: #f5f5f5;
        }
      }
    }
  }

  /* 内容区域 */
  .content-container {
    flex: 1;
    margin-top: 112rpx; /* 为固定的tab栏留出空间 */
  }

  /* 编辑模式下为底部操作栏留出空间 */
  &.edit-mode .content-container {
    padding-bottom: 200rpx;
  }

  /* 加载状态 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;

    .loading-text {
      margin-top: 32rpx;
      font-size: 28rpx;
      color: #999;
    }
  }

  /* 列表容器 */
  .list-container {
    padding: 16rpx 0;
  }



  /* 空状态 */
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 160rpx 0;

    .empty-text {
      font-size: 32rpx;
      color: #999;
      margin: 32rpx 0 16rpx;
    }

    .empty-desc {
      font-size: 28rpx;
      color: #ccc;
    }
  }

  /* 占位内容 */
  .placeholder-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 160rpx 0;

    .placeholder-text {
      font-size: 32rpx;
      color: #999;
    }
  }

  /* 上拉加载更多 */
  .load-more-container {
    padding: 32rpx 0;

    .load-more-item {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16rpx;

      .load-more-text {
        font-size: 28rpx;
        color: #999;

        &.no-more {
          color: #ccc;
        }
      }
    }
  }

}
