/* components/imageUploader/index.less */
.image-uploader {
  .uploader-header {
    margin-bottom: 24rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      line-height: 40rpx;
    }
  }

  .upload-scroll {
    height: 168rpx; // 148rpx + 20rpx padding
    padding-bottom: 8rpx;
  }

  .upload-grid {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    white-space: nowrap;

    .upload-item {
      width: 148rpx;
      min-width: 148rpx;
      max-width: 148rpx;
      height: 148rpx;
      flex-shrink: 0;
      background-color: #f7f7f7;
      border-radius: 24rpx;
      margin-right: 16rpx;
      position: relative;

      &:last-child {
        margin-right: 0;
      }

      &.image-item {
        .uploaded-image {
          width: 100%;
          height: 100%;
          border-radius: 24rpx;
        }

        .delete-btn {
          position: absolute;
          top: -4rpx;
          right: -0rpx;
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
          // 增加触摸区域，改善用户体验
          // 防止触摸事件穿透
          background: transparent;

          .delete-icon {
            width: 40rpx;
            height: 40rpx;
            // 确保图片在中心位置
            flex-shrink: 0;
          }
        }
      }

      &.upload-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        border: 2rpx dashed #cccccc;
        background-color: #fafafa;

        &.uploading {
          opacity: 0.6;
          pointer-events: none;
        }

        .upload-icon {
          width: 48rpx;
          height: 48rpx;
          flex-shrink: 0;
        }

        .upload-text {
          font-size: 24rpx;
          color: #666666;
          line-height: 32rpx;
          text-align: center;
        }
      }
    }
  }

  .uploader-content {
    margin-top: 24rpx;
  }
}
