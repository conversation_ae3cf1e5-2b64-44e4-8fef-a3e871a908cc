<block wx:if="{{!isLoading}}">
  <!-- navbar -->
  <nav-bar isOpacity="{{isShowOpacityNavbar}}"></nav-bar>
  <!-- 页面滚动部分 -->
  <scroll-view scroll-y class="main" bindscroll="handleScroll">
    <!-- 轮播图 -->
    <view class="activity-banner">
      <!-- 轮播图 -->
      <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500">
        <block wx:for="{{detailData.coverImages}}" wx:key="index">
          <swiper-item>
            <image src="{{item}}" mode="aspectFill" class="swiper-image"></image>
          </swiper-item>
        </block>
      </swiper>
      <!-- 城市 -->
      <view class="location-box">
        <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/loction_icon.png"></image>
        <text>{{detailData.cityName}}</text>
      </view>
    </view>

    <!-- 头部标题 -->
    <view class="activity-title">
      <text class="title">{{detailData.title}}</text>
      <!-- 标签 -->
      <view class="tips-list">
        <!-- 本期不做 -->
        <!-- 保险 -->
        <view 
          class="tips-item" 
          data-type="insurancePlan" 
          data-visible="{{true}}" 
          bindtap="handPop">
          <image class="p1" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/ywbz_icon.png"></image>
          <text>意外保障</text>
          <image class="p2" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/right_icon.png"></image>
        </view>
        <!-- 退改 -->
        <view 
          class="tips-item" 
          data-type="refundPolicy" 
          data-visible="{{true}}" 
          bindtap="handPop" 
          wx:if="{{detailData.policyDTO &&detailData.policyDTO.name}}">
          <image class="p1" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/zdt_icon.png"></image>
          <text>{{detailData.policyDTO.name}}</text>
          <image class="p2" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/right_icon.png"></image>
        </view>
      </view>
      <view class="price-box">
        <view class="content">
          <text class="left">{{detailData.integerPrice}}</text>
          <view class="right">
            <text class="t1">{{detailData.decimalPrice}}</text>
            <text class="t2">元/人</text>
          </view>
        </view>

      </view>

    </view>

    <!-- 活动信息 -->
    <view class="activity-message">
      <!-- 时间 -->
      <view class="title-box">
        <view class="left-content">
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/time_icon.png" />
          <text>活动时间</text>
        </view>
        <text class="time-txt">{{detailData.activityTime}}</text>
      </view>

      <!-- 地点 -->
      <view class="address">
        <view class="title-left">
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/address_icon.png" />
          <text>集合地点</text>
        </view>
        <view class="address-item" wx:for="{{detailData.assemblyPlaceDTOS}}" wx:key="index" data-lon="{{item.longitude}}" data-lat="{{item.latitude}}" data-address="{{item.placeName}}" data-cityName="{{item.cityName}}" bindtap="openMap">
          <text>
            <text>{{item.startTime}}</text>
            <text class="blod">{{item.placeName}}</text>
          </text>
          <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/map_icon.png" />
        </view>
      </view>

      <!-- 成员 -->
      <view class="member">
        <view class="title-box">
          <view class="left-content">
            <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/member_icon.png" />
            <text>
              <text>报名人数</text>
              <text class="txt-num">{{detailData.leftTicketCount}}/{{detailData.maxTicketCount}}</text>
            </text>
          </view>
          <view class="right-content" data-type="registrationList" bindtap="jumpToRegistrationList">
            <text>查看全部</text>
            <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/right_icon.png" />
          </view>
        </view>
        <view class="head-list">
          <view class="head-item" wx:for="{{memberList}}" wx:key="index">
            <view class="head-pic-box">
              
              <view class="head-pic-box">
                <image class="head-pic" src="{{item.imageUrl}}" />
                <view class="icon-box {{item.sex === 0 ? 'girl' : 'boy'}}">
                  <image 
                    class="icon" 
                    src="{{'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/' + (item.sex === 0 ? 'girl' : 'boy') + '_icon.png'}}" 
                    mode="aspectFit"
                  />
                </view>
              </view>
            </view>
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>


    </view>

    <!-- 活动描述 -->
    <view class="activity-describe">
      <text class="title">活动描述</text>
      <block>
        <!-- 选择系统线路 -->
        <view wx:if="{{detailData.routeInfo}}">
          <!-- 引入组件 -->
        </view>
        <!-- 自定义线路 -->
        <view wx:else class="content">
          <text class="describe-txt">{{detailData.description}}</text>
        </view>
      </block>
      <view class="bottom">
        <view class="line"></view>
        <text>到底了</text>
        <view class="line"></view>
      </view>
    </view>
  </scroll-view>
  <!-- 底部按钮 -->
  <view class="bottom-bar">
    <view class="bottom-bar-content">
      <view class="left" bindtap="shareActivity">
        <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/share_icon.png" mode="" />
        <text>分享活动</text>
      </view>
      <view class="left" bindtap="signInActivity" wx:if="{{detailData.showSignInBtn}}">
        <image src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/tripHelper/activityDetail/check_icon.png" mode="" />
        <text>签到</text>
      </view>
      <view 
        wx:if="{{detailData.btmObj}}" 
        class="right {{detailData.btmObj.isDisabled ? 'disabled' : ''}} {{detailData.showSignInBtn ? 'short' : 'long'}}" 
        data-type="{{detailData.btmObj.type}}" 
        bindtap="btmClickEvent"
      >
        <text>{{detailData.btmObj.desc}}</text>
      </view>
    </view>
  </view>
  <!-- 分享卡片 -->
  <share-card 
    visible="{{shareCardVisible}}" 
    data="{{shareData}}"></share-card>
  <!-- 退款政策 -->
  <refund-modal 
    visible="{{isShowRefundModal}}" 
    bind:close="handPop"></refund-modal>
  <!-- 保险 -->
  <insurance-modal 
    visible="{{isShowInsuranceModal}}" 
    bind:close="handPop"></insurance-modal>
  <!-- 活动管理 -->
  <management-modal 
    visible="{{isShowManagementModal}}"
    setList="{{setActivityList}}"
    bind:close="handPop"
    bind:btmClick="btmClickEvent"></management-modal>
  <!-- 悬浮弹窗 -->
  <middle-modal 
    visible="{{isShowMiddleModal}}"
    content="{{middleModalContent}}"
    bind:close="handPop"></middle-modal>
  
</block>

<skeleton-screen wx:else></skeleton-screen>