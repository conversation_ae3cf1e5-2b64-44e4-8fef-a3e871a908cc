<t-popup placement="bottom" visible="{{visible}}" bind:visible-change="handleVisibleChange">
	<view class="popup-body">
		<view class="popup-title">
			<text></text>
			<text class="main-title">上传二维码</text>
			<text class="close-trigger" bind:tap="handleClose"></text>
		</view>
		
		<view class="popup-content">
			<Uploader
				value="{{imageUrl ? [imageUrl] : []}}"
				showTitle="{{false}}"
				max-count="{{1}}"
				bind:change="handleImageChange"
			/>
		</view>
	</view>
	<view class="operate-wrap">
		<button bind:tap="handleSubmit">上传</button>
	</view>
</t-popup>