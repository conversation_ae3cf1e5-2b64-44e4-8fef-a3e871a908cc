import { getImageUrl } from "../../../utils/images";
import { uploadFeedbackImage, submitFeedback } from "../../../api/my";
import { EFeedbackType } from "../../../enum/my";
import { ComponentWithComputedStore } from "../../../core/componentWithStoreComputed";
import { storeBindingsBehavior } from "mobx-miniprogram-bindings";
import { behavior as computedBehavior } from "miniprogram-computed";
import { store } from "../../../store/index";

// pages/user/feedback/index.ts
ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior, computedBehavior],

  storeBindings: {
    store,
    fields: ['userInfo'],
    actions: []
  } as const,
  data: {
    // 反馈类型选项
    feedbackTypes: [
      { label: '功能故障', value: 'bug' },
      { label: '产品建议', value: 'suggestion' },
      { label: '订单问题', value: 'order' },
      { label: '其他反馈', value: 'other' }
    ] as Feedback.TypeOption[],
    selectedType: '', // 选中的反馈类型
    feedbackContent: '', // 反馈内容
    uploadedImages: [] as string[], // 已上传的图片列表（本地路径）
    uploadedImageUrls: [] as string[], // 已上传的图片URL列表（服务器返回）
    uploading: false, // 是否正在上传图片
    pictureIcon: getImageUrl('user/picture.png'),
    feedbackCloseIcon: getImageUrl('user/feedback_close.png'),
  },

  computed: {
    // 是否可以提交
    canSubmit(data: Record<string, unknown>) {
      const typedData = data as unknown as Feedback.FormData;
      return typedData.selectedType && typedData.feedbackContent.trim();
    },
    // 上传进度
    uploadProgress(data: Record<string, unknown>) {
      const typedData = data as unknown as { uploadedImages: string[] };
      return typedData.uploadedImages.length / 9 * 100;
    },
    // 剩余可上传图片数量
    remainingImageCount(data: Record<string, unknown>) {
      const typedData = data as unknown as { uploadedImages: string[] };
      return 9 - typedData.uploadedImages.length;
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化
    }
  },

  methods: {
    /**
     * 选择反馈类型
     */
    onSelectType(e: WechatMiniprogram.BaseEvent) {
      const value = e.currentTarget.dataset.value;
      this.setData({
        selectedType: value
      });
    },

    /**
     * 输入反馈内容
     */
    onContentInput(e: WechatMiniprogram.Input) {
      const value = e.detail.value;
      this.setData({
        feedbackContent: value
      });
    },

  /**
   * 选择图片并上传
   */
  onChooseImage() {
    const remainingCount = 9 - this.data.uploadedImages.length;

    if (this.data.uploading) {
      wx.displayToast({
        title: '正在上传中，请稍候',
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const filePaths = res.tempFiles.map(file => file.tempFilePath);
        this.uploadImages(filePaths);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.displayToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 批量上传图片（逐个上传方式，更稳定可靠）
   */
  async uploadImages(filePaths: string[]) {
    this.setData({ uploading: true });

    try {
      // 直接使用逐个上传方式，避免FormData方式的复杂性和不稳定性
      await this.uploadImagesOneByOne(filePaths);
    } catch (error) {
      console.error('上传图片异常:', error);
      wx.hidePrevLoading();
      wx.displayToast({
        title: '图片上传失败',
        icon: 'none'
      });
    } finally {
      // 确保状态被重置
      this.setData({ uploading: false });
    }
  },

  /**
   * 回退方案：逐个上传图片
   */
  async uploadImagesOneByOne(filePaths: string[]) {
    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];
      try {
        // 显示上传进度
        wx.displayLoading({
          title: `上传中 ${i + 1}/${filePaths.length}`
        });

        // 调用单文件上传接口
        const { isSuccess, data, message } = await uploadFeedbackImage(filePath);

        // 隐藏当前loading
        wx.hidePrevLoading();

        if (isSuccess && data) {
          // 处理返回的URL
          let imageUrl = '';
          if (Array.isArray(data) && data.length > 0) {
            imageUrl = data[0];
          } else if (data.url) {
            imageUrl = data.url;
          } else if (typeof data === 'string') {
            imageUrl = data;
          }

          if (imageUrl) {
            this.setData({
              uploadedImages: [...this.data.uploadedImages, filePath],
              uploadedImageUrls: [...this.data.uploadedImageUrls, imageUrl]
            });
          }
        } else {
          wx.displayToast({
            title: message || '图片上传失败',
            icon: 'none'
          });
          break;
        }
      } catch (error) {
        // 确保隐藏loading
        wx.hidePrevLoading();
        console.error('上传图片失败:', error);
        wx.displayToast({
          title: '图片上传失败',
          icon: 'none'
        });
        break;
      }
    }

    // 显示最终结果
    if (this.data.uploadedImages.length > 0) {
      wx.displayToast({
        title: `成功上传${this.data.uploadedImages.length}张图片`,
        icon: 'success'
      });
    }
  },

  /**
   * 删除图片
   */
  onDeleteImage(e: WechatMiniprogram.BaseEvent) {
    const index = parseInt(e.currentTarget.dataset.index);
    const images = [...this.data.uploadedImages];
    const imageUrls = [...this.data.uploadedImageUrls];

    images.splice(index, 1);
    imageUrls.splice(index, 1);

    this.setData({
      uploadedImages: images,
      uploadedImageUrls: imageUrls
    });
  },



  /**
   * 提交反馈
   */
  async onSubmit() {
    // 必填项校验
    if (!this.data.selectedType) {
      wx.displayToast({
        title: '请选择意见类型',
        icon: 'none'
      });
      return;
    }
    if (!this.data.feedbackContent.trim()) {
      wx.displayToast({
        title: '请填写详细意见',
        icon: 'none'
      });
      return;
    }
    if (!this.data.canSubmit) {
      return;
    }

    // 显示加载提示
    wx.displayLoading({
      title: '提交中...'
    });

    try {
      // 构造提交数据
      const feedbackData = {
        feedbackType: this.getFeedbackTypeNumber(this.data.selectedType),
        content: this.data.feedbackContent.trim(),
        images: this.data.uploadedImageUrls.length > 0 ? this.data.uploadedImageUrls : undefined
      };

      // 调用提交接口
      const { isSuccess, message } = await submitFeedback(feedbackData);

      wx.hidePrevLoading();

      if (isSuccess) {
        // 提交成功
        wx.showModal({
          title: '提交成功',
          showCancel: false,
          success: () => {
            // 返回上一页
            wx.navigateBack();
          }
        });
      } else {
        // 提交失败
        wx.showModal({
          title: '提交失败',
          content: message || '提交失败，请重试',
          showCancel: false
        });
      }
    } catch (error) {
      wx.hidePrevLoading();
      console.error('提交反馈失败:', error);
      wx.showModal({
        title: '提交失败',
        content: '网络异常，请稍后重试',
        showCancel: false
      });
    }
  },

  /**
   * 将反馈类型字符串转换为枚举值
   */
  getFeedbackTypeNumber(type: string): number {
    const typeMap: Record<string, EFeedbackType> = {
      'bug': EFeedbackType.BUG,              // 功能故障
      'suggestion': EFeedbackType.SUGGESTION, // 产品建议
      'order': EFeedbackType.ORDER,          // 订单问题
      'other': EFeedbackType.OTHER           // 其他反馈
    };
    return typeMap[type] || EFeedbackType.OTHER;
    }
  }
})
