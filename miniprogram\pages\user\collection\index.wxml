<!--pages/user/collection/index.wxml-->
<view class="collection-page {{isEditMode ? 'edit-mode' : ''}}">

  <!-- Tab切换 -->
  <view class="tab-container">
    <view class="tab-list">
      <view class="tab-item {{currentTab === item.value ? 'active' : ''}}" wx:for="{{tabs}}" wx:key="value" data-value="{{item.value}}" bindtap="onTabClick">
        <text class="tab-text">{{item.label}}</text>
        <view class="tab-indicator" wx:if="{{currentTab === item.value}}"></view>
      </view>
    </view>
  </view>

  <!-- 使用通用的下拉刷新上拉加载组件 -->
  <refreshLoadList
    refreshing="{{refreshing}}"
    loading-more="{{loadingMore}}"
    has-more="{{pageParams[currentTab].hasMore}}"
    is-empty="{{currentListLength === 0}}"
    loading="{{loading}}"
    disabled="{{isEditMode}}"
    empty-text="{{emptyText}}"
    empty-desc="{{emptyDesc}}"
    empty-icon="heart"
    top-offset="128rpx"
    bind:refresh="onRefresh"
  >
    <!-- 收藏内容展示组件 -->
    <collection-content
      current-list="{{currentList}}"
      is-edit-mode="{{isEditMode}}"
      current-swipe-index="{{currentSwipeIndex}}"
      circle-icon="{{circleIcon}}"
      circle-act-icon="{{circleActIcon}}"
      delete-icon="{{deleteIcon}}"
      bind:touchend="onTouchEnd"
      bind:selectitem="onSelectItem"
      bind:deleteitem="onDeleteItem"
      bind:itemclick="onItemClick"
    />
  </refreshLoadList>

  <!-- 悬浮按钮组件 -->
  <floating-button
    visible="{{currentListLength > 0}}"
    is-edit-mode="{{isEditMode}}"
    bind:togglemode="onToggleEditMode"
  />

  <!-- 编辑模式底部操作栏组件 -->
  <edit-actions
    visible="{{isEditMode}}"
    is-all-selected="{{isAllSelected}}"
    action-text="确定删除"
    circle-icon="{{circleIcon}}"
    circle-act-icon="{{circleActIcon}}"
    bind:toggleselectall="onToggleSelectAll"
    bind:batchaction="onBatchCancel"
  />
</view>