<!-- pages/hotel/hotelList/components/AreaDistance/AreaDistance.wxml -->
<view>
    <view class="area-distance">
        <!-- 第一栏：一级分类列表 -->
        <view class="category-list first-column">
            <view class="category-item {{currentCategoryIndex === index ? 'active' : ''}}" wx:for="{{areaDistance}}" wx:key="typeId" wx:for-index="index" data-index="{{index}}" bind:tap="onCategoryClick">
                <text class="category-name">{{item.name}}</text>
            </view>
        </view>
        <!-- 第二栏：二级分类列表（仅在三级结构时显示） -->
        <view class="sub-category-list second-column" wx:if="{{isThreeLevel}}">
            <view class="sub-category-item {{currentSubCategoryIndex === index ? 'active' : ''}}" wx:for="{{currentSubCategories}}" wx:key="typeId" wx:for-index="index" data-index="{{index}}" bind:tap="onSubCategoryClick">
                <text class="sub-category-name">{{item.name}}</text>
            </view>
        </view>
        <!-- 第三栏：最终选项列表 -->
        <view class="final-items-list third-column {{isThreeLevel ? 'three-level' : 'two-level'}}">
            <view class="final-item {{(!item.subFilters || item.subFilters.length === 0) ? ((selectedItem && selectedItem.typeId === item.typeId && selectedItem.filterId === item.filterId) ? 'selected' : 'selectable') : 'disabled'}}" wx:for="{{currentFinalItems}}" wx:key="filterId" data-item="{{item}}" bind:tap="onFinalItemClick">
                <view class="final-item-content">
                    <text class="final-item-name">{{item.name}}</text>
                    <!-- 显示用户选择百分比（如果有describe字段） -->
                    <text class="final-item-desc" wx:if="{{item.describe}}">{{item.describe}}</text>
                </view>
                <!-- 选中图标 -->
                <image class="check-icon" wx:if="{{selectedItem && selectedItem.typeId === item.typeId && selectedItem.filterId === item.filterId}}" src="{{imgBaseUrl}}/hoteList/Select.png" />
            </view>
        </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
        <button class="reset-btn" bindtap="onResetClick">重置</button>
        <button class="confirm-btn" bindtap="onConfirmClick">完成</button>
    </view>
</view>