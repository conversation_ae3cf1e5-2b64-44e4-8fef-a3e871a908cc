Component({

  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    value: {
      type: Number,
      value: undefined
    },
    list: {
      type: Array,
      value: [] as Activity.IPolicy[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    val: null as Nullable<number>
  },
  observers: {
    visible() {
      if (this.data.visible) {
        this.setData({
          val: this.data.value
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleClose() {
      this.triggerEvent('update-visible', { visible: false });
    },
    handleVisibleChange(e: WechatMiniprogram.CustomEvent<{ visible: boolean }>) {
      this.triggerEvent('update-visible', e.detail);
    },
    handlePolicyClick(e: WechatMiniprogram.TouchEvent<{}, {}, { value: number }>) {
      this.setData({
        val: e.currentTarget.dataset.value
      })
    },
    handleSubmit() {
      if (!this.data.val) return;

      this.triggerEvent('update-visible', { visible: false });
      this.triggerEvent('change', {
        value: this.data.val
      });
    },
  }
})