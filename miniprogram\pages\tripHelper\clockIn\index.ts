import { activityClockIn } from '../../../api/tripHelper/activity';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    star: 5,
    formModel: {
      recommendStar: 4,
      position: {
        name: '',
        longitude: '',
        latitude: ''
      },
      pics: [],
      comment: '',
      activityId: null,
      orderId: null
    } as ClockIn.IPayload
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: { activityId: string }) {
    this.setData({
      'formModel.activityId': options.activityId
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  /**
   * 自定义导航栏返回按钮点击事件
   */
  onNavigationBack() {
    wx.navigateBack();
  },
  handleStarClick(e: WechatMiniprogram.TouchEvent<{}, {}, { value: number }>) {
    this.setData({
      'formModel.recommendStar': e.currentTarget.dataset.value
    })
  },
  /** 图片上传回调 */
  handleImageChange(e: WechatMiniprogram.CustomEvent<{ value: string[] }>) {
    this.setData({
      'formModel.pics': e.detail.value ?? []
    });
  },
  handleCommentInput(e: WechatMiniprogram.CustomEvent<{value: string}>) {
    this.setData({
      'formModel.comment': e.detail.value
    })
  },
  /** 选择位置 */
  handleChoosePosition() {
    wx.chooseLocation({
      success: (res) => {
        if (res.errMsg === "chooseLocation:ok") {
          // example: {errMsg: "chooseLocation:ok", name: "香溢烟酒店(龙王塘路店)", address: "浙江省杭州市临平区龙王塘路心怡苑北侧约40米", latitude: 30.420338, longitude: 120.296968}
          const { name, latitude, longitude } = res;
          const formModel = this.data.formModel;
          Object.assign(formModel.position, {
            name,
            longitude,
            latitude
          });
          this.setData({
            formModel
          })
        }
      }
    })
  },
  /** 打卡提交 */
  async handleSubmit() {
    this.setData({
      loading: true
    });
    const res = await activityClockIn({...this.data.formModel});
    this.setData({
      loading: false
    })
    if (res.isSuccess) {
      wx.displayToast({
        title: '打卡成功',
        icon: 'none',
        duration: 3000
      })
      wx.navigateBack();
    }
  }
})