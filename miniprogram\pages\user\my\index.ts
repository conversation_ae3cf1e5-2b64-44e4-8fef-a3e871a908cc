// pages/user/my/index.ts
import { getUserInfo } from '../../../api/my';
import { getImageUrl } from '../../../utils/images';
import { hasToken } from '../../../utils/userInfo';
import { ComponentWithComputedStore } from '../../../core/componentWithStoreComputed';
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings';
import { behavior as computedBehavior } from 'miniprogram-computed';
import { store } from '../../../store/index';

ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior, computedBehavior],

  storeBindings: {
    store,
    fields: ['userInfo'],
    actions: ['setUserDetail', 'updateUserField']
  } as const,

  /**
   * 页面的初始数据
   */
  data: {
    // 是否已登录
    isLogin: false,
    // 箭头图标，用于列表项右侧
    arrowIcon: getImageUrl('arrow.png'),
    // 页面头部背景图
    headBgIcon: getImageUrl('user/user_my_bg.png'),
    // 用户默认头像，当接口返回的头像为空时使用
    avatarDefaultIcon: getImageUrl('user/avatar_default.png'),
    // 订单状态列表，用于快速访问不同状态的订单
    orderTabs: [{
      icon: getImageUrl('user/all_order.png'),
      title: '全部订单',
      id: 'all_order'
    }, {
      icon: getImageUrl('user/pending_payment.png'),
      title: '待支付',
      id: 'pending_payment'
    }, {
      icon: getImageUrl('user/not_traveling_yet.png'),
      title: '未出行',
      id: 'not_traveling_yet'
    }, {
      icon: getImageUrl('user/refund.png'),
      title: '退款/售后',
      id: 'refund'
    }],
    // 用户菜单列表，包含收藏、反馈、设置等功能入口
    userMenus: [{
      icon: getImageUrl('user/my_collection.png'),
      title: '我的收藏',
      path: '/pages/user/collection/index'
    }, {
      icon: getImageUrl('user/feedback.png'),
      title: '意见反馈',
      path: '/pages/user/feedback/index'
    }, {
      icon: getImageUrl('user/seting.png'),
      title: '设置',
      path: '/pages/user/seting/index'
    }]
  },

  computed: {
    // 显示的用户头像，如果store中没有则使用默认头像
    displayAvatar(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes; avatarDefaultIcon: string };
      return typedData.userInfo?.avatar || typedData.avatarDefaultIcon;
    },
    // 显示的用户昵称
    displayNickname(data: Record<string, unknown>) {
      const typedData = data as { userInfo?: My.IUserInfoRes };
      return typedData.userInfo?.nickname || '';
    }
  },

  lifetimes: {
    attached() {
      // 判断有没有token，没有token直接提示未登录
      if (!hasToken()) return;
      // 有token走接口验证逻辑
      this.setData({
        isLogin: true
      });
      this.init();
    }
  },

  methods: {
    /**
     * 初始化页面数据
     * 获取用户信息并更新到store中
     * 如果获取失败则直接返回
     */
    async init() {
      // 调用获取用户信息接口
      const { isSuccess, data } = await getUserInfo();
      // 接口调用失败，直接返回
      if (!isSuccess) return;
      // 更新到store中
      this.setUserDetail(data);
    },

    /**
     * 处理菜单项点击事件
     * @param e 事件对象
     */
    onMenuClick(e: WechatMiniprogram.BaseEvent) {
      const { path } = e.currentTarget.dataset;
      if (!path) return;

      // 现在不需要事件监听，直接跳转即可，因为使用store同步
      wx.navigateTo({
        url: path,
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.displayToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 处理订单Tab点击事件
     * @param e 事件对象
     */
    onOrderTabClick(e: WechatMiniprogram.BaseEvent) {
      const { id } = e.currentTarget.dataset;
      console.log('点击订单Tab:', id);

      // 直接传递用户中心的订单状态ID，让订单列表页面进行映射
      const url = `/pages/order/list/index?tab=${id}`;

      wx.navigateTo({
        url,
        fail: (err) => {
          console.error('订单页面跳转失败:', err);
          wx.displayToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  }
})