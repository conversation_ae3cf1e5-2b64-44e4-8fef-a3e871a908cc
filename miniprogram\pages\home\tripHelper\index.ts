// pages/home/<USER>/index.ts
import { getImageUrl } from '../../../utils/images';
import { LoginManager } from '../../../utils/loginManager';
import { hasToken } from '../../../utils/userInfo';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentTabIndex: 0, // 当前在首页，对应tabbar的第一个tab

    // 背景图片
    bgImage: getImageUrl('tripHelper/home/<USER>'),
    titleLeftImage: getImageUrl('tripHelper/home/<USER>'),
    titleRightImage: getImageUrl('tripHelper/home/<USER>'),

    // 产品计划列表
    productPlans: [
      {
        id: 'activity',
        icon: getImageUrl('tripHelper/home/<USER>'),
        titleImage: getImageUrl('tripHelper/home/<USER>'),
        titleImageWidth: '166rpx',
        time: '2025年7月',
        description: '想去哪里自己说了算，喊上小伙伴一起出发',
        images: [
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '用车，保险产品' },
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '活动报名' },
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '赚钱提现' }
        ]
      },
      {
        id: 'ranking',
        icon: getImageUrl('tripHelper/home/<USER>'),
        titleImage: getImageUrl('tripHelper/home/<USER>'),
        titleImageWidth: '124rpx',
        time: '2025年8月',
        description: '世界这么大，周末先安排上',
        images: [
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '热门旅游路线' },
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '人物榜单' }
        ]
      },
      {
        id: 'travel',
        icon: getImageUrl('tripHelper/home/<USER>'),
        titleImage: getImageUrl('tripHelper/home/<USER>'),
        titleImageWidth: '166rpx',
        time: '2025年9月',
        description: '记录旅行足迹，分享美好时光和攻略心得',
        images: [
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '城市' },
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '主题公园' },
          { url: getImageUrl('tripHelper/home/<USER>'), desc: '游学记' }
        ]
      }
    ],

    // 功能区域 - 上下结构
    featureRows: [
      // 第一行
      {
        rowId: 'row1',
        items: [
          { id: 1, icon: getImageUrl('tripHelper/home/<USER>'), text: '去哪玩' },
          { id: 2, icon: getImageUrl('tripHelper/home/<USER>'), text: '智能推荐' },
          { id: 3, icon: getImageUrl('tripHelper/home/<USER>'), text: '旅行账单' }
        ]
      },
      // 第二行
      {
        rowId: 'row2',
        items: [
          { id: 4, icon: getImageUrl('tripHelper/home/<USER>'), text: '找搭子' },
          { id: 5, icon: getImageUrl('tripHelper/home/<USER>'), text: '订机票' },
          { id: 6, icon: getImageUrl('tripHelper/home/<USER>'), text: '叫辆车' }
        ]
      },
      // 第三行
      {
        rowId: 'row3',
        items: [
          { id: 7, icon: getImageUrl('tripHelper/home/<USER>'), text: '世界日历' },
          { id: 8, icon: getImageUrl('tripHelper/home/<USER>'), text: '查天气' },
          { id: 9, icon: getImageUrl('tripHelper/home/<USER>'), text: '想要啥你说' }
        ]
      }
    ],

    // 建议弹框显示状态
    showSuggestPopup: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    console.log('行程助手页面加载', options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 用户信息弹框现在只在登录时显示，不需要在这里检查
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 点击"我来支一招"按钮
   */
  onSuggestClick() {
    // 检查是否已登录
    if(!hasToken()){
      LoginManager.navigateToLogin();
      return;
    }
    this.setData({
      showSuggestPopup: true
    });
  },

  /**
   * 关闭建议弹框
   */
  onSuggestPopupClose() {
    this.setData({
      showSuggestPopup: false
    });
  },

  /**
   * 建议提交成功
   */
  onSuggestPopupSuccess(e: any) {
    const { suggestion, images } = e.detail;
    console.log('建议提交成功:', suggestion, images);

    // 关闭弹框
    this.setData({
      showSuggestPopup: false
    });

    // TODO: 这里可以添加提交成功后的其他处理逻辑
  },

  /**
   * 点击产品计划项
   */
  onProductPlanClick(e: WechatMiniprogram.BaseEvent) {
    const { id } = e.currentTarget.dataset;
    console.log('点击产品计划:', id);
    // TODO: 根据不同的产品计划跳转到对应页面
  },

  /**
   * 点击九宫格功能项
   */
  onFeatureClick(e: WechatMiniprogram.BaseEvent) {
    const { id } = e.currentTarget.dataset;
    console.log('点击功能:', id);
    // TODO: 根据不同的功能跳转到对应页面
  },

  /**
   * 处理tabbar切换事件
   */
  onTabChange(e: WechatMiniprogram.CustomEvent<{ index: number; path: string }>) {
    const { index, path } = e.detail;
    console.log('Tab changed:', index, path);

    this.setData({
      currentTabIndex: index
    });
  },


})