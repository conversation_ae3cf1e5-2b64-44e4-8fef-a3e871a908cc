<view class="clock-ino">
	<navigationBar theme="transparent" title="精彩打卡" color="#000000" back-icon-color="#000000" title-size="32rpx" bind:back="onNavigationBack" />

	<scroll-view scroll-y="true" class="clock-content" enhanced="true" show-scrollbar="true">	
		<view class="evaluate-wrap">
			<view class="star-title {{formModel.recommendStar ? 'is-active' : ''}}">
				<text class="star-num">{{formModel.recommendStar}}</text>
				<text class="star-text">星</text>
			</view>
			<view class="evaluate-tips">推荐指数</view>
			<view class="star-list">
				<view class="star-item {{formModel.recommendStar > item ? 'is-active' : ''}}" wx:for="{{star}}" wx:key="item" bind:tap="handleStarClick" data-value="{{item + 1}}"></view>
			</view>
		</view>
		<view class="form-wrap">
			<view class="evaluate-form">
				<textarea value="{{formModel.comment}}" auto-height="{{true}}"  maxlength="{{500}}" bindinput="handleCommentInput" placeholder="说说您的这趟旅行：有趣的人、物、景点" placeholder-style="color: ccccce;font-weight: normal;" />
				<view class="text-count">{{formModel.comment.length === 0 ? '' : formModel.comment.length + '/500'}}</view>
				<view class="uploader-wrap">
					<Uploader
						value="{{formModel.pics}}"
						showTitle="{{false}}"
						max-count="{{9}}"
						bind:change="handleImageChange"
					/>
				</view>
			</view>
			<view class="place-wrap">
				<text>您的位置</text>
				<view class="place-value {{formModel.position.name ? '' : 'is-placeholder'}}" bind:tap="handleChoosePosition">
					{{formModel.position.name || '请选择'}}
				</view>
			</view>
		</view>
	</scroll-view>
	<view class="operate-wrap">
		<button class="submit-trigger" disabled="{{loading}}" catch:tap="handleSubmit">提交</button>
	</view>
</view>