declare namespace ActivityDetail {
  interface IDetailParams {
    id: string | number;
  }
  interface IDetailResponse {
    /** 活动ID（主键） */
    id: number;
    /** 发布者用户ID */
    userId: number;
    /** 参与人id */
    participantIds:string[];
    /** 集合地点 */
    assemblyPlaceDTOS:assemblyPlaceDetail[]
    /** 活动标题 */
    title: string;
    /** 活动详细描述 */
    description: string;
    /** 活动封面图片URL列表 */
    coverImages: string[];
    /** 活动类型 */
    activityType: string;
    /** 报名截止时间 */
    signUpDeadLineTime: string;
    /** 活动路线详细信息 */
    routeInfo?: RouteDetailResDTO;
    /** 活动开始时间 */
    startTime: string;
    /** 活动结束时间 */
    endTime:string;
    /** 活动日期文字描述 */
    dateDesc: string;
    /** 活动最大参与人数 */
    maxTicketCount: number;
    /** 活动最小成团人数 */
    minTicketCount: number;
    /** 当前已报名人数 */
    leftTicketCount: number;
    /** 价格选项配置 */
    perCost: PerCostDTO[];
    /** 取消政策信息 */
    policyDTO: PolicyDTO;
    /** 是否允许候补报名 */
    allowWaiting: number;
    /** 活动状态 */
    status: number;
    /** 微信群二维码图片URL */
    wxImage: string;
    /** 城市名 */
    cityName:string;
    /** 是否显示候补按钮 */
    showAlternateButton: boolean;
    /** 是否显示查看名单按钮 */
    showDetailBtn: boolean;
    /** 是否显示签到按钮 */
    showSignInBtn: boolean;
    /** 是否显示打卡按钮 */
    showCheckinBtn: boolean;
    /** 是否显示报名按钮 */
    showSignUpBtn: boolean;
    /** 活动管理按钮 */
    showActivityManageBtn:boolean;
    [key: string]: any;
  }
  interface ShareDataParams {
    headImg:string,
    price:number,
    title:string,
    startDate:string,
    cityName:string,
    wxImage:string,
    integerPrice:string,
    decimalPrice:string,
  }
  interface RouteDetailResDTO {
    /** 路线ID */
    routeId: number;
    /** 路线名称 */
    name: string;
    /** 渠道ID列表 */
    channel: number[];
    /** 项目ID列表 */
    project: number[];
    /** 路线描述 */
    description: string;
    /** 涉及城市代码列表 */
    cities: string[];
    /** 城市名称列表 */
    cityNames: string[];
    /** 渠道名称列表 */
    channelName: string[];
    /** 项目名称列表 */
    projectName: string[];
    /** 路线图片URL列表 */
    picUrls: string[];
    /** 每日行程信息 */
    journeyInfos: JourneyInfo[];
  }
  interface JourneyInfo {
    /** 第几天 */
    dayNo: number;
    /** 时间段描述 */
    time: string;
    /** 地点信息列表 */
    locations: LocationInfo[];
  }
  interface LocationInfo {
    /** 地点ID */
    locationId: number;
    /** 用户输入的地点名称 */
    locationNameInput: string;
    /** 关联的地点完整信息 */
    location: Location;
    /** 描述说明 */
    description: string;
  }
  interface Location{
    /** 地点唯一标识ID */
    locationId: number;
    /** 地点名称 */
    name: string;
    /** 详细地址信息 */
    address: string;
    /** 地点类型 */
    type: number;
    /**  地点类型名称  */
    typeName: string;
    /** 地点分类 */
    category: number;
    /** 地点分类名称 */
    categoryName: string;
    /** 游玩标签 */
    playTag: number[];
    /** 游玩标签名称数组 */
    playTagName: string[];
    /** 游玩内容*/
    playContent: number[];
    /** 游玩内容名称数组 */
    playContentName: string[];
    /** 其他标签ID数组 */
    otherTag: number[];
    /**其他标签名称数组 */
    otherTagName: string[];
    /** 联系方式 */
    contact: string;
    /** 地点详细描述 */
    description: string;
    /** 图片信息数组 */
    pics: PicInfo[]
    /** 图片URL数组（简化访问）*/
    picUrls: string[];
    /** 视频URL数组 */
    videos: string[];
  }
  interface PicInfo {
    /** 图片URL地址 */
    url: string;
    /** 是否为主图 */
    isMain: boolean;
  }
  interface PerCostDTO {
    /** 报名费用 */
    perCost: number;
    /** 参与者类型 */
    type: number;
  }
  interface PolicyDTO {
    /** 政策ID */
    id: number;
    /** 政策名称 */
    name: string;
    /** 政策描述 */
    desc: string;
    /** 政策细则列表 */
    policyDetails: PolicyDetailDTO[];
  }
  interface PolicyDetailDTO {
    /** 业务ID */
    id: number;
    /** 退款规则类型 */
    refundRule: number;
    /** 时间类型（1-天 2-小时） */
    timeType: number;
    /** 时间数量（如3天/2小时） */
    time: number;
    /** 是否允许退款（0-否 1-是） */
    canRefund: number;
    /** 退款比例（0-100表示百分比） */
    refundProportion: number;
  }
  interface assemblyPlaceDetail{
    startTime:string;
    placeName:string;
    cityName:string;
    longitude:number;
    latitude:number;
  }
}