import { request } from "../utils/request";
import { uploadImage } from "../utils/upload";
import { EImageUploadType } from "../enum/my";

/**
 * 获取附近/推荐酒店
 */
export const getNearbyHotels = (params: Home.GetRecommendHotelParams) => request<API.Common.PaginatingQueryRecord<Home.IRecommendHotel>>({
	url: '/hotel/home/<USER>/v1/queryHomeItems',
	params
})

/**
 * 上传首页反馈图片（单张）
 * 用于"我来支一招"功能的图片上传
 */
export const uploadFeedbackImage = (filePath: string) => {
  return uploadImage(filePath, {
    url: '/trip/activity/v1/upload/media',
    name: 'images',
    type: EImageUploadType.AVATAR
  });
}

/**
 * 批量上传反馈图片（真正的批量上传，一次接口调用上传多张图片）
 * 用于"我来支一招"功能的多图片上传
 */
export const uploadMultipleFeedbackImages = async (filePaths: string[]) => {
  const { uploadMultipleImagesAtOnce } = require('../utils/upload');
  return uploadMultipleImagesAtOnce(filePaths, {
    url: '/trip/activity/v1/upload/media',
    formData: {
      type: EImageUploadType.AVATAR
    }
  });
}

/**
 * 提交首页反馈
 * 用于"我来支一招"功能的反馈提交
 */
export const submitFeedback = (data: Home.ISubmitFeedbackReq) => request<Home.ISubmitFeedbackRes>({
  method: 'POST',
  showLoading: true,
  url: '/hotel/user/feedback/submit',
  data
})