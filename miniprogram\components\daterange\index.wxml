<!--components/daterange/index.wxml-->
<t-popup visible="{{visible}}" placement="bottom" bind:visible-change="handleVisibleChange">
  <view class="daterange-wrap">
    <view class="daterange-header is-{{step}}">
      <view class="left-trigger" catch:tap="onLeftClick"></view>
      <text class="daterange-title">{{step === 'start' ? '开始时间' : '结束时间'}}</text>
      <view class="right-trigger" catch:tap="onRightClick"></view>
    </view>
    <t-date-time-picker
      usePopup="{{false}}"
      visible="{{true}}"
      mode="{{mode}}"
      value="{{step === 'start' ? startDate : endDate}}"
      start="{{step === 'start' ? minDate : startDate}}"
      end="{{maxDate}}"
      bind:pick="onDatePickChange"
      format="{{format}}"
      class="daterange-picker"
      header="{{false}}"
      show-week="{{showWeek}}"
    />
  </view>
</t-popup>