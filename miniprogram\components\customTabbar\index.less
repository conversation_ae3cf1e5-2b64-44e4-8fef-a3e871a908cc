/* components/customTabbar/index.wxss */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 164rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 2rpx solid #F7F7F7;
  z-index: 1000;

  // 正常的tab项布局
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    position: relative;

    &.active {
      .tab-text {
        color: #0198FF;
      }
    }
  }

  // 脱离文档流的中间圆形按钮
  .floating-center-btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -24rpx; // 向上偏移，使按钮超出tabbar
    width: 112rpx;
    height: 112rpx;
    border-radius: 56rpx;
    background: #0198FF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .center-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
  
  .tab-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 8rpx;

    // 中间位置的占位图标，透明不可见，但占据空间
    &.placeholder-icon {
      background: transparent;
    }
  }

  .tab-text {
    font-size: 20rpx;
    color: #66666E;
    line-height: 28rpx;
    text-align: center;
  }
}
