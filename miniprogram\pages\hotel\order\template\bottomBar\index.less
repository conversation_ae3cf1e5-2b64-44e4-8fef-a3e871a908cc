.bottom-box{
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 100;
  background-color: #FFFFFF;
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容iOS >= 11.2 */

  .bottom-bar{
    height: 128rpx;
    padding: 16rpx 32rpx 0 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left{
      display: flex;
      align-items: flex-end;
      text{
        color: #000000;
        line-height: 1;
      }
      .t1{
        font-weight: 500;
        font-size: 28rpx;
      }
      .t2{
        font-size: 20rpx;
        margin-left: 20rpx;
      }
      .t3{
        font-weight: bold;
        font-size: 48rpx;
        margin-bottom: -4rpx;
      }
    }
    .right{
      display: flex;
      align-items: center;
      text{
        font-size: 24rpx;
        color: #66666E;
      }
      image{
        width: 24rpx;
        height: 24rpx;
        margin-left: 8rpx;
        margin-right: 16rpx;
      }
      .btm{
        width: 226rpx;
        height: 96rpx;
        background: #568DED;
        border-radius: 120rpx 120rpx 120rpx 120rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 96rpx;
      }
    }
  }
}