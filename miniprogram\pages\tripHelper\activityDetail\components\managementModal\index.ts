Component({
  properties: {
    // 控制弹窗开关
    visible: {
      type: Boolean,
      value: false
    },
    setList: {
      type:Array,
      value:[] as Array<{ desc: string; type: string }>
    }
  },
  data:{},
  methods: {
    stopPropagation(){},
    closePopClick(){
      this.triggerEvent('close', { visible:false, type: 'management'})
    },
    btmEvent(event:WechatMiniprogram.BaseEvent ){
      const { type } = event.currentTarget.dataset
      this.triggerEvent('btmClick', {type})
    }
  }
})