Component({
  properties: {
    // 是否是透明效果
    isOpacity: {
      type: Boolean,
      value: false
    }
  },

  data: {
    statusBarHeight: 0,  // 刘海屏高度
    navBarHeight: 0,     // 导航栏高度
  },

  lifetimes: {
    attached() {
      // 获取状态栏高度和胶囊按钮位置信息
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      // 计算导航栏高度 = 胶囊按钮顶部距离 + 胶囊按钮高度 + 顶部间距
      const navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2;

      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        navBarHeight: navBarHeight
      });
    }
  },

  methods: {
    // 返回
    handleBack() {
      wx.navigateBack({
        delta: 1
      });
    },
  }
}) 